<?php


class loyalv
{
    function __construct()
    {
        $code = System::$data['url']['query'][3];
        if (!method_exists($this, $code)) {
            $this->index();
        }
        $this->$code();
    }

    /**
     * <PERSON><PERSON> thực hiện lệnh đổi điểm lấy quà
     * @return [type] [description]
     * @var $type 1: quà voucher
     */
	function redeem()
	{
        System::apiError('Token không hợp lệ.');
		$id = Url::getParamInt('id', 0);
		$tokenKey = Url::getParam('token', "");
		$qty = Url::getParamInt('qty', 1);
		$form = Url::getParam('form', "");
		$hash = Url::getParam('hash', "");
		$hash = StringLib::clean_value($hash);
		$type = Url::getParam('type', '');
		# Kiểm tra Token
		if(!in_array($type,[1,2])){
			System::apiError('<PERSON>h<PERSON><PERSON> bại, <PERSON><PERSON><PERSON><PERSON> đúng loại.');
		}
		if($tokenKey != System::decrypt($hash)){
			System::apiError('Token không hợp lệ.');
		}
		if($qty > 10) $qty = 10;
		
		if($id <=0){
			System::apiError('Quà không tồn tại.');
		}
		if(isset($_SESSION['click_redeem']) && TIME_NOW - $_SESSION['click_redeem'] < 6){
			System::apiError("Bạn đã đổi quà quá nhanh, vui lòng chờ trong giây lát",['showPopup'=> 2]);
		}
		$_SESSION['click_redeem'] = TIME_NOW;
		$tokens = LoyaltyTomo::getToken($tokenKey);
		if (!empty($tokens)) {
			$isLastToken = Loyalty::getLastToken($tokens);
			$campaign = Loyalty::getCampaign($tokens);
			if ($isLastToken == false || ($tokens['expired'] < TIME_NOW && $campaign['hasApiExpired'] == 2)) {
				System::apiError('Phiên làm việc đã hết, vui lòng đăng nhập lại.');
			}
			$count_gift = Cart::checkGiftSet($campaign['giftset_id'],$id);
			if($count_gift  <=0){
				System::apiError('Một trong số các món quà bạn chọn không được phép mua. Vui lòng chọn quà khác!');
			}
			$gift_detail = DB::fetch("SELECT id,price,gift_id,justGetOrder FROM " . T_GIFT_DETAIL . " WHERE id=" . $id);
			if ($gift_detail) {
				#Khong cho tao don hang khi het diem o token nay
				if($tokens['point'] < ($gift_detail['price']/$campaign['exchange_rate'])*$qty){
					System::apiError('Tài khoản của bạn không đủ để đổi quà.');
				}
				#Tạo đơn hàng
				$site_user_id = $tokens['site_user_id'];
				$site = Card::getAppById($tokens['site_id']);
				$customer_id = 0;
				if ($site) {
					$customer_id = $site['customer_id'];
				}
				#Lay thong tin don hang
				$ttmessage = '';
				$ttemail = '';
				$ttfullname = $site_user_id;
				
				$customer = Customer::get($customer_id);
				
				$emailnguoigui = isset($customer['email'])?$customer['email']:'';
				$phonenguoigui = isset($customer['phone'])?$customer['phone']:'';
				$nguoigui = ($customer['first_name']?$customer['first_name']:'') . ' ' . (isset($customer['last_name'])?$customer['last_name']:'');
				
				$msg = '';
				
				$cart = array(
					'from' => array('name' => $nguoigui, 'phone' => $phonenguoigui, 'email' => $emailnguoigui, 'message' => $ttmessage, 'thiep_id' => 0),
					'to' => array('name' => $ttfullname, 'phone' => '', 'email' => $ttemail),
					'items' => [['id' => $gift_detail['gift_id'], 'priceId' => $gift_detail['id'], 'quantity' => $qty]],
					'type' => 4,
					'coupon' => '',
					'send_time' => '',
					'address' => '',
					'site_id' => $tokens['site_id'],
					'site_user_id' => $site_user_id,
					'justGetOrder' => $gift_detail["justGetOrder"],
					'payment_type' => PAY_CARD
				);
				$arrCartFinal = Cart::createOrder($cart, $msg);
				if ($msg != '') {
					System::apiError($msg);
				} else {
					if (!empty($arrCartFinal)) {
						//
						$receiver = true;
						if($type == 2){
							$receiver = $this->receiver($form,$arrCartFinal);
						}
						if($receiver == false) System::apiError("Hệ thống không lưu được thông tin đơn hàng. Vui lòng thử lại sau");
						
						$point = $gift_detail['price'] / $campaign['exchange_rate'];
						$price = $gift_detail['price'];
					 	$point = Loyalty::point_format($point);
						
						$newRowOrderLoyal = array(
							'campaign_id' => $campaign['id'],
							'site_user_id' => $site_user_id,
							'point' => $point * $qty,
							'exchange_rate' => $campaign['exchange_rate'],
							'money' => $price * $qty,
							'cart_id' => $arrCartFinal['id'],
							'pay_status' => 1,
							'created' => TIME_NOW
						);
						$id_order_loyal = DB::insert(T_LOYAL_ORDER,$newRowOrderLoyal);
						if (Site::check_money($site['id'], $site['access_key'])) {
							$tokens['exchange_rate'] = $campaign['exchange_rate'];
							$options = [
								"qty"=>$qty,
								'point' => $point * $qty,
								'gift_id' => $id,
								'loyal_order_id' => $id_order_loyal,
								'cart_id' => $arrCartFinal['id'],
								"point_url" => $campaign['point_url']
							];
							$result = LoyaltyTomo::SendRedeem($tokens,$arrCartFinal,$options);
							if($result['done'] == 1) {
								System::apiSuccess($result);
							}else{
								System::apiError($result['msg'], false);
							}
						} else {
							System::apiError('Hệ thống không đổi được quà. Vui lòng thử lại(2).', false);
						}
						
					} else {
						System::apiError('Hệ thống không đổi được quà. Vui lòng thử lại(3).');
					}
				}
			}
		}
		
		System::apiError('Không tìm thấy dữ liệu.');
		
	}
	//lưu thông tin đổi quà vật lý
	function receiver($form,$carts)
	{
		if(empty($form) || empty($carts)) return false;
		$phone = $form['phone'];
		$email = $form['email'];
		$city = $form['city'];
		$district_id = $form['district_id'];
		$address = $form['address'];
		$full_name = $form['phone'];
		$ward = $form['district_id'];
		$note = $form['note'];
		
		
		if ($phone == "" || !FunctionLib::is_phone( $phone )) {
			return false;
		}
		if ($email == "" || !FunctionLib::is_email( $email )) {
			return false;
		}
		if ($city == 0 || $district_id == 0 || $address == "") {
			return false;
		}
		if ($full_name == "") {
			return false;
		}
		
		$checkReceiver = DB::fetch( "SELECT id FROM " . T_GIFT_RECEIVER . " WHERE phone='" . $phone . "'" );
		$cartDetail = Loyalty::getCartDetailFromCart($carts['id']);
		foreach ($cartDetail as $cart) {
			
			if (!$checkReceiver) {
				$row = array(
					'city_id' => $city,
					'district_id' => $district_id,
					'address' => $address,
					'fullname' => $full_name,
					'phone' => $phone,
					'email' => $email,
					'ward' => $ward,
					'created' => TIME_NOW,
				);
				$checkReceiver['id'] = DB::insert( T_GIFT_RECEIVER, $row );
			}
			DB::update( T_CART_DETAIL,
				[
					'delivery_note' => $note,
					'delivery_required' => 2,
					'client_time' => TIME_NOW,
					'delivery_city' => $city,
					'delivery_district' => $district_id,
					'delivery_ward' => $ward,
					'delivery_address' => $address,
				
				]
				,
				"id={$cart['id']}"
			);
			DB::update( T_CART,
				[
					'delivery_note' => $note,
					'email' => $email,
					'phone' => $phone,
					'address' => $address,
					'fullname' => $full_name,
					'receiver_id' => $checkReceiver['id']
				]
				,
				"id={$cart['cart_id']}"
			);
		}
		return true;
	}
	
	function index()
    {
        System::apiSuccess('Gọi thành công Ajax Index');
    }


    /**
     * Mẫu để đối tác tạo API trừ điểm khách hàng
     * @return [type] [description]
     */
    function changePoint(){
        define('DEMO_APP_ID', 39); // Do Urbox cung cấp
        define('DEMO_APP_SECRET', '1778ab610e6ec39594262b9ecb7b55ba'); // Do Urbox cung cấp
        #define('DEMO_CUSTOMER_ID', 'TESTNINH'); // Do Urbox cung cấp
		$params = array(
			'app_id' => Url::getParamInt('app_id',''),
			'app_secret' => Url::getParam('app_secret',''),
			'campaign_id' =>Url::getParamInt('campaign_id',''),
			'customer_id' => Url::getParamInt('customer_id',''),
			'cart_id' => Url::getParamInt('cart_id',''),
			'point' => Url::getParam('point','')
		);

        $out = array(
            'done' => 0, // Mặc định: ko thành công
            'status_code' => 0, // Mặc định: Không có trạng thái nào
            'data' => array(
                'cart_id' => $params['cart_id'], // Trả lại cart_id để đảm bảo là urbox và đối tác đã liên thông chính xác thông tin đơn hàng
                'transaction_id' => 0, // Mặc định, Không có giao dịch trừ điểm
            )
        );
/*
        # Kiểm tra xem app_id và app_secret có hợp lệ ko
        if(empty($params['app_id']) || $params['app_id'] != DEMO_APP_ID || $params['app_secret'] != DEMO_APP_SECRET){
            $out['status_code'] = 14; // Sai kiểu dữ liệu

            echo json_encode($out); die();
        }

        # Nếu Customer ID ko hợp lệ
        if($params['customer_id'] == ''){
            $out['status_code'] = 13; // Sai Customer ID

            echo json_encode($out); die();
        }*/

        # Thực hiện trừ điểm và trả kết quả thành công
        $out['done'] = 1;//rand(0, 1);
        if($out['done'] == 0){
            $out['status_code'] = 11; // Không đủ điểm
            echo json_encode($out); die();
        }

        $out['status_code'] = 2;
        $out['data']['transaction_id'] = rand(1, 1000);

        echo json_encode($out); die();
    }

    
    function listGifts(){

        $tokenKey = CookieLib::get(md5('TOKEN'), '');
		$tokenKey = StringLib::clean_value($tokenKey);
        if (!empty($tokenKey)) {

            $token = DB::fetch("SELECT * FROM " . T_LOYAL_TOKEN . " WHERE token = '" . $tokenKey . "'");

            if ($token) {

                $campain = DB::fetch("SELECT * FROM ".T_LOYAL_CAMPAIGN." WHERE id=".$token['campaign_id']);
                #Gift
                $per_page = Url::getParamInt('per_page', 5);
                $city = Url::getParamInt('city', 0);
                $cat_id = Url::getParamInt('cat_id', 0);
                $page_no = Url::getParamInt('page_no', 1);
                $id_gift_set = $campain['giftset_id'];
                $param = array();
                if ($cat_id > 0) {
                    $param[] = 'cat_id='.$cat_id;
                }
                $param[] = 'code_quantity>0';
                $param[] = 'status=1';

                $id_gift_detail = array();

                #Lay them phan chi ban theo GIFT_SET
                $getSiteGift = DB::query("SELECT * FROM ".T_GIFT_SET." WHERE gift_id=".$id_gift_set);

                if($getSiteGift){
                    while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)){
                        $id_gift_detail[$row['gift_detail_id']] = $row['gift_detail_id'];
                    }
                    if(!empty($id_gift_detail)){
                        $param[] = " id IN (".implode(',',$id_gift_detail).")";
                    }
                }
                #loc theo LOCATION
                if ($city > 0) {
                    $giftId2 = array();
                    $re = DB::query("SELECT * FROM " . T_GIFT_CITY . " WHERE (city_id=" . $city . " OR city_id=0) AND gift_detail_id IN (" . implode(',',$id_gift_detail) . ")");
                    if ($re) {
                        while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                            $giftId2[$row['gift_detail_id']] = $row['gift_detail_id'];
                        }
                        if (!empty($giftId2)) {
                            $param[] = " id IN (" . implode($giftId2, ',') . ")";
                        } else {
                            $param[] = " id = 0 ";
                        }
                    }
                }
                $condition =  FunctionLib::addCondition($param, true);

                $sql = "SELECT * FROM ".T_GIFT_DETAIL.$condition." ORDER BY id DESC";
                $re = Pagging::query($sql, $per_page,$page_no);
                $data = array();
                if($re && Pagging::$totalPage>1){
                    $arr_brand_id = array();
                    $arr_gift_id = array();
                    while ($row = $re->fetch(PDO::FETCH_ASSOC)){
                        $images = MediaUrl::fromImageTitle($row['avatar']);
                        $temp = array(
                            'id'=>$row['id'],
                            'brand_id'=>$row['brand_id'],
                            'cat_id'=>$row['cat_id'],
                            'gift_id'=>$row['gift_id'],
                            'title'=>$row['title'],
                            'price'=>$row['price'],
                            'price_point'=>$row['price']/$campain['exchange_rate'],
                            'view'=>$row['view'],
                            'link'=>'/loyal/'.$token['token'].'/gift/'.$row['id'],
                            'image'=>isset($images[640])?$images[640]:''
                        );
                        $arr_brand_id[$row['brand_id']] = $row['brand_id'];
                        $arr_gift_id[$row['gift_id']] = $row['gift_id'];
                        $data[$row['id']] = $temp;
                    }

                    /*if(!empty($arr_gift_id)){
                        $re2 = DB::query("SELECT * FROM ".T_GIFT." WHERE id IN(".implode(',',$arr_gift_id).")");
                        if($re2){
                            while ($row2 = $re2->fetch(PDO::FETCH_ASSOC)){
                                foreach ($data as &$value){
                                    if($value['gift_id']==$row2['id']){
                                        $value['title'] = $row2['name'];
                                    }
                                }
                            }
                        }
                    }*/
                    System::apiSuccess(array('gifts'=>array_values($data),'campaign'=>array('point_name'=>$campain['point_name']),'totalPage'=>Pagging::$totalPage));

                }
            }
        }
        System::apiError('No data found');
    }
    /*white label v2*/
    function getProvide(){
        $is_city = Url::getParamInt('is_city', 0);
        if($is_city == "0") $is_city = false;
        else $is_city = true;
        $data = Loyalty::allProvide($is_city);
        System::apiSuccess($data);
    }
    function listGift()
    {
        $token = Url::getParam('token', '');
        if($token == "")System::apiError('No data found');
        $tokens = Loyalty::getToken($token);
        $campaign = Loyalty::getCampaign($tokens);
        if ($tokens) {
            #Gift
            $cat_id = Url::getParamInt('cat_id', 0);
            $per_page = Url::getParamInt('per_page', 10);
            $page_num = Url::getParamInt('page_num', 1);
            $pages = [
                'per_page' => $per_page,
                'page_num' => $page_num,
            ];
            $params = [
                "keyword" => Url::getParam('keyword', ''),
                'isPhysical'=> Url::getParamInt('type', 1),
                'orderBy'=> Url::getParam('orderBy', ''),
                'priceRange'=> Url::getParam('priceRange', ''),
                'length' => Url::getParamInt('length', 0),
            ];
            $data = Loyalty::getListGifts($tokens,$cat_id,$pages,$params);
            System::apiSuccess(array('gifts' => array_values($data),'campaign'=>array('point_name'=>$campaign['point_name']),));
        }
        System::apiError('No data found');
    }
    function getDistrict(){
        $token = Url::getParam('token', '');
        if($token == "")System::apiError('No data found');
        $city_id = Url::getParamInt('city_id', 0);
        $data = Loyalty::allDistrict($city_id);
        System::apiSuccess(array('district' => array_values($data)));
    }
}