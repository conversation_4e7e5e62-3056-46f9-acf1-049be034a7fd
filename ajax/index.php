<?php


class ajax_index
{
    function __construct()
    {
        $code = System::$data['url']['query'][2];
        if (!method_exists($this, $code)) {
            $this->index();
        }
        $this->$code();
    }


    function code()
    {
        $id = Url::getParamInt('id', 0);
        $code = Url::getParam('code', '');
        $token = Url::getParam('token', '');
        $isValidated = CaptchaService::verify($token);
        if ($isValidated && $id > 0 && $code != '') {
            LogApp::checkRequest();
            $gift = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $id);
            if ($gift) {
                $gift_code = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE status=1 AND brand_id=" . $gift['brand_id'] . " AND code='" . System::encrypt($code) . "'");
                if (empty($gift_code) || $gift_code['cart_detail_id'] == 0) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã không tồn tại - Code does not exist');
                }
                if ($gift_code['gift_detail_id'] != $id) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã không tồn tại - Code does not exist');
                }

                $return = self::checkCode($gift['brand_id'], $code);
                if ($return['done'] == 1) {
                    LogApp::resetFailRequest();
                    CookieLib::set(md5('code'), $code, TIME_NOW + 24 * 60 * 60);
                    System::apiSuccess(array('link' => WEB_ROOT . '/grab.reward/gift/receiver/' . $id));
                } else {
                    LogApp::saveFailRequest();
                    System::apiError(isset($return['msg_text']) ? $return['msg_text'] : 'Có lỗi xảy ra');
                }
            }

        }

        System::apiError('Không tìm thấy dữ liệu - No data found.');

    }

    function topUpGrab()
    {
        $id = Url::getParamInt('id', 0);
        $code = Url::getParam('code', '');
        $appName = Url::getParam('appName', '');
        $token = Url::getParam('token', '');
        $isValidated = CaptchaService::verify($token);
        if ($id > 0 && $code != '' && $isValidated) {
            LogApp::checkRequest();
            $gift = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $id);
            if ($gift) {
                $gift_code = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE status=1 AND brand_id=" . $gift['brand_id'] . " AND code='" . System::encrypt($code) . "'");
                if (empty($gift_code) || $gift_code['cart_detail_id'] == 0) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã không tồn tại - Code does not exist');
                }
                if ($gift_code['gift_detail_id'] != $id) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã không tồn tại - Code does not exist');
                }
                if ($gift_code['brand_id'] != 692) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã code không hợp lệ - Invalid Code');
                }
                $return = self::checkCode($gift['brand_id'], $code);
                if ($return['done'] == 1) {
                    LogApp::resetFailRequest();
                    System::apiSuccess(['grab_code' => $code, 'grab_point' => $gift['valuex']]);
                } else {
                    LogApp::saveFailRequest();
                    System::apiError(isset($return['msg_text']) ? $return['msg_text'] : 'Có lỗi xảy ra');
                }
            }

        }

        System::apiError('Không tìm thấy dữ liệu - No data found.');

    }

    function gift()
    {
        $id = Url::getParamInt('id', 0);
        $code = Url::getParam('code', '');
        LogApp::checkRequest();
        if ($id > 0 && $code != '') {
            $gift = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE type=12 and id=" . $id);
            if ($gift) {
                $gift_code = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE code='" . System::encrypt($code) . "'");
                if (empty($gift_code) || $gift_code['cart_detail_id'] == 0) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã không tồn tại - Code does not exist');
                }
                if ($gift_code['gift_detail_id'] != $id) {
                    LogApp::saveFailRequest();
                    System::apiError('*Mã không tồn tại - Code does not exist');
                }
                $return = self::checkCode($gift['brand_id'], $code);
                if ($return['done'] == 1) {
                    LogApp::resetFailRequest();
                    $cartDetail = DB::fetch("SELECT receive_code,id FROM " . T_CART_DETAIL . " WHERE id=" . $gift_code['cart_detail_id']);
                    System::apiSuccess(array('link' => '/receiver/' . System::decrypt($cartDetail['receive_code'])));
                } else {
                    LogApp::saveFailRequest();
                    System::apiError(isset($return['msg_text']) ? $return['msg_text'] : 'Có lỗi xảy ra');
                }
            }

        }

        System::apiError('Không tìm thấy dữ liệu - No data found.');

    }

    function checkCode($brand_id, $code)
    {
        $arrParam = array(
            "access_urbox" => ACCESS_URBOX,
            "agent_site" => AGENT_SITE,
            "brand_id" => $brand_id,
            "coupon" => $code
        );
        $curl = new CURL();
        $returned_data = $curl->post(LINK_API_URBOX . '2.0/coupon/check', $arrParam);
        $data = json_decode($returned_data, true);
        return $data;
    }

    function activeCode($brand_id, $code)
    {
        $arrParam = array(
            "access_urbox" => ACCESS_URBOX,
            "agent_site" => AGENT_SITE,
            "brand_id" => $brand_id,
            "coupon" => $code
        );
        $curl = new CURL();
        $returned_data = $curl->post(LINK_API_URBOX . '2.0/coupon/active', $arrParam);
        $data = json_decode($returned_data, true);
        return $data;
    }

    function receiver()
    {
        $id = Url::getParamInt('id', 0);
        $phone = Url::getParam('phone', '');
        $email = Url::getParam('email', '');
        $city_id = Url::getParamInt('city', 0);
        $note = Url::getParam('note', '');
        $address = Url::getParam('address', '');
        $ward_id = Url::getParamInt('ward', 0);
        $district_id = Url::getParamInt('district_id', 0);
        $fullname = Url::getParam('fullname', '');
        $appName = Url::getParam('appName', '');
        $token = Url::getParam('token', '');
        $isValidated = CaptchaService::verify($token);
        $code = CookieLib::get(md5('code'), '');
        LogApp::checkRequest();
        if (!$isValidated || $id == 0 || empty($code)) {
            System::apiError('Không tìm thấy dữ liệu - No data found.');
        }
        if ($phone == "" || !FunctionLib::is_phone($phone)) {
            System::apiError('Số điện thoại không hợp lệ - Invalid phone number.');
        }
        if ($email == "" || !FunctionLib::is_email($email)) {
            System::apiError('Email không hợp lệ - Invalid email.');
        }
        if ($city_id == 0 || $district_id == 0 || $address == "") {
            System::apiError('Không tìm thấy thông tin địa chỉ - No address information found.');
        }
        $gift = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $id);
        if ($gift) {
            $gift_code = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE status=1 AND brand_id=" . $gift['brand_id'] . " AND code='" . System::encrypt($code) . "'");
            if (empty($gift_code) || $gift_code['cart_detail_id'] == 0) {
                LogApp::saveFailRequest();
                System::apiError('Mã không hợp lệ - Invalid Code.');
            }
            if ($gift_code['gift_detail_id'] != $id) {
                LogApp::saveFailRequest();
                System::apiError('Không tìm thấy dữ liệu - No data found.');
            }
            LogApp::resetFailRequest();
            $return = self::checkCode($gift['brand_id'], $code);
            if ($return['done'] == 1) {
                $active = self::activeCode($gift['brand_id'], $code);
                if ($active['done'] == 1) {
                    $cart_detail = DB::fetch("SELECT * FROM " . T_CART_DETAIL . " WHERE id=" . $gift_code['cart_detail_id']);
                    if ($cart_detail) {
                        $row = array(
                            'city_id' => $city_id,
                            'district_id' => $district_id,
                            'ward' => $ward_id,
                            'address' => $address,
                            'fullname' => $fullname,
                            'phone' => $phone,
                            'email' => $email
                        );
                        $checkReceiver = ReceiverCore::add($row);
                        if ($checkReceiver) {
                            if ($row['email'] != '' && $row['email'] != $checkReceiver['email']) {
                                DB::update(T_GIFT_RECEIVER, array('email' => $row['email']), 'id=' . $checkReceiver['id']);
                            }
                            if ($row['phone'] != '' && $row['phone'] != $checkReceiver['phone']) {
                                DB::update(T_GIFT_RECEIVER, array('phone' => $row['phone']), 'id=' . $checkReceiver['id']);
                            }
                        }
                        $update = array(
                            'receiver_id' => $checkReceiver['id'],
                            'delivery_note' => $note,
                            'client_time' => TIME_NOW,
                            'delivery_city' => $city_id,
                            'delivery_district' => $district_id,
                            'delivery_ward' => $ward_id,
                            'delivery_address' => $address
                        );
                        if ($gift['justGetOrder'] == IS_NO) {
                            $update['delivery_required'] = IS_YES;
                            #$update['app_id'] = APP_GRAPREWARD;#Tam thoi co GRAP va qua nhan ho
                        }
                        DB::update(T_CART_DETAIL, $update, 'id=' . $cart_detail['id']);

                        DB::insert(T_CART_DETAIL_INFO, [
                            'cart_detail_id' => $cart_detail['id'],
                            'fullname' => $fullname,
                            'phone' => $phone,
                            'email' => $email,
                        ]);
                        DB::insert(T_ADDRESS,[
                            'city_id' => $city_id,
                            'district_id' => $district_id,
                            'ward_id' => !empty($ward_id) && $ward_id > 0 ? $ward_id : 0,
                            'number' => $address,
                            'fullname' => $fullname,
                            'card_id' => $cart_detail['card_id'],
                            'receiver_id' => $checkReceiver['id'],
                            'phone' => System::encrypt($phone),
                            'isDefault' => 1,
                            'status' => 2,
                            'email' => System::encrypt($email)
                        ]);
                    }
                    CookieLib::set(md5('ACTIVESUS' . $id . $code), 1, TIME_NOW + 24 * 60 * 60);
                    if($cart_detail['app_id'] == APP_GRAPREWARD) {
                        $content_title = "Thông tin khách đặt Grab";
                        $sub_title = "Khách đăng ký thông tin Grab thành công";
                        $email_content = '<EMAIL>';
                    }else{
                        $content_title = "Thông tin khách đặt HSBC";
                        $email_content = '<EMAIL>';
                        $sub_title = "Khách đăng ký thông tin HSBC thành công";
                    }

                        $content = '<h3>'.$content_title.'</h3><br/><br/><table cellpadding="1" cellspacing="1" width="100%">
                        <tr><td>Họ tên:</td><td>' . $fullname . '</td>
                        <tr><td>Số điện thoại:</td><td>' . $phone . '</td>
                        <tr><td>Email:</td><td>' . $email . '</td>
                        <tr><td>Đơn hàng:</td><td>' . $cart_detail['cart_id'] . '</td>
                        <tr><td>Đơn hàng con:</td><td>' . $cart_detail['id'] . '</td>
                        <tr><td>Tỉnh thành:</td><td>' . DB::fetch("SELECT * FROM ___province WHERE id=" . $city_id, 'title') . '</td>
                        <tr><td>Quận huyện:</td><td>' . DB::fetch("SELECT * FROM " . T_DISTRICT . " WHERE id=" . $district_id, 'title') . '</td>
                        <tr><td>Phường xã:</td><td>' . DB::fetch("SELECT * FROM " . T_WARD . " WHERE id=" . $ward_id, 'title') . '</td>
                        <tr><td>Địa chỉ:</td><td>' . $address . '</td>
                        <tr><td>Ghi chú thêm:</td><td>' . $note . '</td>
                        </tr></table>';
                        $rowInsertEmail = array(
                            'subject' => $sub_title,
                            'email' => $email_content,
                            'content' => $content,
                            'time_send' => 0,
                            'created' => TIME_NOW,
                        );
                        DB::insert(T_EMAIL_SEND, $rowInsertEmail);

                    return System::apiSuccess(array('link' => WEB_ROOT . '/grab.reward/gift/finish/' . $id));
                } else {
                    System::apiError(isset($active['msg_text']) ? $active['msg_text'] : '');
                }
            } else {
                System::apiError(isset($return['msg_text']) ? $return['msg_text'] : '');
            }
        }
        System::apiError('Không tìm thấy dữ liệu - No data found.');
    }

    function district()
    {
        $idCity = Url::getParamInt('idCity', 0);
        $lang = Url::getParam('lang', 'vi');
        $prefix = Url::getParamInt('prefix', 0);
        if ($idCity > 0) {
            $district = array();
            $re = DB::query("SELECT * FROM " . T_DISTRICT . " WHERE city_id=" . $idCity . "  and status= 2 order by title asc");
            if ($re) {
                while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                    if ($lang == 'vi') {
                        if ($prefix != 0) {
                            $title = $row['prefix'] . " " . $row['title'];
                        } else {
                            $title = $row['title'];
                        }
                    } else {
                        if ($prefix != 0) {
                            $title = StringLib::convert_ascii(StringLib::stripUnicode($row['prefix'] . " " . $row['title']));
                        } else {
                            $title = StringLib::convert_ascii(StringLib::stripUnicode($row['title']));
                        }
                    }
                    $district[] = array('id' => $row['id'], 'title' => $title);
                }
                return System::apiSuccess(array('district' => $district));
            }
        }
        System::apiError('Không tìm thấy dữ liệu.');
    }

    function ward()
    {
        $id = Url::getParamInt('id', 0);
        $lang = Url::getParam('lang', 'vi');
        $prefix = Url::getParamInt('prefix', 0);
        if ($id > 0) {
            $ward = array();
            $re = DB::query("SELECT * FROM " . T_WARD . " WHERE district_id=" . $id . "  and status= 2 order by title asc");
            if ($re) {
                while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                    if ($lang == 'vi') {
                        if ($prefix != 0) {
                            $title = $row['prefix'] . " " . $row['title'];
                        } else {
                            $title = $row['title'];
                        }
                    } else {
                        if ($prefix != 0) {
                            $title = StringLib::convert_ascii(StringLib::stripUnicode($row['prefix'] . " " . $row['title']));
                        } else {
                            $title = StringLib::convert_ascii(StringLib::stripUnicode($row['title']));
                        }
                    }
                    $ward[] = array('id' => $row['id'], 'title' => $title);
                }
                return System::apiSuccess(array('ward' => $ward));
            }
        }
        System::apiError('Không tìm thấy dữ liệu.');
    }

    function detectmobile($cusmobile)
    {
        // $initCheck = $scope.cusmobile;
        $viettelRegx = "/^(096|097|098|0162|0163|0164|0165|0166|0167|0168|0169|086|032|033|034|035|036|037|038|039)\d{7}$/";
        $isCheckViettel = preg_match($viettelRegx, $cusmobile);
        if ($isCheckViettel) {
        } else {
        }

        $vinaRegx = "/^(094|091|088|081|082|083|084|085)\d{7}$/";
        $isCheckVina = preg_match($vinaRegx, $cusmobile);
        if ($isCheckVina) {

        } else {

        }

        $mobiRegx = "/^(090|093|089|070|076|077|078|079)\d{7}$/";
        $isCheckMobi = preg_match($mobiRegx, $cusmobile);
        if ($isCheckMobi) {
        } else {
        }
        $vietnammobiRegx = "/^(092|0188|0186|056|058|052)\d{7}$/";

        $isCheckVietnamMobi = preg_match($vietnammobiRegx, $cusmobile);
        if ($isCheckVietnamMobi) {
        } else {
        }
        $beelineRegx = "/^(099|0199|059)\d{7}$/";
        $isCheckBeeLine = preg_match($beelineRegx, $cusmobile);
        if ($isCheckBeeLine) {
        } else {
        };
    }

    function payVTC()
    {
        define("CARD_VIETTEL", "CARD_VIETTEL");
        define("CARD_MOBI", "CARD_MOBI");
        define("CARD_VIETNAM", "CARD_VIETNAM");
        define("CARD_VINA", "CARD_VINA");
        define("CARD_GTEL", "CARD_GTEL");
        $code = CookieLib::get(md5('code'));
        $code = htmlspecialchars_decode($code);
        $code = strip_tags($code);
        $gift_id = Url::getParamInt('id', 0);
        $phone = Url::getParam('phone', "");
        $network = Url::getParam('network', "");
        $prePair = Url::getParam('type', ""); //trả trước or sau

        if ($phone == "" || !FunctionLib::is_phone($phone) || $gift_id <= 0 || $network == "" || $prePair == "") {
            System::apiError('Thông tin không đầy đủ.');
        }

        $ServiceCode = "";
        switch ($network) {
            case CARD_VIETTEL:
                $ServiceCode = "VTC0027";
                $network_type = 1;
                break;
            case CARD_MOBI:
                if ($prePair == 1) $ServiceCode = "VTC0058";
                else $ServiceCode = "VTC0130";
                $network_type = 2;
                break;
            case CARD_VIETNAM:
                if ($prePair == 1) $ServiceCode = "VTC0176";
                else $ServiceCode = "VTC0176";
                $network_type = 2;
                break;
            case CARD_VINA:
                if ($prePair == 1) $ServiceCode = "VTC0057";
                else $ServiceCode = "VTC0201";
                $network_type = 2;
                break;
            case CARD_GTEL:
                if ($prePair == 1) $ServiceCode = "VTC0177";
                else $ServiceCode = "VTC0177";
                $network_type = 2;
                break;
            default:
                $network_type = -1;
                break;
        }
        if ($network_type == -1) {
            System::apiError('Chưa hỗ trợ mạng di động này');
        }
        if ($ServiceCode == "") {
            System::apiError('Chưa áp dụng hình thức nạp tiền này');
        }
        $gift = GiftDetail::get($gift_id);

        if (!empty($gift)) {
            $return = self::checkCode($gift['brand_id'], $code);
            if ($return['done'] == 1) {

                $s_code = "SELECT cart_id,cart_detail_id from " . T_GIFT_CODE . " where code='{$code}' and status =1";
                $gift_code = DB::fetch($s_code);
                if (!empty($gift_code)) {
                    $url_params = [
                        "act" => "api", // class api
                        "code" => "payVtc", //action api
                        "ServiceCode" => $ServiceCode, //check theo mã phụ lục, mã trả trước hay sau của từng nhà mạng
                        "Amount" => $gift['price'], // giá trị price của gift_detail
                        "cart_detail_id" => $gift_code['cart_detail_id'],
                        "type" => $network_type,   //1 = mua mã thẻ(Viettel), else nạp luôn vào tài khoản
                        "Account" => $phone, // Số điện thoại
                        "key" => "Pyb71cHfWJ9"
                    ];

                    $url = "https://urbox.vn/ajax.php";
                    $curl = new CURL();
                    $response = $curl->post($url, $url_params);
                    $response = json_decode($response, true);
                    // $response = [];
                    if ($response['done'] == 0) {
                        $msg = isset($response['err']) ? $response['err'] : $response['msg'];
                        System::apiError($msg);
                    } else {
                        $active = self::activeCode($gift['brand_id'], $code);
                        if ($active['done'] == 1) {
                            $note = "Khách {$phone} đã nạp tiền trực tiếp vào số điện thoại.";
                            if ($network_type == 1) {// gửi SMS nếu là mạng viettel
                                $note = "Khách {$phone} đã mua mã thẻ {$response['code']}. Số serial: {$response['serial']}";
                                $cartDetail = DB::fetch("SELECT * FROM " . T_CART_DETAIL . " WHERE id=" . $gift_code['cart_detail_id']);
                                $arrInsert = array(
                                    'cat_id' => $gift_code['cart_id'],
                                    'app_id' => $cartDetail['app_id'],
                                    'cart_detail_id' => $gift_code['cart_detail_id'],
                                    'phone' => $phone,
                                    'content' => "Ma the nap cua ban la {$response['code']}: . Your series number is: " . $response['code'],
                                    'sent' => 0,
                                    'created' => TIME_NOW
                                );
                                DB::insert(T_SMS_SENT, $arrInsert);
                                // Sms::sendSms($phone, "Ma the nap cua ban la {$response['code']}: . Your series number is: " . $response['code'], 'Urbox.vn', 'Urbox.vn', 0, 0);
                            }

                            // Gửi Email
                            $content = '<h3>Thông tin khách đặt Grab</h3><br/><br/>
                                        <table cellpadding="1" cellspacing="1" width="100%">
                                        <tr><td>Số điện thoại:</td><td>' . $phone . '</td>
                                        <tr><td>Đơn hàng:</td><td>' . $gift_code['cart_id'] . '</td>
                                        <tr><td>Đơn hàng con:</td><td>' . $gift_code['cart_detail_id'] . '</td>
                                        <tr><td>Ghi chú thêm:</td><td>' . $note . '</td>
                                        </tr></table>';
                            $rowInsertEmail = array(
                                'subject' => 'Khách nạp Topup qua Grab thành công',
                                'email' => '<EMAIL>',
                                'content' => $content,
                                'time_send' => 0,
                                'created' => TIME_NOW,
                            );
                            DB::insert(T_EMAIL_SEND, $rowInsertEmail);
                            // Lưu lại thông tin khách

                            $checkReceiver = DB::fetch("SELECT id FROM " . T_GIFT_RECEIVER . " WHERE phone='" . $phone . "'");
                            if (!$checkReceiver) {
                                $checkReceiver['id'] = DB::insert(T_GIFT_RECEIVER, ['phone' => $phone, 'rank' => 102]);
                            }
                            DB::update(T_CART_DETAIL, array('receiver_id' => $checkReceiver['id'], 'app_id' => APP_GRAPREWARD, 'delivery_note' => $note), 'id=' . $gift_code['cart_detail_id']);

                            // trả vè thông báo cho người dùng
                            System::apiSuccess(['type' => $network_type, "msg" => 'Nạp tiền thành công']);
                        } else {
                            System::apiError(isset($active['msg']) ? $active['msg'] : '');
                        }
                    }
                }
            } else {
                System::apiError(isset($return['msg']) ? $return['msg'] : '');
            }

            System::apiError('Bạn chưa nhập mã code');
        }
        System::apiError('Chưa áp dụng hình thức nạp tiền này');
        //
        #Done=0 ko thanh cong ban ra loi data['msg']]
        #Done =1
        #type=1 => gưi sms cho so dien thoai do (gưi sms ma nap thẻ của ban +data['code']])
        #type=0 => nap truoc tiep thì chuyen sang trang

    }

    function sendGift()
    {
        System::apiError('Dữ liệu không hợp lệ.');
        $id_cart_detail = Url::getParamInt('id', 0);
        $phone = Url::getParam('friendPhone', '');
        $friendName = Url::getParam('friendName', '');
        $friendMessage = Url::getParam('friendMessage', '');
        $encrypt_auth = CookieLib::get('auth');
        $token = CookieLib::get('cardNum');
        if ($token != "" && $id_cart_detail > 0 && $phone != "" && $friendName != "") {
            $row = DB::fetch("SELECT id,cart_id,receive_code,app_id FROM " . T_CART_DETAIL . " WHERE id=$id_cart_detail AND receiver_id =0  AND (thankyou is null OR thankyou ='')");
            if ($row) {
                $link = LINK_URBOX . 'nhan-qua/' . System::decrypt($row['receive_code']) . '.html';
                $link_sort = Shorten::build($link);
                $note = StringLib::stripUnicode(StringLib::truncateHtml($friendMessage, 25));
                if ($note == '...') $note = "";
                $card = Card::getByToken($token);
                if (empty($card)) {
                    System::apiError('Quà không tồn tại.');
                }
                if ($row['app_id'] != $card['app_id']) {
                    System::apiError('Quà không tồn tại(1).');
                }

                if ($note != "") {
                    $sms_id = TEMPLATE_SEND_GIFT;
                    $sms_content = [
                        "1",
                        $link_sort,
                        //$note,
                        UB_CUSTOMER_PHONE,
                    ];
                } else {
                    $sms_id = TEMPLATE_SEND_GIFT;
                    $sms_content = [
                        "1",
                        $link_sort,
                        UB_CUSTOMER_PHONE,
                    ];
                }
                $smsResponse = Sms::sendSms($phone, $sms_content, $sms_id, $row['app_id']);
                if ($smsResponse['done'] == 0) {
                    $tag = [
                        'error_cat' => "otp",
                        "error_from" => "urbox-page",
                        "error_by_team" => "tech",
                        "client_name" => $card['app_id']
                    ];
                    $card_sms = [
                        "id" => $card['id'],
                        'phone' => $phone,
                        "app_id" => $card['app_id'],
                        "ip" => System::ip()
                    ];
                    $smsResponse['source'] = "index.php";
                    Card::logMessage($card_sms, SentryMessage::SEND_GIFT, $smsResponse, $tag);
                }
                $receiver = ReceiverCore::add(array(
                    'phone' => $phone,
                    'fullname' => $friendName,
                    'first_name' => $friendName
                ));
                DB::update(T_CART_DETAIL, array('receiver_id' => $receiver['id'], 'thankyou' => $sms_id), 'id=' . $id_cart_detail);

                System::apiSuccess(['msg' => "Đã gửi quà tới bạn"]);
            }
            System::apiError('Bạn đã sử dụng hoặc tặng món quà này cho người khác rồi.');
        }
        System::apiError('Dữ liệu không hợp lệ.');
    }
}