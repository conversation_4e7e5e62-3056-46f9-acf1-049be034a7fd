<?php


class ajax_vnaMile
{
    const OK = 0;
    const API_URL = "https://pmsapiprd.vietnamairlines.com/EarnApi/v1/";
    const API_AUTH = "authen";
    const API_GIVE_MILE = "give-miles";
    const PENDING = 9;
    const NOT_FOUND = 11;
    const POINT_UNIT_ID = 2;
 	const TOPUP_ERROR_VAR = "ErrorTurn:";
 	const PIN_ERROR_VAR = "PinTurn:";
 	const MAX_ERROR_TURN = 5;
 	
    private $clientId = "ur3_api_prod";
    private $clientSecret = "VXIzVk5BQDIwMjQwMQ==";
//    private $clientId = "urb_api";
//    private $clientSecret = "VXJib3h2bmFhcGlAMTIzNDU=";
    private $rateName = '0901/2025/VNA-URB';
    private $header;
    
	private $app_id;

	function __construct()
	{
		$code = System::$data['url']['query'][2];
		if (!method_exists($this, $code)) {
			$this->index();
		}
		$this->$code();
	}
	function index(){
		System::apiError('run index');
	}
	function giveMiles(){
		$_token = Url::getParam('token','');
		$_memberNo = Url::getParamInt('memberId',0);
		$_memberSurName = Url::getParam('memberSurName','');
		$_memberFirstName = Url::getParam('memberFirstName','');
		$_memberPhone = Url::getParam('memberPhone','');
		$_memberEmail = Url::getParam('memberEmail','');

		$_memberNo = str_replace(" ","",$_memberNo);
		if(empty($_memberNo) || (strlen($_memberNo) <8 && strlen($_memberNo) > 10)){
			System::apiError(("Mã hội viên không chính xác"),['errType'=> 1]);
		}
		if(empty($_memberSurName) || strlen($_memberSurName) > 20){
			System::apiError(("Họ tên hội viên không hợp lệ"),['errType'=> 1]);
		}
		if(empty($_memberFirstName) || strlen($_memberFirstName) >25){
			System::apiError(("Họ tên hội viên không hợp lệ"),['errType'=> 1]);
		}
		
		if(empty($_memberPhone) || !FunctionLib::is_phone($_memberPhone)){
			System::apiError(("Số điện thoại không hợp lệ"),['errType'=> 1]);
		}
		if(!empty($_memberEmail) && !FunctionLib::is_email($_memberEmail)){
			System::apiError(("Email không hợp lệ"),['errType'=> 1]);
		}

        $card = Point::getByToken($_token);
		if(empty($card)){
			System::apiError(("Mã topup không chính xác"),['errType'=> 1]);
		}
		if($card['process'] > 2){
			System::apiError(("Mã topup đã được sử dụng. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ"),['errType'=> 4]);
		}
		if($card['lock_time'] > TIME_NOW - 3600){
			System::apiError(("Trang nạp dặm này đã bị khoá do nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ"),['errType'=> 2]);
		}
		#$_auth = Point::getAuth($_token);
		//$_auth = $_SESSION['vna_authen'];
		
		#if(empty($_auth) || $_auth != md5($card['id'].$card['token'])){
		//if(empty($_auth) || $_auth != $card['token']){
			//System::apiError(("Bạn cần đăng nhập trước khi sử dụng"),['errType'=> 1]);
		//}
		$topUpTurn = VnaMile::getErrorTime(self::TOPUP_ERROR_VAR,$card['token']);
		if($topUpTurn >= self::MAX_ERROR_TURN){
			//DB::update(T_POINT,['lock_time'=> time(), "error_time" => time()],'id='.$card['id']);
			//Point::updateHash($card['id']);
			VnaMile::delRedisKey(self::TOPUP_ERROR_VAR,$card['token']);
			System::apiError(("Trang nạp dặm này đã bị khoá do nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ"),['errType'=> 2]);
		}
	
		$point_units = Point::getPointUnit(self::POINT_UNIT_ID);
		if(empty($point_units)) {
			System::apiError(("Mã chương trình không đúng. Vui lòng liên hệ hotline ". UB_CUSTOMER_PHONE ." để được hỗ trợ"), ['errType' => 1] );
		}
        if($card['expired'] < time() && $card['expired'] != 0){
            System::apiError(("Quà tặng của bạn đã hết hạn. Vui lòng liên hệ hotline ". UB_CUSTOMER_PHONE ." để được hỗ trợ"), ['errType' => 1] );
        }
        date_default_timezone_set('Asia/Bangkok');
        $request = [
			"memberNo"=> (int)$_memberNo,
			"firstName"=> $_memberFirstName,
			"surname"=> $_memberSurName,
			"transactionDate"=> date("Y-m-d"),
//			"invoiceNumber"=> $card['cart_detail_id'],
			"merMemberNo"=> "{$card['customer_id']}",
			"transactionCode"=> "{$card['id']}",
			"vnaPoint"=> $card['amount'],
			"vnaPointType"=> "Bonus Mile",
			"merPoint"=> $card['amount'],
//			"memberEmail"=> $_memberEmail,
			"merPointType"=> "point",
			"rateName"=> $this->rateName,
		];
		DB::update( T_POINT, ["app_user_id" => $_memberNo,"topup_time" => TIME_NOW, "process" => 2], "id={$card['id']}" );
		$this->app_id = $card['app_id'];
		$enc_phone = System::encrypt($_memberPhone);
		$phone_sub = substr($_memberPhone,-6);
		$response = $this->request(self::API_GIVE_MILE,$request);
		if($response->code == 0 && $response->statuscode == 200) {
			$cus = Customer::add(['first_name'=> $_memberFirstName,'last_name'=> $_memberSurName,"email"=>$_memberEmail,"phone"=>$_memberPhone]);
			DB::update(T_POINT,["done_time" => time(),"process" => 3,'receiver_id'=>$cus['id'],"phone"=>$enc_phone,'phone_sub'=>$phone_sub],'id='.$card['id']);
			Point::updateHash($card['id']);
			VnaMile::delRedisKey(self::TOPUP_ERROR_VAR,$card['token']);
			// cập nhật trạng thái code thành đã sử dụng
			$gift_detail = DB::fetch('SELECT brand_id from '. T_GIFT_DETAIL . " where id='{$card['gift_detail_id']}'");
			$sql = "SELECT id,cart_id,cart_detail_id FROM " . T_GIFT_CODE . " WHERE cart_detail_id={$card['cart_detail_id']} AND gift_detail_id={$card['gift_detail_id']} AND active = 1 AND status = 1 AND brand_id =" . $gift_detail['brand_id'] . " AND ( expired = 0 OR expired>=" . TIME_NOW . ")";
			$gift_code = DB::fetch($sql);
			GiftCode::useCode($gift_code,$gift_detail['brand_id'],true);
			System::apiSuccess($response);
		}else {
			VnaMile::setErrorTime(self::TOPUP_ERROR_VAR,$card['token']);
			DB::update(T_POINT,["error_time" => time(),"phone"=>$enc_phone,'phone_sub'=>$phone_sub],'id='.$card['id']);
			Point::updateHash($card['id']);
			#System::apiError($response['msg']);
			if($topUpTurn >= (self::MAX_ERROR_TURN -1)){
				DB::update(T_POINT,['lock_time'=> time()],'id='.$card['id']);
				System::apiError("Trang nạp dặm này đã bị khoá do nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ",['errType'=> 2]);
			}
			System::apiError(("Quý khách vui lòng kiểm tra lại thông tin.<br>Trang nạp dặm này sẽ bị khoá nếu nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." nếu quý khách cần hỗ trợ"));
		}
		
	}
	
	function checkPin(){
		$_pin = Url::getParamInt('pin', 0);
		$_token = Url::getParam('token','');

		if(strlen($_pin) != 6 || !is_numeric($_pin)){
			System::apiError("Mã kích hoạt không đúng. Vui lòng nhập lại");
		}
		$card = Point::getByToken($_token);
		if(empty($card)){
			System::apiError("Mã kích hoạt không đúng. Vui lòng nhập lại.<br> Tính năng sẽ bị khoá nếu nhập sai quá 5 lần");
		}
		if($card['process'] > 2){
			System::apiError("Mã topup đã được sử dụng. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ",['errType'=> 4]);
		}
		if($card['lock_time'] > TIME_NOW - 3600){
			System::apiError(("Trang nạp dặm này đã bị khoá do nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ"),['errType'=> 2]);
		}
		$topUpTurn = VnaMile::getErrorTime(self::PIN_ERROR_VAR,$card['token']);
		if($topUpTurn >= self::MAX_ERROR_TURN ){
			//DB::update(T_POINT,['lock_time'=> TIME_NOW,"error_time" => time()],'id='.$card['id']);
			//Point::updateHash($card['id']);
			VnaMile::delRedisKey(self::PIN_ERROR_VAR,$card['token']);
			System::apiError("Trang nạp dặm này đã bị khoá do nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ",['errType'=> 2]);
		}
		$pin = Point::hashPin($_pin,$card['id']);
		if($card['pointu_id'] != self::POINT_UNIT_ID){
			VnaMile::setErrorTime(self::PIN_ERROR_VAR,$card['token']);
			System::apiError("Mã chương trình không đúng. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ.<br> Tính năng sẽ bị khoá nếu nhập sai quá 5 lần!",['time'=>$topUpTurn]);
			
		}
		if($pin != $card['pin']){
			VnaMile::setErrorTime(self::PIN_ERROR_VAR,$card['token']);
			if($topUpTurn >= (self::MAX_ERROR_TURN -1)){
				DB::update(T_POINT,['lock_time'=> time(), "error_time" => time()],'id='.$card['id']);
				System::apiError("Trang nạp dặm này đã bị khoá do nhập sai thông tin quá 5 lần. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ",['errType'=> 2]);
			}
			System::apiError("Mã kích hoạt không đúng. Vui lòng nhập lại.<br> Tính năng sẽ bị khoá nếu nhập sai quá 5 lần!",['time'=>$topUpTurn]);
		}
		
		if($card){
			VnaMile::delRedisKey(self::PIN_ERROR_VAR,$card['token']);
			$_SESSION['vna_authen'] = $card['token'];
			System::apiSuccess(['token'=>$card['token']]);
		}
	}
	
    private function _Auth(){
        $body = [
            "clientId"=> $this->clientId,
            "clientSecret"=> $this->clientSecret
        ];
        $result = $this->request(self::API_AUTH,
			$body
        );
        return $result;
    }
 	private function makeHeader($params,$api){
        date_default_timezone_set('GMT');
		$date =  gmdate('D, d M Y H:i:s \G\M\T', time());
		$_arr["Date"] = $date;
		if($api != self::API_AUTH) {
			$auth = $this->_Auth();
			if(isset($auth->token) && isset($auth->hashKey) && $auth->statuscode == 200){
				$body_json = json_encode( $params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES ) . "&" . $date;
				$hashStr = hash_hmac( 'sha256', $body_json, $auth->hashKey );
				$hashStr = strtolower( $hashStr );
				$pre_base64 = $auth->token . ":" . $hashStr;
				$Authorization = base64_encode( $pre_base64 );
				$_arr["Authorization"] = $Authorization;
				$this->header = $_arr;
				return ['done' => 1];
			}else{
				return['done'=> 0,'msg'=>"Lỗi xác thực. Vui lòng gọi đến ". UB_CUSTOMER_PHONE ." để được hỗ trợ!"];
			}
		}
		$this->header = $_arr;
		return ['done' => 1];
	}

	private function request($api,$data = []){
		
		if(!in_array($api,[self::API_GIVE_MILE,self::API_AUTH])){
			return false;
		}
		$header = [
			"Content-Type"=> "application/json;charset=UTF-8",
		];
		$_header =$this->makeHeader($data,$api);
		if($_header['done'] == 0){
			System::apiError($_header['msg'],['errType'=> 1]);
		}
		if(!empty($this->header)){
			foreach ($this->header as $key => $item) {
				$header[$key] = $item;
			}
		}
		$body = [];
		$body += $data;
		$body = json_encode($body,JSON_HEX_TAG | JSON_UNESCAPED_SLASHES);
		
		$client = new \GuzzleHttp\Client(["base_uri"=> self::API_URL]);
		$request = [
			"headers" => $header,
			"body" => $body
		];
        $user = [
            'id' => isset($body['memberNo'])?$body['memberNo']:'',
            'username' => isset($body['firstName'])?$body['firstName']:'',
            'email' => isset($body['memberEmail'])?$body['memberEmail']:'',
            'ip_address' => System::ip()
        ];
		try {
			$_response = $client->post($api, $request );
			$status_code = $_response->getStatusCode();
			$response = $_response->getBody()->getContents();
			$responseObject = json_decode($response);
			$responseObject->statuscode = $status_code;
			$response = json_encode($responseObject);
		}catch (\GuzzleHttp\Exception\RequestException $e){
            Loyalty::writeSentryLog($e,$user);
			$response = $e->getResponse()->getBody()->getContents();
			$status_code = $e->getResponse()->getStatusCode();
			$responseObject = json_decode($response);
			$responseObject->statuscode = $status_code;
			$response = json_encode($responseObject);
			
		}catch (Exception $e) {
            Loyalty::writeSentryLog($e,$user);
			$_response = new \stdClass;
			$_response->statuscode = 400;
			$_response->msg = $e->getMessage();
			$response = json_encode($_response);
		}finally {
            $entity = array(
				'request' => json_encode($request),
				'response' => is_string($response)?$response: json_encode($response),
			);
			DB::insert(T_LOGS_API,['param'=> $entity['request'],'response'=> $entity['response'],'site'=>DOMAIN_NAME,'linkapi'=>self::API_URL . $api]);
			return json_decode($response);
		}
	}
	
}