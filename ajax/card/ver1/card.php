<?php


class cardv
{
    function __construct()
    {
        $code = System::$data['url']['query'][3];
        if (!method_exists($this, $code)) {
            $this->index();
        }
        $this->$code();
    }

    function createOtp(){
        //token: token,phone:phone_number
        $token = Url::getParam('token', '');
        $phone = Url::getParam('phone', '');
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        
        $card = Card::getByToken($token);
        if(empty($card)){
            System::apiError('Không tìm thấy thẻ.', array('errType' => 0));
        }
		if($card['status'] != IS_OFF){
			System::apiError('Thẻ đã kích hoạt hoặc đang bị khóa.', array('errType' => 0));
		}
        if ($phone == '') {
            System::apiError('Bạn vui lòng nhập số điện thoại.');
        }
        if (!FunctionLib::is_phone($phone)) {
            System::apiError('Số điện thoại bạn nhập không đúng định dạng.', array('errType' => 2));
        }
        #Ngay 8 - 8 - 2019
        #Doi voi so Dien thoai se ma hoa
        $phoneEncrypt = System::encrypt($phone,CRYPT_KEY);
        
        if($card['phone'] != "" && $card['pin'] != "" && $card['phone'] != $phoneEncrypt){
			System::apiError('Số điện thoại bạn nhập không đúng với số điện thoại đã đăng ký.', array('errType' => 3));
		}
		if (!Card::isWhitelist( ['app_id' => $card['app_id'], 'phone' =>$phone, 'phone_sub' => substr($phone,4)] )) {
			//System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
			$inWhiteList = 0;
		}else{
			$inWhiteList = 1;
		}
		$app = Card::getAppById($card['app_id']);
		if($app) {
			if (isset( $app['needHasWhitelist'] ) && $app['needHasWhitelist'] == 2) {
				if ($inWhiteList == 0) {
					System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
				}
			}
		}else{
			System::apiError('Thẻ không thuộc chương trình nào.', array('errType' => 3));
		}
        #END
		if (!in_array($card['status'],[CARD_DEACTIVATE,CARD_ACTIVE])) {
			System::apiError('Thẻ đang bị khoá.');
		}
		if($card['needActive'] == IS_YES && $card['needExactPhone'] == IS_YES ){
			#Neu can actice và sdt chi dc nhap trong topup
			$preAcc = DB::fetch("SELECT * FROM ".T_CARD_PREACC." WHERE phone='".$phoneEncrypt."' AND app_id=".$card['app_id']);
			if(empty($preAcc)){
				System::apiError('Số điện thoại không tồn tại.');
			}
		}
        if ($card && is_numeric($phone)) {
			if($card['needActive'] == 1 && $card['phone'] != "" && $card['pin'] != "" && $card['status'] == CARD_DEACTIVATE){
				$encrypt_session = System::encrypt($token, CRYPT_KEY);
				CookieLib::set('auth',$encrypt_session,TIME_NOW+(60*60));
				DB::update(T_CARD, ['phone_sub'=> substr($phone,4),'status' => CARD_ACTIVE, 'status_hash' => Card::statusHash( $card['token'], $card['id'], CARD_ACTIVE )], "id={$card['id']}" );
				Card::updateHash($card['id']);
				System::apiError('oK.',array('errType'=> 4));
			}
			$newOtp = Card::createOtp();
			Card::active($card,$phoneEncrypt,CARD_DEACTIVATE,$newOtp);
			DB::update(T_CARD,['phone_sub'=> substr($phone,4)],"id={$card['id']}");
			$_SESSION['otp_phone'] = $phone;
			Card::sendOTP($card,$phone,$newOtp);
            System::apiSuccess($phone);
        } else {
            System::apiError("Số điện thoại hoặc token không đúng!");
        }
    }

    function resendOtp(){
        //token: token,phone:phone_number
        $token = Url::getParam('token', 0);
        $phone = Url::getParam('phone', 0);
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($phone == '') {
            System::apiError('Bạn vui lòng nhập số điện thoại.');
        }
        if (!FunctionLib::is_phone($phone)) {
            System::apiError('Số dữ liệu bạn nhập vào là không đúng.', array('errType' => 2));
        }

        if ($token != "" && is_numeric($phone)) {
            $phoneEncrypt = System::encrypt($phone,CRYPT_KEY);
            #END
            $newOtp = Card::createOtp();
            $card = Card::getByToken($token);
            if(empty($card)){
				System::apiError('Thẻ không tồn tại.');
			}
			
			if (!in_array($card['status'],[CARD_DEACTIVATE,CARD_ACTIVE])) {
				System::apiError('Thẻ đang bị khoá.');
			}
			if($card['phone'] != "" && $card['pin'] != "" && $card['phone'] != $phoneEncrypt){
				System::apiError('Số điện thoại bạn nhập không đúng với số điện thoại đã đăng ký.', array('errType' => 2));
			}
			if (!Card::isWhitelist( ['app_id' => $card['app_id'], 'phone' =>$phone, 'phone_sub' => substr($phone,4)] )) {
				System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
				$inWhiteList = 0;
			}else{
				$inWhiteList = 1;
			}
			$app = Card::getAppById($card['app_id']);
			if($app) {
				if (isset( $app['needHasWhitelist'] ) && $app['needHasWhitelist'] == 2) {
					if ($inWhiteList == 0) {
						System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
					}
				}
			}else{
				System::apiError('Thẻ không thuộc chương trình nào.', array('errType' => 3));
			}
			if($card['needActive'] !=2 && $card['phone'] != "" && $card['pin'] != "" && $card['status'] == CARD_ACTIVE){
				System::apiError('Chức năng không dành cho thẻ này.');
			}
			if($card['needActive'] == 1 && $card['phone'] != "" && $card['pin'] != "" && $card['status'] == CARD_DEACTIVATE){
				$encrypt_session = System::encrypt($token, CRYPT_KEY);
				CookieLib::set('auth',$encrypt_session,TIME_NOW+(60*60));
				DB::update( T_CARD, ['phone_sub'=> substr($phone,4),'status' => CARD_ACTIVE, 'status_hash' => Card::statusHash( $card['token'], $card['id'], CARD_ACTIVE )], "id={$card['id']}" );
				Card::updateHash($card['id']);
				System::apiError('oK.',array('errType'=> 4));
			}
			Card::sendOTP($card,$phone,$newOtp);
            Card::active($card,$phoneEncrypt,CARD_DEACTIVATE,$newOtp);
			$_SESSION['otp_phone'] = $phone;
			DB::update(T_CARD,['phone_sub'=> substr($phone,4)],"id={$card['id']}");
            System::apiSuccess($newOtp);
        } else {
            System::apiError("Số điện thoại hoặc token không đúng!");
        }
    }

    function validateOtp()
    {
        $phone = Url::getParam('phone', '');
        $otp = Url::getParam('otp', '');
        $token = Url::getParam('token', '');

        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($otp == '') {
            System::apiError('Bạn chưa nhập mã xác nhận.');
        }
        if ($phone == '') {
            System::apiError('Bạn vui lòng nhập số điện thoại.');
        }
        if (!FunctionLib::is_phone($phone)) {
            System::apiError('Số điện thoại bạn nhập không đúng định dạng.', array('errType' => 2));
        }
		$phoneEncrypt = System::encrypt($phone,CRYPT_KEY);
        $encrypt_token = System::encrypt($token,CRYPT_KEY);
        #END
        $card = DB::fetch("SELECT id,active_time,phone,pin,app_id,issue_id,token,status,issue_id,needActive,gift_id FROM " . T_CARD . " WHERE token='{$encrypt_token}' AND otp='$otp' and status in(1,2) ORDER BY id DESC LIMIT 1");
        // triển
		if ($card['id'] > 0) {
			if($card['phone'] == "" && $card['pin'] == "" && $phone != $_SESSION['otp_phone']){
				System::apiError('Số điện thoại bạn nhập khác với số điện thoại nhận OTP.');
			}
			
			$app = BalanceVer1::getApp($card);
			$useWL = 1;
			if(isset($app['needHasWhitelist']) && $app['needHasWhitelist'] == 2) {
				if (!Card::isWhitelist(['app_id' => $card['app_id'], 'phone' =>$phone, 'phone_sub' => substr($phone,4)] )) {
					System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
				}
				$useWL = 2;
			}
			if($card['phone'] == "" && $card['pin'] == "" && $phone == $_SESSION['otp_phone'] ){
				DB::update(T_CARD,['phone'=>$phoneEncrypt,'phone_sub'=> substr($phone,4)],"id={$card['id']}");
			}
			
			if($phoneEncrypt != $card['phone']){
				System::apiError( 'Mã OTP đã hết hạn(1).');
			}
            if(TIME_NOW - $card['active_time'] <= 60 ) {
				if($card['phone'] != "" && $card['pin'] == "" && $phone == $_SESSION['otp_phone'] && ($useWL == 2 && $card['needActive'] == 1 || $useWL == 1 && $card['needActive'] == 2)){
					$encrypt_session = System::encrypt($token, CRYPT_KEY);
					CookieLib::set('auth',$encrypt_session,TIME_NOW+(60*60));
				}
    
				if($card['needActive'] == 2 && $card['pin'] != "" && $card['status'] == CARD_DEACTIVATE){
					DB::update(T_CARD,['pin'=>''],"id={$card['id']}");
				}
				$countCard= DB::query("SELECT id,number,money,app_id,token from ". T_CARD. " where phone='{$phoneEncrypt}'")->fetchAll(PDO::FETCH_ASSOC);
				if(count($countCard) > MAX_CARD_PER_PHONE && $useWL != 2) {
					$toCC = EMAIL_NOTIFY;
					$server = FunctionLib::ip();
					$content = 'Time: ' . date('d-M-Y H:i:s') . '<br />';
					$content .= 'Server: ' . $server . '<br />';
					$content .= 'Content: Số điện thoại ' . $phone . ' đã được sử dụng để kích hoạt ' . count($countCard) . ' thẻ <br />';
					$content .= "Danh sách thẻ:<br>";
					foreach ($countCard as $item) {
						$content .= "Số thẻ: {$item['number']}, AppID: {$item['app_id']}, Money: {$item['money']} <br>";
					}
//					Card::changeGiftSetFraud($card);
                    $appLog = $app;
                    unset($appLog['access_key']);
                    SentryMessage::logMaxCard($card, $appLog);
					System::send_mail('System', '<EMAIL>', 'URPAGE Active Phone - '.$phone.' - ' . WEB_ROOT . ' - ' . date('d-M-Y H:i'), $content,$toCC,2);
				}
				Card::active($card, $phoneEncrypt, CARD_ACTIVE);
				Card::updateHash($card['id']);
				DB::update(T_CARD,['otp'=>'','phone_sub'=> substr($phone,4)],"id={$card['id']}");
                System::apiSuccess(['link' =>"/card/{$token}"]);
            }
            else System::apiError( 'Mã OTP đã hết hạn');
        } else {
            System::apiError('Mã OTP không chính xác(2).');
        }
    }

    function savePin()
    {
        $pin = Url::getParam('pin', '');
        $pin2 = Url::getParam('pin2', '');
        $token = Url::getParam('token', '');

        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (is_integer($pin)) {
            System::apiError('Mã PIN phải là số.');
        }
        if ($pin2 == '') {
            System::apiError('Bạn vui lòng nhập xác nhận lại mã PIN.');
        }
        if ($pin2 != $pin) {
            System::apiError('Mã PIN không giống nhau.');
        }
        $isVailPin = Card::strongPin($pin);
        if(!$isVailPin){
			System::apiError('Mã PIN không đủ mạnh. Vui lòng nhập mã PIN khác!');
		}
        $tokenCard  =  Card::getByToken($token);
        if(empty($tokenCard)){
			System::apiError('Thẻ không tồn tại.');
		}
		if($tokenCard['needActive'] !=2){
			System::apiError('Chức năng không dành cho thẻ này.');
		}
        if($tokenCard['id'] > 0){
			$db_pin = Card::encodePin($pin);
			$hash_pin = Card::hashPin($tokenCard['id'],$db_pin);
			$row = array(
				'pin' => $db_pin,
				'pin_hash' => $hash_pin
			);
            DB::update(T_CARD, $row, "id='{$tokenCard['id']}'");
			$encrypt_session = System::encrypt($token, CRYPT_KEY);
            CookieLib::set('auth',$encrypt_session,TIME_NOW+(60*60));
        }
		$logData = [
			'done' => 1,
			'ip' => System::ip(),
			'token' => $token,
			'userID' => System::encrypt($pin),
			'agent' =>$_SERVER['HTTP_USER_AGENT']
		];
		Card::logCard($token,CARD_ACTION_LOGIN,json_encode($logData),2);
        System::apiSuccess(['link'=> "/card/{$token}"]);
    }

    function checkPin()
    {
        $pin = Url::getParam('pin', '');
        $token = Url::getParam('token', '');

        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (is_integer($pin)) {
            System::apiError('Mã PIN phải là số.');
        }
		$login = Card::login($token,$pin);
		if($login){
			System::apiSuccess(['link' => "/card/{$token}"]);
		}else{
			System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
		}
    }


    function checkUPin()
    {
        $pin = Url::getParam('pin', '');
        $token = Url::getParam('token', '');

        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (strlen($pin) !=6 && strlen($pin) !=7) {
            System::apiError('Mã PIN không hợp lệ.');
        }
		$login = Card::login($token,$pin, true);
		if($login){
			System::apiSuccess(['link' => "/card/{$token}"]);
		}else{
			System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
		}
    }

    function listGifts()
    {

        $token = Url::getParam('token', '');
		if($token == "")  System::apiError("Token không hợp lệ.");
        $card = Card::getByToken($token);
        if ($card) {
            #Gift
            $per_page = Url::getParamInt('per_page', 5);
            $city = Url::getParamInt('city', 0);
            $cat_id = Url::getParamInt('cat_id', 0);
            $page_no = Url::getParamInt('page_no', 1);
            $id_gift_set =  $card['gift_id']>0?$card['gift_id']:CGlobal::$idGiftSetDefault;
            $param = array();
            if ($cat_id > 0) {
                $cat_arr[$cat_id] =  $cat_id;
                $catData = DB::query("SELECT * FROM ".T_CATEGORY." WHERE status=1 AND parent_id=".$cat_id);
                if($catData){
                    while ($rw = $catData->fetch(PDO::FETCH_ASSOC)) {
                        $cat_arr[$rw['id']] =  $rw['id'];
                    }
                }
                if(!empty($cat_arr)){
                    $param[] = 'cat_id IN('.implode(',',$cat_arr).')';
                }
            }
            $param[] = 'code_quantity>0';
            $param[] = 'status=1';

            $id_gift_detail = array();

            #Lay them phan chi ban theo GIFT_SET
            $getSiteGift = DB::query("SELECT * FROM " . T_GIFT_SET . " WHERE gift_id=" . $id_gift_set);

            if ($getSiteGift) {
                while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)) {
                    $id_gift_detail[$row['gift_detail_id']] = $row['gift_detail_id'];
                }
                if (!empty($id_gift_detail)) {
                    $param[] = " id IN (" . implode(',', $id_gift_detail) . ")";
                }
            }
            #loc theo LOCATION
            if ($city > 0) {
                $giftId2 = array();
                if(!empty($id_gift_detail)){
                    $re = DB::query("SELECT * FROM " . T_GIFT_CITY . " WHERE (city_id=" . $city . " OR city_id=0) AND gift_detail_id IN (" . implode(',',$id_gift_detail) . ")");

                }else{
                    $re = DB::query("SELECT * FROM " . T_GIFT_CITY . " WHERE city_id=" . $city . " OR city_id=0");
                }
                if ($re) {
                    while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                        $giftId2[$row['gift_detail_id']] = $row['gift_detail_id'];
                    }
                    if (!empty($giftId2)) {
                        $param[] = " id IN (" . implode($giftId2, ',') . ")";
                    } else {
                        $param[] = " id = 0 ";
                    }
                }
            }
            $condition = FunctionLib::addCondition($param, true);

            $sql = "SELECT * FROM " . T_GIFT_DETAIL . $condition . " ORDER BY id DESC";
            $re = Pagging::query($sql, $per_page, $page_no);
            $data = array();
            if ($re&&Pagging::$totalPage>=$page_no) {
                $arr_brand_id = array();
                $arr_gift_id = array();
                while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                    $images = MediaUrl::fromImageTitle($row['avatar']);
                    $temp = array(
                        'id'=>$row['id'],
                        'brand_id'=>$row['brand_id'],
                        'cat_id'=>$row['cat_id'],
                        'gift_id'=>$row['gift_id'],
                        'title'=>$row['title'],
                        'price'=>$row['price'],
                        'view'=>$row['view'],
                        'link'=>'/card/'.$card['token'].'/'.$row['id'],
                        'image'=>isset($images[640])?$images[640]:''
                    );
                    $arr_brand_id[$row['brand_id']] = $row['brand_id'];
                    $arr_gift_id[$row['gift_id']] = $row['gift_id'];
                    $data[$row['id']] = $temp;
                }

                System::apiSuccess(array('gifts' => array_values($data)));

            }
        }
        System::apiError('No data found');
    }

    /**
     * Đặt mua
     */
    function buy()
    {
        $token = Url::getParam('token', '');
        $gift = Url::getParam('gift', "");
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.');
        }
        if(empty($gift)){
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.');
        }
        $card = Card::getByToken($token);
		if(empty($card) || !in_array($card['status'],[CARD_ACTIVE,CARD_WALLET])){
			System::apiError('Thẻ không hợp lệ.');
		}
        if($card['expired_time'] > 0 && $card['expired_time'] < TIME_NOW){
            System::apiError('Thẻ của bạn đã hết hạn sử dụng. Bạn chỉ có thể xem các quà đã đổi tại mục "Quà của tôi"');
        }
        if ($card) {
			if($card['pin'] != ""){
				$encrypt_session = CookieLib::get('auth');
				$decrypt_token = System::decrypt($encrypt_session,CRYPT_KEY);
				if($token != $decrypt_token){
					if($card['needActive'] == 1){
						CookieLib::set('upin','');
					}
					System::apiError('Bạn cần đăng nhập trước khi đổi quà.');
				}
			}
            if($card['version'] != 'ver1'){
                System::apiError('Version thẻ không hợp lệ.');
            }
			$giftIDs = [];
			$gift = [array_shift($gift)];
			$gift_ids = implode(",",array_column($gift,"id"));
	
			foreach ($gift as &$ig) {
				$ig['quantity'] = (int)$ig['quantity'];
				if($ig['quantity'] <=0) $ig['quantity'] = 1;
				if($ig['quantity'] > 10) $ig['quantity']= 10;
				$giftIDs[] = $ig['id'];
			}
			$gift_ids = implode(",",array_column($gift,"id"));
			// nếu mua quà bảo hiểm thì số lượng chỉ dc phép 1
			$giftDetail = DB::fetch_all("SELECT id,type,gift_id FROM " . T_GIFT_DETAIL . " WHERE id in($gift_ids)");
			$giftParent = [];
			$needRedirect = [];
			$isCombo = 0;
			foreach ($giftDetail as $item) {
				$giftParent[] = $item['gift_id'];
				if($item['type'] == GIFT_TYPE_RECEIVER){
					$gift[0]["quantity"] = 1;
					$needRedirect[] = $item['gift_id'];
				}
				if($item['type'] == GIFT_TYPE_COMBO){
					$isCombo++;
				}
			}
			$options= [
				"phone"=> !empty($card['phone'])?System::decrypt($card['phone']):"",
				"identity"=> "",
				"address"=> "",
				'needRedirect'=> $needRedirect
			];
			$giftTopup = Gift::isTopupGift($giftParent);
			if($giftTopup > 0 || $isCombo > 0){
				System::apiError('Sản phẩm bạn chọn mua không nằm trong danh sách cho phép.');
			}
            $cart = Cart::multiOrder($card, $gift,$options);
            if ($cart) {
                if($cart['done']==1){
                    System::apiSuccess(array('link' => '/card/'.$token.'?page=account','redirect'=>$cart['data']['cart']['redirectLink']));
                }else{
                    System::apiError($cart['msg']);
                }
            }else{
                System::apiError('Hệ thống không đặt mua được quà vui lòng thử lại trong giây lát.');
            }

        }
        System::apiError('Có lỗi xảy ra. Vui lòng thử lại sau.');
    }

    function saveToPhone(){
        System::apiError('No data found');
        $id_loyal_order = Url::getParamInt('id_loyal_order',0);
        $yourPhone = Url::getParam('yourPhone','');
        $token = Url::getParam('token','');
        if($id_loyal_order>0&&$yourPhone!=''){
            # Kiểm tra Token
            $card = Card::getByToken($token);
            if ($card) {
                $cart_detail = DB::query("SELECT * FROM ".T_CART_DETAIL." WHERE cart_id=".$id_loyal_order);
                if($cart_detail){
                    while ($row = $cart_detail->fetch(PDO::FETCH_ASSOC)){
						$sentGift = BalanceVer3::checkSendGift($row['id']);
						//dã gửi sms cho người khác hay chưa
						if($sentGift > 0){
							System::apiError('Quà này đã được tặng rồi');
						}
                        $link = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
                        $link_sort = Shorten::build($link);
                        $arrInsert = array(
                            'cat_id' => $row['cart_id'],
                            'cart_detail_id' => $row['id'],
                            'app_id' => $row['app_id'],
                            'phone' => $yourPhone,
                            'content' => 'Chuc mung quy khach da dat mua thanh cong mon qua tu URBOX.VN. Bam vao link: '.$link_sort.' de nhan qua.',
                            'sent' => 0,
                            'created' => TIME_NOW
                        );
                        DB::insert(T_SMS_SENT,$arrInsert);
                    }
                }
                #Them Customer
                $checkCus = DB::fetch("SELECT * FROM ".T_CUSTOMER." WHERE phone='".$yourPhone."'");
                if(!$checkCus){
                    #Them 1 ban ghi moi
                    $checkCus = Customer::add(array(
                        'first_name' =>$yourPhone,
                        #'email' => $yourPhone.'@urbox.vn',
                        'phone' => $yourPhone,
                    ));
                }
                #Cap nhat vao don hang va loyal-order
                DB::update(T_CART,array('customer_id'=>$checkCus['id'],'phone'=>$yourPhone,'email'=>$checkCus['email']),'id='.$id_loyal_order);

                System::apiSuccess();

            }
        }
        System::apiError('No data found');
    }

    function sendAsGift(){
        System::apiError('No data found');
        $id_loyal_order = Url::getParamInt('id_loyal_order',0);
        $friendPhone = Url::getParam('friendPhone','');
        $friendName = Url::getParam('friendName','');
        $friendMessage = Url::getParam('friendMessage','');
        $token = Url::getParam('token','');

        if($id_loyal_order>0&&$friendPhone!=''){
            # Kiểm tra Token
                $card = Card::getByToken($token);
                if ($card) {
                    #Kiem tra loyal Order
                    $cart_detail = DB::query("SELECT * FROM ".T_CART_DETAIL." WHERE cart_id=".$id_loyal_order);
                    if($cart_detail){
                        while ($row = $cart_detail->fetch(PDO::FETCH_ASSOC)){
							$sentGift = BalanceVer3::checkSendGift($row['id']);
							//dã gửi sms cho người khác hay chưa
							if($sentGift > 0){
								System::apiError('Quà này đã được tặng rồi');
							}
                            $link = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
                            $link_sort = Shorten::build($link);
                            $content = StringLib::stripUnicode(StringLib::truncateHtml($friendName,10)).' da tang quy khach mon qua tu URBOX.VN voi loi nhan "'.StringLib::stripUnicode(StringLib::truncateHtml($friendMessage,50)).'". Bam vao link: '.$link_sort.' de nhan qua';
                            $arrInsert = array(
                                'cat_id' => $row['cart_id'],
                                'cart_detail_id' => $row['id'],
                                'app_id' => $row['app_id'],
                                'phone' => $friendPhone,
                                'content' => strlen($content)<=180?$content:(StringLib::stripUnicode(StringLib::truncateHtml($friendName,10)).' da tang quy khach mon qua tu URBOX.VN . Bam vao link: '.$link_sort.' de nhan qua.'),
                                'sent' => 0,
                                'created' => TIME_NOW
                            );
                            DB::insert(T_SMS_SENT,$arrInsert);
                        }

                    }
                    #Them Customer
                    $checkRecivergift = DB::fetch("SELECT * FROM ".T_GIFT_RECEIVER." WHERE phone='".$friendPhone."'");
                    if(!$checkRecivergift){
                        #Them 1 ban ghi moi
                        $checkRecivergift = ReceiverCore::add(array(
                            'first_name' => $friendPhone,
                            #'email' => $friendPhone.'@urbox.vn',
                            'phone' => $friendPhone,
                        ));

                    }

                    #Cap nhat vao don hang va loyal-order
                    DB::update(T_CART,array('receiver_id'=>$checkRecivergift['id'],'message'=>$friendMessage),'id='.$id_loyal_order);

                    System::apiSuccess();

                }
        }
        System::apiError('No data found');
    }

}