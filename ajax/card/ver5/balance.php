<?php


class balancev
{
    function __construct()
    {
        $code = System::$data['url']['query'][3];
        if (!method_exists($this, $code)) {
            $this->index();
        }
        $this->$code();
    }
    function index()
    {
        System::apiSuccess('Gọi thành công Ajax Index');
    }
    function listGift()
    {
		$token = CookieLib::get('cardNum');$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$token = StringLib::clean_value($token);
        if($token == "")System::apiError('No data found');
        $tokens = Card::getByToken($token);
        if ($tokens) {
            #Gift
            $per_page = Url::getParamInt('per_page', 18);
            $page_num = Url::getParamInt('page_num', 1);
            $isSearch = Url::getParamInt('is_search');
            $type = Url::getParamInt('type',0);

            $pages = [
                'per_page' => $per_page,
                'page_num' => $page_num,
            ];
            $params = [
                "keyword" => Url::getParam('keyword', ''),
				'paging' => Url::getParamInt('paging', 0)
            ];
            if ($type == 9){
                $params["type"] = $type;
            }
            if ($isSearch){
                $data = BalanceVer5::getGiftByKeyword($tokens,Url::getParam('keyword', ''));
            }else{
                $data = BalanceVer5::getGiftByBrand($tokens,$pages,$params);
            }
            System::apiSuccess(array('gifts' => array_values($data)));
        }
        System::apiError('No data found');
    }
	function getGift(){
		$id = Url::getParamInt('id',0);
		$quantity = Url::getParamInt('quantity',1);
		$fee = Url::getParam('fee');
		if(empty($id) || $id <= 0){
			System::apiError('Quà này không tồn tại!');
		}
		if(empty($fee)){
			System::apiError('Dữ liệu không hợp lệ!');
		}
		$gift = Balance::getGift($id);
		$brand = Brand::get($gift['brand_id']);
		$totalBuy = $gift['price'] * $quantity;
		$opsFee = Cart::getOpsFee($fee,$gift['brand_id'],$totalBuy);
		$gift['opsFee'] = $opsFee;
        $gift['brand'] = $brand;
		System::apiSuccess($gift);
	}
}
