<?php


class balancev
{
    function __construct()
    {
        $code = System::$data['url']['query'][3];
        if (!method_exists($this, $code)) {
            $this->index();
        }
        $this->$code();
    }
    function index()
    {
        System::apiSuccess('Gọi thành công Ajax Index');
    }

    function viewGift(){
        $id = Url::getParamInt('id',0);
        if(empty($id) || $id <= 0){
            System::apiError('Quà này không tồn tại!');
        }
        $sql = "SELECT a.id,a.title,a.price,a.avatar,b.note,c.title as brand_name
                FROM ".T_GIFT_DETAIL." a
                LEFT JOIN ". T_GIFT ." g on a.gift_id=g.id
                LEFT JOIN ". T_GIFT_CONTENT ." b on b.gift_id=g.id
                LEFT JOIN ". T_BRAND ." c on a.brand_id=c.id
                
                WHERE a.id=".$id;
        $gift = DB::fetch($sql);
        if(!empty($gift)) {
            $images = MediaUrl::fromImageTitle($gift['avatar']);
            $gift['image'] = isset($images[320]) ? $images[320] : '';
            $gift['price'] = number_format($gift['price'],0,",",".");
            $gift['note'] = html_entity_decode($gift['note']);
        }
        System::apiSuccess($gift);
    }
    function listGift()
    {
        $token = Url::getParam('token', '');
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        if($token == "")System::apiError('No data found');
        $tokens = Card::getByToken($token);
        if ($tokens) {
            #Gift
            $cat_id = Url::getParamInt('cat_id', 0);
            $per_page = Url::getParamInt('per_page', 10);
            $page_num = Url::getParamInt('page_num', 1);
            $pages = [
                'per_page' => $per_page,
                'page_num' => $page_num,
            ];
            $params = [
                "keyword" => Url::getParam('keyword', ''),
                'isPhysical'=> Url::getParamInt('type', 1),
                'orderBy'=> Url::getParam('orderBy', ''),
                'priceRange'=> Url::getParam('priceRange', ''),
                'length' => Url::getParamInt('length', 0),
            ];
            $data = BalanceVer3::getListGifts($tokens,$cat_id,$pages,$params);
            System::apiSuccess(array('gifts' => array_values($data)));
        }
        System::apiError('No data found');
    }
    function listBrand()
    {
        $per_page = Url::getParamInt('per_page', 18);
        $cat_id = Url::getParamInt('cat_id', 0);
        $page_no = Url::getParamInt('page_no', 1);
        $paging = Url::getParam('paging', 1);
        $token = CookieLib::get('cardNum');$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $order = Url::getParam('order');
        $groupby = Url::getParamInt('groupby',0);
		$keyword = Url::getParam('keyword', "");
        $token = StringLib::clean_value($token);
        $card_session = SessionLib::get('auth');
        if ($token == ''  || $card_session['token'] != $token)System::apiError('No data found');
        if($paging == 0) $paging = false;
        else $paging = true;
        
        if(empty($cat_id)) $cat_id = 0;
        if($groupby == 0) {
			$allow_loc = CookieLib::get('C_ALLOW_LOCATION');
			if($allow_loc==1 ) $group_by= false;
			else $group_by = true;
		}else{
			$group_by = true;
		}
		
        $card = Card::getByToken($token);
		$params = [];
		$params['paging'] = $paging;
		$params['cat'] = $cat_id;
		$params['row_per_page'] = $per_page;
		$params['page_no'] = $page_no;
		$params['group_by'] = $group_by;
		$params['keyword'] = $keyword;
		$params['order'] = $order;
        $data = BalanceVer3::listBrands($card,$params);

        System::apiSuccess(array('brands' => array_values($data)));
    }
    function getProvide(){
        $is_city = Url::getParamInt('is_city', 0);
        if($is_city == "0") $is_city = false;
        else $is_city = true;
        $data = BalanceVer3::allProvide($is_city);
        System::apiSuccess($data);
    }
    function setUsed(){
        $id = Url::getParamInt('id', 0);
        $token = Url::getParam('token', null);
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        if($id <=0){
            System::apiError('No data found');
        }
        if($token == ""){
            System::apiError('Token not found');
        }
       $result =  BalanceVer3::updateCart($token,$id);
        if($result != false)  System::apiSuccess(['msg'=>"Success!"]);
        System::apiError('Error when update');
    }

    function saveSurvey(){
        $data = System::$data['post'];
        if(!is_array($data)) System::apiError('Error when save');
        $token = array_shift($data);
		if($token == "")System::apiError('No data found');
        $survey_id = array_shift($data);
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $card = Card::getByToken($token);
        $survey_id = (int) $survey_id;
        if($survey_id <= 0) System::apiError('Error when save');
        if(empty($card)) System::apiError('Error when save');
        $rows = [];
        // foreach từng câu trả lời của khách để lưu vào bảng cus_ques
        // kiểm tra số lượng câu hỏi trong bộ survey  xem có bo nhiêu câu bắt buộc phải trả lời
        // so sánh với kết quả trả lời của người dùng
        // nếu không giống nhau = > người dùng chưa trả lời hết câu hỏi yêu cầu
        // toi mất 2 cái vé xem phim :(((
        $fail = 0;
        $require = 0;
        foreach($data as $type => $item){
            $input = $this->checkType($type);

            $question = Balance::getQuestion((int)$input[1]);
            if(is_array($item)){
                if($question['isRequired'] == 2){
                    $require ++;
                    if( count($item) <= 0) $fail = 1;
                }
                foreach ($item as $value) {
                    $row = [];
                    $row['survey_id'] = $survey_id;
                    $row['card_id'] = $card['id'];
                    $row['question_id'] = $input[1];
                    $row['answer_id'] = $value;
                    $row['content'] = '';
                    $rows[] = $row;
                }
            }else {
                if($question['isRequired'] == 2){
                    $require ++;
                    if( $item == "") $fail = 1;
                }
                $row = [];
                $row['survey_id'] = $survey_id;
                $row['card_id'] = $card['id'];
                $row['question_id'] = $input[1];
                if ($input[0] == 'textarea') {
                    $row['content'] = $item;
                    $row['answer_id'] = 0;
                }
                else{
                    $row['content'] = '';
                    $row['answer_id'] = $item;
                }
                $rows[] = $row;
            }
        }
        
        $survey_require = Balance::countRequire($survey_id);
        if($fail ==0 && $require == $survey_require['qty']) {
            Balance::saveSurvey($rows,$card);
            System::apiSuccess(['token'=>$token]);
        }else
            System::apiError('Bạn cần trả lời hết các câu hỏi được yêu cầu!');
    }
    function getGift(){
        $id = Url::getParamInt('id',0);
		$quantity = Url::getParamInt('quantity',1);
		$fee = Url::getParam('fee');
        if(empty($id) || $id <= 0){
            System::apiError('Quà này không tồn tại!');
        }
		if(empty($fee)){
			System::apiError('Dữ liệu không hợp lệ!');
		}
        $gift = Balance::getGift($id);
		$opsFee = Cart::getOpsFee($fee,$gift['brand_id'],0);
		$gift['opsFee'] = $opsFee;
        System::apiSuccess($gift);
    }

    private function checkType($type){
        $array = explode("-",$type);
        array_shift($array);
        return $array;
    }
    function saveAddress(){
        $token = Url::getParam('token', "");
        $id = Url::getParamInt('id', 0);
        $phone = Url::getParam('phone', '');
        $email = Url::getParam('email', '');
        $city = Url::getParamInt('city', 0);
        $ward = Url::getParam('ward', '');
        $address = Url::getParam('address', '');
        $district = Url::getParamInt('district', 0);
        $full_name = Url::getParam('full_name', '');
        $appName = Url::getParam('appName', 'card');
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		if($token == "")System::apiError('No data found');
        $card = Card::getByToken($token);
        if ($id == 0 || empty($token) || !is_numeric($id)) {
            System::apiError('Không tìm thấy dữ liệu');
        }
        if ($phone == "" || !FunctionLib::is_phone($phone)) {
            System::apiError('Số điện thoại không hợp lệ');
        }
        if ($email == "" || !FunctionLib::is_email($email)) {
            System::apiError('Email không hợp lệ');
        }
        if ($city == 0 || $district == 0 || $ward == "" || $address == "") {
            System::apiError('Không tìm thấy thông tin địa chỉ');
        }
        if ($full_name == "") {
            System::apiError('Họ tên không hợp lệ');
        }
        if ($appName != "card") {
            System::apiError('App không hợp lệ');
        }

        if(empty($card)){
            System::apiError('Token không hợp lệ');
        }
        $entity_receive = [
            "phone" => $phone,
            "email" => $email,
            "city_id" => $city,
            "ward" => $ward,
            "address" => $address,
            "district_id" => $district,
            "fullname" => $full_name
        ];
        $receiver = ReceiverCore::add($entity_receive);
        if($receiver['id']) {
            $entity_add = [
                "card_id" => $card['id'],
                "city_id" => $city,
                "ward_id" => $ward,
                "number" => $address,
                "district_id" => $district,
                "receiver_id" => $receiver['id'],
                "status" => 2
            ];
            ReceiverCore::saveAddress($entity_add);
        }
        System::apiSuccess($id);

    }
}