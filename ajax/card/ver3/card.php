<?php


class cardv
{
    function __construct()
    {
        $code = System::$data['url']['query'][3];
        if (!method_exists($this, $code)) {
            $this->index();
        }
        $this->$code();
    }

    function index()
    {
        System::apiError('ok');
    }

    function checkNumber()
    {
        System::apiError("Số thẻ không tồn tại. Vui lòng thử lại vào ngày mai hoặc gọi hotline để được hỗ trợ.");
        $cardNumber = Url::getParamInt('number', 0);
        $ip = Url::getParam('ip', "");
        $accessTime = CookieLib::get('accessTime');
        $accessTime = System::decrypt($accessTime, CRYPT_KEY);
        if (!Balance::checkCardPrefix($cardNumber)) {
            System::apiError("Thẻ không được phép sử dụng trên link", ['show_popup_content' => 2]);
        }
        if (TIME_NOW - $accessTime > 60) {
            CookieLib::get('requestNum', 0);
        }
        $requestNum = CookieLib::get('requestNum');
        if ($requestNum == "") {
            System::apiError("Số thẻ không tồn tại. Vui lòng thử lại vào ngày mai hoặc gọi hotline để được hỗ trợ.");
        }
        $requestNum += 1;
        CookieLib::set('requestNum', $requestNum);
        if (TIME_NOW - $accessTime < 60 && $requestNum > 3) {
            System::apiError("Bạn đã nhập quá nhiều lần. Vui lòng thử lại sau 1 phút hoặc gọi hotline để được hỗ trợ.");
        }
        if ($requestNum > 3) {
            System::apiError("Bạn đã nhập quá nhiều lần. Vui lòng thử lại vào ngày mai hoặc gọi hotline để được hỗ trợ.");
        }
        $card = Card::getByNumber($cardNumber);
        if (empty($card)) {
            if ($requestNum < 3) {
                System::apiError("Số thẻ không tồn tại. Bạn còn " . (3 - $requestNum) . " lần điền số thẻ, vui lòng kiểm tra lại hoặc gọi hotline để được hỗ trợ.");
            } else {
                System::apiError("Số thẻ không tồn tại. Vui lòng thử lại vào ngày mai hoặc gọi hotline để được hỗ trợ.");
            }
        }
        // $log = Card::lastAction($card['id']);

//        if($log['ip'] != $ip){
//            System::apiError("Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này.");
//        }

        if ($accessTime == "") {
            System::apiError("Số thẻ không tồn tại. Vui lòng thử lại vào ngày mai hoặc gọi hotline để được hỗ trợ.");
        }
        $app = Card::getAppById($card['app_id']);
        $card['app_title'] = isset($app['title']) ? $app['title'] : "";
        $ip_server = Card::clientIp();
        if ($ip != $ip_server) {
            $tags = [
                "error_cat" => Sentry::CAT_LOGIN,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này.")
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này."])
                ->save();
            System::apiError("Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này.");
        }
        if ($card['isLogined'] == 2) {
            $tags = [
                "error_cat" => Sentry::CAT_LOGIN,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];

            Sentry::message("Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này.")
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này."])
                ->save();

            System::apiError("Thẻ đã được sử dụng trên thiết bị khác. Quý khách vui lòng đăng xuất tại mục hỗ trợ để sử dụng thẻ trên thiết bị này.");
        }
        CookieLib::set('requestNum', 0, -1);
        CookieLib::set("cardNum", $card['token'], TIME_NOW + 60 * 60 * 24 * 15, false);
        if ($card['needActive'] == 1) {
            Card::setLogin($card['id'], IS_YES);
        }

        System::apiSuccess(['token' => $card['token']]);
    }

    function createOtp()
    {
        //token: token,phone:phone_number
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $phone = Url::getParamInt('phone', 0);
        $token = StringLib::clean_value($token);
        $phoneEncrypt = System::encrypt($phone, CRYPT_KEY);
        if ($token == '') {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];

            Sentry::message('Có lỗi xảy ra vui lòng liên hệ ban quản trị.')
                ->setUser(['phone' => $phone])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => 'Lỗi xảy ra khi không lấy được thông tin token khách gửi lên'])
                ->save();

            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($phone == '') {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];

            Sentry::message('Bạn vui lòng nhập số điện thoại.')
                ->setUser(['token' => "$token", 'phone' => $phone])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Bạn vui lòng nhập số điện thoại."])
                ->save();

            System::apiError('Bạn vui lòng nhập số điện thoại.');
        }
        if (!FunctionLib::is_phone($phone)) {

            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];

            Sentry::message('Số điện thoại bạn nhập không đúng định dạng.')
                ->setUser(['token' => "$token", 'phone' => $phone])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Số điện thoại bạn nhập không đúng định dạng."])
                ->save();
            System::apiError('Số điện thoại bạn nhập không đúng định dạng.', array('errType' => 2));
        }
        $card = Card::getByToken($token);
        if (empty($card)) {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_URBOX,
                "error_by_team" => Sentry::BY_TECH,
            ];

            Sentry::message('Không tìm thấy thẻ.')
                ->setUser(['token' => "$token", 'phone' => $phone])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Không tìm thấy thẻ."])
                ->save();
            System::apiError('Không tìm thấy thẻ.', array('errType' => 0));
        }
        $app = Card::getAppById($card['app_id']);
        $card['app_title'] = isset($app['title']) ? $app['title'] : "";

        if ($card['type'] == 1 && $card['status'] != IS_OFF) {

            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_URBOX,
                "error_by_team" => Sentry::BY_OPS,
            ];

            Sentry::message('Thẻ đã kích hoạt hoặc đang bị khóa')
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ đã kích hoạt hoặc đang bị khóa."])
                ->save();
            System::apiError('Thẻ đã kích hoạt hoặc đang bị khóa.', array('errType' => 0));
        }
        if ($card['type'] == 4 && !in_array($card['status'], [IS_OFF, IS_ON])) {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_URBOX,
                "error_by_team" => Sentry::BY_OPS,
            ];

            Sentry::message('Thẻ đã kích hoạt hoặc đang bị khóa')
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ đã kích hoạt hoặc đang bị khóa."])
                ->save();
            System::apiError('Thẻ đã kích hoạt hoặc đang bị khóa.', array('errType' => 0));
        }

        #Ngay 8 - 8 - 2019
        #Doi voi so Dien thoai se ma hoa

        if ($card['phone'] != "" && $card['pin'] != "" && $card['phone'] != $phoneEncrypt) {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_URBOX,
                "error_by_team" => Sentry::BY_OPS,
            ];

            Sentry::message('Số điện thoại bạn nhập không đúng với số điện thoại đã đăng ký')
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Số điện thoại bạn nhập không đúng với số điện thoại đã đăng ký."])
                ->save();
            System::apiError('Số điện thoại bạn nhập không đúng với số điện thoại đã đăng ký.', array('errType' => 3));
        }
        if (!Card::isWhitelist(['app_id' => $card['app_id'], 'phone' => $phone, 'phone_sub' => substr($phone, 4)])) {
            //System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
            $inWhiteList = 0;
        } else {
            $inWhiteList = 1;
        }
        #END
        if ($app) {
            if (isset($app['needHasWhitelist']) && $app['needHasWhitelist'] == 2) {
                if ($inWhiteList == 0) {
                    $tags = [
                        "error_cat" => Sentry::CAT_LOGIN,
                        "error_from" => Sentry::FROM_CUSTOMER,
                        "error_by_team" => Sentry::BY_OPS,
                    ];

                    Sentry::message('Số điện thoại bạn nhập không thuộc chương trình này')
                        ->setUser($card)
                        ->setTag($tags)
                        ->setConText(Sentry::CONTEXT_PARAMS, [
                            "GET" => $_GET,
                            "POST" => $_POST
                        ])
                        ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Số điện thoại bạn nhập không thuộc chương trình này."])
                        ->save();

                    System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
                }
            }
        } else {
            $tags = [
                "error_cat" => Sentry::CAT_LOGIN,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_OPS,
            ];

            Sentry::message('Số điện thoại bạn nhập không thuộc chương trình này')
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Số điện thoại bạn nhập không thuộc chương trình này."])
                ->save();

            System::apiError('Thẻ không thuộc chương trình nào.', array('errType' => 3));
        }
        if ($card['needActive'] == IS_YES && $card['needExactPhone'] == IS_YES) {
            #Neu can actice và sdt chi dc nhap trong topup
            $preAcc = DB::fetch("SELECT * FROM " . T_CARD_PREACC . " WHERE phone='" . $phoneEncrypt . "' AND app_id=" . $card['app_id']);
            if (empty($preAcc)) {

                $tags = [
                    "error_cat" => Sentry::CAT_LOGIN,
                    "error_from" => Sentry::FROM_CUSTOMER,
                    "error_by_team" => Sentry::BY_OPS,
                ];
                Sentry::message('Số điện thoại không tồn tại.')
                    ->setUser($card)
                    ->setTag($tags)
                    ->setConText(Sentry::CONTEXT_PARAMS, [
                        "GET" => $_GET,
                        "POST" => $_POST
                    ])
                    ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Trạng thái thẻ : " . System::decrypt($card['phone'])])
                    ->save();

                System::apiError('Số điện thoại không tồn tại.');
            }
        }

        if ($card && is_numeric($phone)) {
            $newOtp = Card::createOtp();
            if ($card['needActive'] == 1 && $card['phone'] != "" && $card['pin'] != "" && $card['status'] == CARD_DEACTIVATE) {
                $encrypt_session = System::encrypt($token, CRYPT_KEY);
                CookieLib::set('auth', $encrypt_session, TIME_NOW + (60 * 60));
                DB::update(T_CARD, ['phone_sub' => substr($phone, 4), 'status' => CARD_ACTIVE, 'status_hash' => Card::statusHash($card['token'], $card['id'], CARD_ACTIVE)], "id={$card['id']}");
                Card::updateHash($card['id']);
                System::apiError('oK.', array('errType' => 4));
            }
            $_SESSION['otp_phone'] = $phone;
            Card::active($card, $phoneEncrypt, CARD_DEACTIVATE, $newOtp);
            DB::update(T_CARD, ['phone_sub' => substr($phone, 4)], "id={$card['id']}");
            Card::sendOTP($card, $phone, $newOtp);
            System::apiSuccess($phone);
        } else {
            $tags = [
                "error_cat" => Sentry::CAT_LOGIN,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_OPS,
            ];
            Sentry::message('Số điện thoại hoặc token không đúng!.')
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Trạng thái thẻ : " . System::decrypt($card['phone'])])
                ->save();
            System::apiError("Số điện thoại hoặc token không đúng!");
        }
    }

    function validateOtp()
    {
        $phone = Url::getParam('phone', '');
        $otp = Url::getParamInt('otp', '');
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);
        if ($token == '') {
            SentryMessage::logOtp(['token' => $token, 'phone' => $phone], ['token' => $token, "content" => "Có lỗi xảy ra vui lòng liên hệ ban quản trị.", "otp" => $otp, "phone" => $phone, "description" => "Không tìm thấy thông tin token khách gửi lên "]);
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($otp == '') {

            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message('Bạn chưa nhập mã xác thực')
                ->setUser(["otp" => $otp, "phone" => $phone, 'token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "otp" => $otp, "phone" => $phone, 'token' => $token
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => 'Bạn chưa nhập mã xác thực.'])
                ->save();

            System::apiError('Bạn chưa nhập mã xác thực.');
        }
        if ($phone == '') {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Bạn vui lòng nhập số điện thoại.")
                ->setUser(["otp" => $otp, "phone" => $phone, 'token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "otp" => $otp, "phone" => $phone, 'token' => $token
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Bạn vui lòng nhập số điện thoại."])
                ->save();


            System::apiError('Bạn vui lòng nhập số điện thoại.');
        }
        if (!FunctionLib::is_phone($phone)) {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Số điện thoại bạn nhập không đúng định dạng.")
                ->setUser(["otp" => $otp, "phone" => $phone, 'token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "otp" => $otp, "phone" => $phone, 'token' => $token
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Số điện thoại bạn nhập không đúng định dạng."])
                ->save();

            System::apiError('Số điện thoại bạn nhập không đúng định dạng.', array('errType' => 2));
        }
        #Ngay 8 - 8 - 2019
        #Doi voi so Dien thoai se ma hoa
        $phoneEncrypt = System::encrypt($phone);
        #END
        $encrypt_token = System::encrypt($token);
        $card = DB::fetch("SELECT id,active_time,phone,pin,app_id,issue_id,token,status,issue_id,needActive,gift_id FROM " . T_CARD . " WHERE token='{$encrypt_token}' AND otp='$otp' and status in(1,2) ORDER BY id DESC LIMIT 1");
        // triển
        if ($card['id'] > 0 && in_array($card['status'], [CARD_DEACTIVATE, CARD_ACTIVE])) {
            $app = Card::getAppById($card['app_id']);
            $card['app_title'] = isset($app['title']) ? $app['title'] : "";
            if ($card['phone'] == "" && $card['pin'] == "" && $phone != $_SESSION['otp_phone']) {
                $tags = [
                    "error_cat" => Sentry::CAT_OTP,
                    "error_from" => Sentry::FROM_CUSTOMER,
                    "error_by_team" => Sentry::BY_TECH,
                ];
                Sentry::message("Số điện thoại bạn nhập khác với số điện thoại nhận OTP.")
                    ->setUser(["otp" => $otp, "phone" => $phone, 'token' => $token])
                    ->setTag($tags)
                    ->setConText(Sentry::CONTEXT_PARAMS, [
                        "GET" => $_GET,
                        "POST" => $_POST,
                        "otp" => $otp, "phone" => $phone, 'token' => $token
                    ])
                    ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Số điện thoại bạn nhập khác với số điện thoại nhận OTP."])
                    ->save();

                System::apiError('Số điện thoại bạn nhập khác với số điện thoại nhận OTP.');
            }
            if (!Card::isWhitelist(['app_id' => $card['app_id'], 'phone' => $phone, 'phone_sub' => substr($phone, 4)])) {
                $useWL = 1;
                //System::apiError('Số điện thoại bạn nhập không thuộc chương trình này.', array('errType' => 3));
            } else $useWL = 2;

            if ($card['phone'] == "" && $card['pin'] == "" && $phone == $_SESSION['otp_phone']) {
                DB::update(T_CARD, ['phone' => $phoneEncrypt, 'phone_sub' => substr($phone, 4)], "id={$card['id']}");
            }

            if (TIME_NOW - $card['active_time'] <= 60) {
                if ($card['phone'] != "" && $card['pin'] == "" && $phone == $_SESSION['otp_phone'] && ($useWL == 2 && $card['needActive'] == 1 || $useWL == 1 && $card['needActive'] == 2)) {
                    $encrypt_session = System::encrypt($token, CRYPT_KEY);
                    CookieLib::set('auth', $encrypt_session, TIME_NOW + (60 * 60));
                }
                if ($card['needActive'] == 2 && $card['pin'] != "" && $card['status'] == CARD_DEACTIVATE) {
                    DB::update(T_CARD, ['pin' => ''], "id={$card['id']}");
                }
                $countCard = DB::query("SELECT id,number,money,app_id,token from " . T_CARD . " where phone='{$phoneEncrypt}'")->fetchAll(PDO::FETCH_ASSOC);
                if (count($countCard) > MAX_CARD_PER_PHONE && $useWL == 1) {
//                    Card::changeGiftSetFraud($card);
                    $appLog = $app;
                    unset($appLog['access_key']);
                    SentryMessage::logMaxCard($card, $appLog);
                }
                Card::active($card, $phoneEncrypt, CARD_ACTIVE);
                DB::update(T_CARD, ['pin' => '', 'phone_sub' => substr($phone, 4)], "id={$card['id']}");
                Card::updateHash($card['id']);
                System::apiSuccess(['link' => "/card"]);
            } else {
                $tags = [
                    "error_cat" => Sentry::CAT_OTP,
                    "error_from" => Sentry::FROM_CUSTOMER,
                    "error_by_team" => Sentry::BY_TECH,
                ];
                Sentry::message("Mã xác thực đã hết hạn")
                    ->setUser($card)
                    ->setTag($tags)
                    ->setConText(Sentry::CONTEXT_PARAMS, [
                        "GET" => $_GET,
                        "POST" => $_POST,
                        "otp" => $otp, "phone" => $phone, 'token' => $token
                    ])
                    ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Khách bấm xác nhận OTP sau 60s từ lúc nhận được OTP."])
                    ->save();

                System::apiError('Mã xác thực đã hết hạn');
            }
        } else {
            $tags = [
                "error_cat" => Sentry::CAT_OTP,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Mã xác thực không chính xác.")
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "otp" => $otp, "phone" => $phone, 'token' => $token
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Mã xác thực không chính xác."])
                ->save();

            System::apiError('Mã xác thực không chính xác ');
        }


    }

    function validatePin()
    {
        $pin = Url::getParam('pin', '');
        $pin2 = Url::getParam('pin2', '');
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);

        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (is_integer($pin)) {
            System::apiError('Mã PIN phải là số.');
        }
        if ($pin2 == '') {

            System::apiError('Bạn vui lòng nhập xác nhận lại mã PIN.');
        }
        if ($pin2 != $pin) {

            System::apiError('Mã PIN không giống nhau.');
        }
        $isVailPin = Card::strongPin($pin);
        if (!$isVailPin) {
            System::apiError('Mã PIN không đủ mạnh. Vui lòng nhập mã PIN khác!');
        }
        $tokenCard = Card::getByToken($token);
        if (empty($tokenCard)) {
            $tags = [
                "error_cat" => Sentry::CAT_PIN,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Thẻ không tồn tại.")
                ->setUser(["pin" => $pin, "pin2" => $pin2, 'token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "pin" => $pin, "pin2" => $pin2, 'token' => $token
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ không tồn tại."])
                ->save();
            System::apiError('Thẻ không tồn tại.');
        }
        if ($tokenCard['pin'] != "") {
            $tags = [
                "error_cat" => Sentry::CAT_PIN,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Thẻ của bạn đã được kích hoạt. Bạn không thể sử dụng chức năng này nữa.")
                ->setUser($tokenCard)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "pin" => $pin, "pin2" => $pin2, 'token' => $token

                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ của bạn đã được kích hoạt. Bạn không thể sử dụng chức năng này nữa.."])
                ->save();
            System::apiError('Thẻ của bạn đã được kích hoạt. Bạn không thể sử dụng chức năng này nữa.');
        }
        if ($tokenCard['id'] > 0) {
            $db_pin = Card::encodePin($pin);
            $hash_pin = Card::hashPin($tokenCard['id'], $db_pin);
            $row = array(
                'pin' => $db_pin,
                'pin_hash' => $hash_pin
            );
            DB::update(T_CARD, $row, "id='{$tokenCard['id']}'");
        }
        $logData = [
            'done' => 1,
            'ip' => System::ip(),
            'token' => $token,
            'userID' => System::encrypt($pin),
            'agent' => $_SERVER['HTTP_USER_AGENT']
        ];
        Card::logCard($token, CARD_ACTION_LOGIN, json_encode($logData), 2);
        $encrypt_session = System::encrypt($token, CRYPT_KEY);
        CookieLib::set('auth', $encrypt_session, TIME_NOW + (60 * 60 * 24 * 15));
        Card::setLogin($tokenCard['id'], IS_YES);
        System::apiSuccess(['link' => "/card"]);
    }

    //kiểm tra sdt ở màn hình reset pass
    function checkPhone()
    {
        $phone = Url::getParam('phone', '');
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($phone == '') {
            System::apiError('Bạn chưa nhập số điện thoại.');
        }
        $encrypt_phone = System::encrypt($phone, CRYPT_KEY);
        $encrypt_token = System::encrypt($token, CRYPT_KEY);
        $tokenCard = DB::fetch("SELECT id FROM " . T_CARD . " WHERE token='{$encrypt_token}' and needActive = 2 and phone='$encrypt_phone'");
        if (empty($tokenCard)) {
            System::apiError('Số điện thoại không chính xác.');
        }
        CookieLib::set(System::encrypt($tokenCard['id'], CRYPT_KEY), System::encrypt(TIME_NOW));
        System::apiSuccess(['data' => "ok"]);
    }

    function changePin()
    {
        $pin = Url::getParam('pin', '');
        $pin2 = Url::getParam('pin2', '');
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (is_integer($pin)) {
            System::apiError('Mã PIN phải là số.');
        }
        if ($pin2 == '') {
            System::apiError('Bạn vui lòng nhập xác nhận lại mã PIN.');
        }
        if ($pin2 != $pin) {
            System::apiError('Mã Pin không trùng khớp.');
        }
        $isVailPin = Card::strongPin($pin);
        if (!$isVailPin) {
            System::apiError('Mã PIN không đủ mạnh. Vui lòng nhập mã PIN khác!');
        }
        System::apiError('Tính năng đang phát triển!');
        $auth = CookieLib::get('auth');
        $auth = System::decrypt($auth, CRYPT_KEY);
        $tokenCard = Card::getByToken($token);
        $requestPin = CookieLib::get(System::encrypt($tokenCard['id'], CRYPT_KEY));
        $requestPin = System::decrypt($requestPin, CRYPT_KEY);
        if ($requestPin == "" || (int)$requestPin < TIME_NOW - 60 || (int)$requestPin > TIME_NOW) {
            System::apiError('Bạn cần xác nhận số điện thoại trước khi sử dụng chức năng này.');
        }

        if (empty($tokenCard)) {
            System::apiError('Thẻ không tồn tại.');
        }
        if ($tokenCard['needActive'] != IS_YES) {
            System::apiError('Thẻ của bạn không sử dụng được chức năng này.');
        }
        if ($tokenCard['pin'] == "") {
            System::apiError('Thẻ của bạn chưa được kích hoạt. Bạn không thể sử dụng chức năng này nữa.');
        }
        if ($tokenCard['id'] > 0) {
            $db_pin = Card::encodePin($pin);
            $hash_pin = Card::hashPin($tokenCard['id'], $db_pin);
            $row = array(
                'pin' => $db_pin,
                'pin_hash' => $hash_pin
            );
            DB::update(T_CARD, $row, "id='{$tokenCard['id']}'");
        }

        System::apiSuccess(['link' => "/card"]);
    }

    function login()
    {
        $pin = Url::getParam('pin', '');
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (is_integer($pin)) {
            System::apiError('Mã PIN phải là số.');
        }
        $login = Card::login($token, $pin);
        if ($login) {
            $login['token'] = $token;
            SessionLib::set('auth', $login);
            System::apiSuccess(['link' => "/card"]);
        } else {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
    }

    function checkUPin()
    {
        $pin = Url::getParam('pin', '');
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        if ($pin == '') {
            System::apiError('Bạn chưa nhập mã PIN.');
        }
        if (strlen($pin) != 6 && strlen($pin) != 7) {
            System::apiError('Mã PIN không hợp lệ.');
        }
        $login = Card::login($token, $pin, true);
        if ($login) {
            System::apiSuccess(['link' => "/card"]);
        } else {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }

    }

    function logout()
    {
        $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $token = StringLib::clean_value($token);
        if ($token == '') {
            System::apiError('Có lỗi xảy ra vui lòng liên hệ ban quản trị.', array('errType' => 0));
        }
        $token = strip_tags($token);
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $auth = CookieLib::get('auth');
        $db_token = System::decrypt($auth, CRYPT_KEY);
        if ($db_token != $token) {
            //System::apiError("Token không hợp lệ. Vui lòng đăng nhập lại");
        }
        $tokenCard = Card::getByToken($db_token);
        if ($tokenCard['id'] > 0) {
            Card::setLogin($tokenCard['id'], IS_NO);
        }
        CookieLib::set('c_qty', '', -1);
        CookieLib::set('cardNum', '', -1);
        CookieLib::set('auth', '', -1);
        CookieLib::set('accessTime', '', -1);
        CookieLib::set('requestNum', '', -1);
        System::apiSuccess(['link' => "/card"]);

        //else {
        // System::apiError('Tài khoản này không tồn tại.');
        //  }
    }

    function listGifts()
    {

        $token = Url::getParam('token', '');
        if ($token == "") System::apiError("Token không hợp lệ.");
        $card = Card::getByToken($token);
        if ($card) {
            #Gift
            $per_page = Url::getParamInt('per_page', 5);
            $city = Url::getParamInt('city', 0);
            $cat_id = Url::getParamInt('cat_id', 0);
            $page_no = Url::getParamInt('page_no', 1);
            $id_gift_set = $card['gift_id'] > 0 ? $card['gift_id'] : CGlobal::$idGiftSetDefault;
            $param = array();
            if ($cat_id > 0) {
                $cat_arr[$cat_id] = $cat_id;
                $catData = DB::query("SELECT * FROM " . T_CATEGORY . " WHERE status=1 AND parent_id=" . $cat_id);
                if ($catData) {
                    while ($rw = $catData->fetch(PDO::FETCH_ASSOC)) {
                        $cat_arr[$rw['id']] = $rw['id'];
                    }
                }
                if (!empty($cat_arr)) {
                    $param[] = 'cat_id IN(' . implode(',', $cat_arr) . ')';
                }
            }
            $param[] = 'code_quantity>0';
            $param[] = 'status=1';

            $id_gift_detail = array();

            #Lay them phan chi ban theo GIFT_SET
            $getSiteGift = DB::query("SELECT * FROM " . T_GIFT_SET . " WHERE gift_id=" . $id_gift_set);

            if ($getSiteGift) {
                while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)) {
                    $id_gift_detail[$row['gift_detail_id']] = $row['gift_detail_id'];
                }
                if (!empty($id_gift_detail)) {
                    $param[] = " id IN (" . implode(',', $id_gift_detail) . ")";
                }
            }
            #loc theo LOCATION
            if ($city > 0) {
                $giftId2 = array();
                if (!empty($id_gift_detail)) {
                    $re = DB::query("SELECT * FROM " . T_GIFT_CITY . " WHERE (city_id=" . $city . " OR city_id=0) AND gift_detail_id IN (" . implode(',', $id_gift_detail) . ")");

                } else {
                    $re = DB::query("SELECT * FROM " . T_GIFT_CITY . " WHERE city_id=" . $city . " OR city_id=0");
                }
                if ($re) {
                    while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                        $giftId2[$row['gift_detail_id']] = $row['gift_detail_id'];
                    }
                    if (!empty($giftId2)) {
                        $param[] = " id IN (" . implode($giftId2, ',') . ")";
                    } else {
                        $param[] = " id = 0 ";
                    }
                }
            }
            $condition = FunctionLib::addCondition($param, true);

            $sql = "SELECT * FROM " . T_GIFT_DETAIL . $condition . " ORDER BY id DESC";
            $re = Pagging::query($sql, $per_page, $page_no);
            $data = array();
            if ($re && Pagging::$totalPage >= $page_no) {
                $arr_brand_id = array();
                $arr_gift_id = array();
                while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                    $images = MediaUrl::fromImageTitle($row['avatar']);
                    $temp = array(
                        'id' => $row['id'],
                        'brand_id' => $row['brand_id'],
                        'cat_id' => $row['cat_id'],
                        'gift_id' => $row['gift_id'],
                        'title' => $row['title'],
                        'price' => $row['price'],
                        'view' => $row['view'],
                        'link' => '/card/' . $card['token'] . '/' . $row['id'],
                        'image' => isset($images[640]) ? $images[640] : ''
                    );
                    $arr_brand_id[$row['brand_id']] = $row['brand_id'];
                    $arr_gift_id[$row['gift_id']] = $row['gift_id'];
                    $data[$row['id']] = $temp;
                }

                System::apiSuccess(array('gifts' => array_values($data)));

            }
        }
        System::apiError('No data found');
    }

    /**
     * Đặt mua
     */
    function buy()
    {
        $token = Url::getParam('token', '');
        $gift = Url::getParam('gift', "");
        $phone = Url::getParam('phone', "");
        $identity = Url::getParam('identity', "");
        $address = Url::getParamInt('address_id', 0);
        if (empty($token)) $token = CookieLib::get('cardNum');
        $token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $initValue = [
            "identity" => $identity,
            'token' => $token,
            "address_id" => $address,
            'gift' => $gift,
            'phone' => $phone
        ];

        $token = StringLib::clean_value($token);
        $card_session = SessionLib::get('auth');
        if ($token == '' || !$card_session || $card_session['token'] != $token) {

            $tags = [
                "error_cat" => Sentry::CAT_REDEEM,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Token không được để trống")
                ->setUser(['token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "token" => $token,
                    'token_session' => isset($card_session) ?$card_session['token']: ''
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Token không được để trống"])
                ->save();

            System::apiError('Token không được để trống.');
        }
        $lifeKey = 'balance:buy:user-'.$token;
        $isLife = URedis::get($lifeKey);
        URedis::setEx($lifeKey, true, 1, 3);
        if($isLife){
            System::apiError('Giao dịch của quý khách đang được xử lý. Quý khách vui lòng chờ trong giây lát!');
        }
        if ($phone == 0) $phone = "";
        if ($phone != "" && !FunctionLib::is_phone($phone)) {
            SentryMessage::logCart(['token' => $token], "Số điện thoại bạn nhập không đúng định dạng.", ['content' => 'Số điện thoại bạn nhập không đúng định dạng.'] + $initValue);
            System::apiError('Số điện thoại bạn nhập không đúng định dạng.', array('errType' => 3));
        }
        if ($identity != '' && ((strlen($identity) != 8 && strlen($identity) != 9 && strlen($identity) != 12) || !preg_match('/^[a-zA-Z0-9]+$/', $identity))) {
            SentryMessage::logCart(['token' => $token], "Số chứng minh thư bạn nhập không đúng định dạng", ['content' => 'Số chứng minh thư bạn nhập không đúng định dạng.'] + $initValue);
            $tags = [
                "error_cat" => Sentry::CAT_REDEEM,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Token không được để trống")
                ->setUser(['token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                    "token" => $token,
                    'token_session' => $card_session['token']
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Token không được để trống"])
                ->save();
            System::apiError('Số chứng minh thư bạn nhập không đúng định dạng.', array('errType' => 3));
        }
        $card = Card::getByToken($token);

        if (empty($card) || $card['status'] != CARD_ACTIVE) {
            SentryMessage::logCart(['token' => $token], "Thẻ không hợp lệ", ['content' => 'Thẻ không hợp lệ.', 'description' => "Trạng thái thẻ ko phải là đang kích hoạt"] + $initValue);
            $tags = [
                "error_cat" => Sentry::CAT_REDEEM,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Thẻ không hợp lệ")
                ->setUser(['token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Trạng thái thẻ ko phải là đang kích hoạt hoặc không tìm thấy thông tin thẻ"])
                ->save();
            System::apiError('Thẻ không hợp lệ.');
        }
        if (!in_array($card['version'], ['ver3', 'ver6', 'ver8'])) {
            System::apiError('Version thẻ không hợp lệ.');
        }
        $app = Card::getAppById($card['app_id']);
        $card['app_title'] = isset($app['title']) ? $app['title'] : "";

        if ($card['expired_time'] > 0 && $card['expired_time'] < TIME_NOW) {
            $date = date('d/m/Y', $card['expired_time']);
            $tags = [
                "error_cat" => Sentry::CAT_REDEEM,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Thẻ hết hạn sử dụng")
                ->setUser(['token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ của bạn đã hết hạn sử dụng vào ngày $date. Bạn chỉ có thể xem các quà đã đổi tại mục \"Quà của tôi\"", 'expired' => $card['expired_time']])
                ->save();

            System::apiError("Thẻ của bạn đã hết hạn sử dụng vào ngày $date. Bạn chỉ có thể xem các quà đã đổi tại mục \"Quà của tôi\"");
        }
        if ($card['wallet_id'] > 0) {
            $tags = [
                "error_cat" => Sentry::CAT_REDEEM,
                "error_from" => Sentry::FROM_CUSTOMER,
                "error_by_team" => Sentry::BY_TECH,
            ];
            Sentry::message("Thẻ đã được gom vào ví")
                ->setUser(['token' => $token])
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Thẻ đã được đồng bộ qua UrBox App.<br>Quý khách vui lòng sử dụng UrBox App để đổi quà."])
                ->save();

            System::apiError("Thẻ đã được đồng bộ qua UrBox App.<br>Quý khách vui lòng sử dụng UrBox App để đổi quà.");
        }

        if ($card['pin'] != "") {
            if (empty($card_session) || $token != $card_session['token']) {
                if ($card['needActive'] == 1) {
                    CookieLib::set('upin', '');
                }
                $tags = [
                    "error_cat" => Sentry::CAT_REDEEM,
                    "error_from" => Sentry::FROM_CUSTOMER,
                    "error_by_team" => Sentry::BY_TECH,
                ];
                Sentry::message("Bạn cần đăng nhập trước khi đổi quà.")
                    ->setUser($card)
                    ->setTag($tags)
                    ->setConText(Sentry::CONTEXT_PARAMS, [
                        "GET" => $_GET,
                        "POST" => $_POST,
                        "token" => $token,
                        'token_session' => $card_session['token']
                    ])
                    ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Bạn cần đăng nhập trước khi đổi quà."])
                    ->save();
                System::apiError('Bạn cần đăng nhập trước khi đổi quà.');
            }
        }

        if (empty($gift)) {
            $tags = [
                "error_cat" => Sentry::CAT_REDEEM,
                "error_from" => Sentry::FROM_URBOX,
                "error_by_team" => Sentry::BY_OPS,
            ];
            Sentry::message("Bạn chưa chọn quà")
                ->setUser($card)
                ->setTag($tags)
                ->setConText(Sentry::CONTEXT_PARAMS, [
                    "GET" => $_GET,
                    "POST" => $_POST,
                ])
                ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Bạn chưa chọn quà"])
                ->save();
            System::apiError('Bạn chưa chọn quà.');
        }

        if ($card) {
            $gift = [array_shift($gift)];
            $gift_ids = implode(",", array_column($gift, "id"));

            foreach ($gift as &$ig) {
                $ig['quantity'] = (int)$ig['quantity'];
                if ($ig['quantity'] <= 0) $ig['quantity'] = 1;
                if ($ig['quantity'] > 10) $ig['quantity'] = 10;
            }
            $giftDetail = DB::fetch_all("SELECT id,type,gift_id FROM " . T_GIFT_DETAIL . " WHERE id in($gift_ids)");
            $needRedirect = [];
            $giftParent = [];
            $isCombo = 0;
            foreach ($giftDetail as $item) {
                $giftParent[] = $item['gift_id'];
                if ($item['type'] == GIFT_TYPE_RECEIVER) {
                    $needRedirect[] = $item['gift_id'];
                    $gift[0]["quantity"] = 1;
                }
                if ($item['type'] == GIFT_TYPE_COMBO) {
                    $isCombo++;
                }
            }
            $giftTopup = Gift::isTopupGift($giftParent);
            if (($card['version'] != 'ver8' && $giftTopup > 0) || $isCombo > 0) {
                $tags = [
                    "error_cat" => Sentry::CAT_REDEEM,
                    "error_from" => Sentry::FROM_CUSTOMER,
                    "error_by_team" => Sentry::BY_TECH,
                ];
                Sentry::message("Sản phẩm bạn chọn mua không nằm trong danh sách cho phép.")
                    ->setUser($card)
                    ->setTag($tags)
                    ->setConText(Sentry::CONTEXT_PARAMS, [
                        "GET" => $_GET,
                        "POST" => $_POST,
                    ])
                    ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Sản phẩm bạn chọn mua không nằm trong danh sách cho phép."])
                    ->save();
                System::apiError('Sản phẩm bạn chọn mua không nằm trong danh sách cho phép.');
            }
            if(in_array($card['version'], ['ver6','ver5'])) {
                $countCart = DB::fetch("SELECT count(*) as total from " . T_CART_DETAIL . " WHERE card_id={$card['id']} and status=2 and pay_status=2");
                if ($countCart['total'] > 1) {
                    System::apiError('Bạn chỉ có thể đổi quà duy nhất 1 lần');
                }
            }

            SessionLib::set('auth', $card);
            $cart = Cart::multiOrder($card, $gift, ['phone' => $phone, 'identity' => $identity, 'address' => $address, 'needRedirect' => $needRedirect]);

            if ($cart) {
                if (isset($cart['done']) && $cart['done'] == 1) {
                    // thêm ghi chú giao hàng
                    $note = Url::getParam('note', "");
                    $note = StringLib::clean_value($note);
                    if ($note != "" && !empty($cart['data']['cart']['id']) && $address > 0) {
                        try {
                            DB::update(T_CART, ['delivery_note' => $note], 'id=' . $cart['data']['cart']['id']);
                        } catch (Exception $e) {
                        }
                    }
                    CookieLib::set('c_note', '', -1);
                    $cart_detail_id = 0;
                    if(isset($cart['data']['cart']['code_link_gift'][0])){
                        $cart_detail = $cart['data']['cart']['code_link_gift'][0];
                        $cart_detail_id = isset($cart_detail['cart_detail_id']) ? $cart_detail['cart_detail_id'] : 0;
                    }

                    System::apiSuccess(['cartNo' => $cart['data']['cart']['id'],'cartDetailNo' => $cart_detail_id, 'time' => date('H:i, d/m/Y'), 'redirect' => $cart['data']['cart']['redirectLink']]);
                } else {
                    $type = 1;
                    if (isset($cart['type'])) $type = 3;
                    $tags = [
                        "error_cat" => Sentry::CAT_REDEEM,
                        "error_from" => Sentry::FROM_CUSTOMER,
                        "error_by_team" => Sentry::BY_TECH,
                    ];
                    $message = isset($cart['msg']) ? $cart['msg'] : "Hệ thống không đặt mua được quà vui lòng thử lại sau!";
                    if(isset($card['app_id']) && $card['app_id'] == 717) {
                        Sentry::message("Lỗi đổi quà!")
                            ->setUser($card)
                            ->setTag($tags)
                            ->setConText(Sentry::CONTEXT_PARAMS, [
                                "GET" => $_GET,
                                "POST" => $_POST,
                                "CART" => $cart,
                                "CARD" => $card
                            ])
                            ->setConText(Sentry::CONTEXT_ERROR, ['message' => $message])
                            ->save();
                    }
                    System::apiError($message, ['errType' => $type]);
                }
            } else {
                $tags = [
                    "error_cat" => Sentry::CAT_REDEEM,
                    "error_from" => Sentry::FROM_URBOX,
                    "error_by_team" => Sentry::BY_TECH,
                ];
                Sentry::message("Hệ thống không đặt mua được quà vui lòng thử lại trong giây lát")
                    ->setUser($card)
                    ->setTag($tags)
                    ->setConText(Sentry::CONTEXT_PARAMS, [
                        "GET" => $_GET,
                        "POST" => $_POST,
                        "CART" => $cart,
                        "CARD" => $card
                    ])
                    ->setConText(Sentry::CONTEXT_ERROR, ['message' => "Hệ thống không đặt mua được quà vui lòng thử lại trong giây lát"])
                    ->save();
                System::apiError('Hệ thống không đặt mua được quà vui lòng thử lại trong giây lát.');
            }

        }
        System::apiError('Có lỗi xảy ra. Vui lòng thử lại sau.');
    }

    function saveToPhone()
    {
        System::apiError('No data found');
        $id_loyal_order = Url::getParamInt('id_loyal_order', 0);
        $yourPhone = Url::getParam('yourPhone', '');
        $token = Url::getParam('token', '');
        if ($id_loyal_order > 0 && $yourPhone != '') {
            # Kiểm tra Token
            $card = Card::getByToken($token);
            if ($card) {
                $cart_detail = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id=" . $id_loyal_order);
                if ($cart_detail) {
                    while ($row = $cart_detail->fetch(PDO::FETCH_ASSOC)) {
                        $sentGift = BalanceVer3::checkSendGift($row['id']);
                        //dã gửi sms cho người khác hay chưa
                        if ($sentGift > 0) {
                            System::apiError('Quà này đã được tặng rồi');
                        }
                        $link = LINK_URBOX . 'nhan-qua/' . System::decrypt($row['receive_code']) . '.html';
                        $link_sort = Shorten::build($link);
                        $arrInsert = array(
                            'cat_id' => $row['cart_id'],
                            'cart_detail_id' => $row['id'],
                            'app_id' => $row['app_id'],
                            'phone' => $yourPhone,
                            'content' => 'Chuc mung quy khach da dat mua thanh cong mon qua tu URBOX.VN. Bam vao link: ' . $link_sort . ' de nhan qua.',
                            'sent' => 0,
                            'created' => TIME_NOW
                        );
                        DB::insert(T_SMS_SENT, $arrInsert);
                    }
                }
                #Them Customer
                $checkCus = DB::fetch("SELECT * FROM " . T_CUSTOMER . " WHERE phone='" . $yourPhone . "'");
                if (!$checkCus) {
                    #Them 1 ban ghi moi
                    $checkCus = Customer::add(array(
                        'first_name' => $yourPhone,
                        #'email' => $yourPhone.'@urbox.vn',
                        'phone' => $yourPhone,
                    ));
                }
                #Cap nhat vao don hang va loyal-order
                DB::update(T_CART, array('customer_id' => $checkCus['id'], 'phone' => $yourPhone, 'email' => $checkCus['email']), 'id=' . $id_loyal_order);

                System::apiSuccess();

            }
        }
        System::apiError('No data found');
    }

    function sendAsGift()
    {
        System::apiError('No data found');
        $id_loyal_order = Url::getParamInt('id_loyal_order', 0);
        $friendPhone = Url::getParam('friendPhone', '');
        $friendName = Url::getParam('friendName', '');
        $friendMessage = Url::getParam('friendMessage', '');
        $token = Url::getParam('token', '');

        if ($id_loyal_order > 0 && $friendPhone != '') {
            # Kiểm tra Token
            $card = Card::getByToken($token);
            if ($card) {
                #Kiem tra loyal Order
                $cart_detail = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id=" . $id_loyal_order);
                if ($cart_detail) {
                    while ($row = $cart_detail->fetch(PDO::FETCH_ASSOC)) {
                        $sentGift = BalanceVer3::checkSendGift($row['id']);
                        //dã gửi sms cho người khác hay chưa
                        if ($sentGift > 0) {
                            System::apiError('Quà này đã được tặng rồi');
                        }
                        $link = LINK_URBOX . 'nhan-qua/' . System::decrypt($row['receive_code']) . '.html';
                        $link_sort = Shorten::build($link);
                        $content = StringLib::stripUnicode(StringLib::truncateHtml($friendName, 10)) . ' da tang quy khach mon qua tu URBOX.VN voi loi nhan "' . StringLib::stripUnicode(StringLib::truncateHtml($friendMessage, 50)) . '". Bam vao link: ' . $link_sort . ' de nhan qua';
                        $arrInsert = array(
                            'cat_id' => $row['cart_id'],
                            'cart_detail_id' => $row['id'],
                            'app_id' => $row['app_id'],
                            'phone' => $friendPhone,
                            'content' => strlen($content) <= 180 ? $content : (StringLib::stripUnicode(StringLib::truncateHtml($friendName, 10)) . ' da tang quy khach mon qua tu URBOX.VN . Bam vao link: ' . $link_sort . ' de nhan qua.'),
                            'sent' => 0,
                            'created' => TIME_NOW
                        );
                        DB::insert(T_SMS_SENT, $arrInsert);
                    }

                }
                #Them Customer
                $checkRecivergift = DB::fetch("SELECT * FROM " . T_GIFT_RECEIVER . " WHERE phone='" . $friendPhone . "'");
                if (!$checkRecivergift) {
                    #Them 1 ban ghi moi
                    $checkRecivergift = ReceiverCore::add(array(
                        'first_name' => $friendPhone,
                        #'email' => $friendPhone.'@urbox.vn',
                        'phone' => $friendPhone,
                    ));

                }

                #Cap nhat vao don hang va loyal-order
                DB::update(T_CART, array('receiver_id' => $checkRecivergift['id'], 'message' => $friendMessage), 'id=' . $id_loyal_order);

                System::apiSuccess();

            }
        }
        System::apiError('No data found');
    }

    function setDelivery()
    {
        $token = Url::getParam('token', "");
        $tokenCache = CookieLib::get('cardNum');
        $id = Url::getParamInt('id', 0);
        if ($token == "" || $tokenCache != $token) {
            System::apiError("Token không chính xác");
        }
        if ($id <= 0) {
            System::apiError("Voucher không tồn tại");
        }
        $result = Card::updateTmpDelivery($id);
        if ($result == true) {
            System::apiSuccess('ok');
        } else {
            System::apiError("Voucher không tồn tại");
        }
    }

    function checkTopupStatus()
    {
        $token = Url::getParam('token', "");
        $cart_detail_id = Url::getParamInt('cart_detail_id', 0);
        $card_session = SessionLib::get('auth');
        if ($token == '' || $card_session['token'] != $token) {
            System::apiError("Có lỗi xảy ra. Vui lòng tải lại trình duyệt");
        }
        if ($cart_detail_id <= 0) {
            System::apiError("Đơn hàng không tồn tại");
        }
        $api = "/v2/service/urgift/topup-order";
        $client_id = "431c5c44aa901f54754bc07bd33328e8";
        $client_secret = "d282a647-5f2d-43e3-994d-3a3a2bdc44dc";
        $signature = hash("sha256", $client_secret . $client_id . TIME_NOW);

        $params = [
            'client_id' => $client_id,
            'cart_detail_id' => $cart_detail_id,
            'timestamp' => TIME_NOW,
            'signature' => $signature,

        ];
        $client = new \GuzzleHttp\Client(["base_uri" => 'http://apib.urbox.internal']);
        $request = [
            "headers" => ['Content-Type' => 'application/json'],
            "query" => $params
        ];

        try {
            $_response = $client->get($api, $request);
            $status_code = $_response->getStatusCode();
            $response = $_response->getBody()->getContents();
            $responseObject = json_decode($response);
            $responseObject->statuscode = $status_code;
            $response = ($responseObject);
        } catch (Exception $e) {
            $response = new \stdClass;
            $response->statuscode = 400;
            $response->status = 5;
            $response->message = $e->getMessage();
        } finally {
            $status = $response->status;
            switch ($status){
                case 2:
                    $result = [
                        "status" => 2,
                        'message' => "Topup thành công"
                    ];
                    break;
                case 1:
                case 5:
                    $result = [
                        "status" => 3,
                        'message' => "Giao dịch topup của quý khách đã được ghi nhận.",
//                        'ex' => isset($response->message) ? $response->message: ""
                    ];
                break;

                default:
                    $result = [
                        "status" => 4,
                        'message' => "Topup thất bại"
                    ];
                break;

            }
            System::apiSuccess($result);
        }

//
//        deliveried          Status = 1 // đã gửi sang bên nhà cung cấp dịch vụ topup
//        success             Status = 2 // đã nhận callback thành công
//        delivery_fail       Status = 3 // fail khi gửi sang NCC
//        callback_fail       Status = 4 // callback gửi báo fail
//        callback_processing TopupOrder_Status = 5 // callback gửi đang xử lý đơn topup
    }
}
