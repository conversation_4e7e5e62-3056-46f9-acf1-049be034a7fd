@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
:root{
  --f7-navbar-height: 56px;
}
a:hover,a:focus,a:active{text-decoration: none}
body{font-family: "Roboto",sans-serif;font-size: 16px;color: rgb(49,49,49);font-weight: 400}
.ios body {
  font-family: 'Roboto',Arial,sans-serif;
    color: rgb(49,49,49);
    line-height: 1.4;
}
.color-app{
  color: #007dc5 !important;
}
.color-text{
  color: #000000 !important;
}
.ios .border_top.list{font-size: 14px}

.ios .toolbar~* .page-content, .ios .toolbar~.page-content{padding-bottom: 95px}
.ios .toolbar{background-color: #FFF;height: 89px;
  box-shadow: 0 -10px 15px -5px rgba(217, 217, 217, 0.5);
}
.ios .toolbar:before{display: none !important;}
.container{padding: 0 28px}
.ios .toolbar-inner{
  padding: 0 20px;}
.ios .navbar.navbar-hidden~.page-content, .ios .navbar.navbar-hidden~:not(.no-navbar) .page-content{padding-top: 0 !important;}
.ios .navbar-hidden {
    -webkit-transform: translate3d(0,-110%,0);
    transform: translate3d(0,-110%,0);
}
.ios .navbar{
  background-color: #fff;
  height: 56px;
  border-bottom: 1px solid rgba(188,188,188,0.25);
}
.ios .navbar-inner{
  padding: 0 20px;
}
.ios .navbar:after{
  background-color: transparent;
}
.ios .page{
  
  background-color: #FFF;
}

/*.ios .navbar{box-shadow: 0 3px 4px 0 rgba(172, 172, 172, 0.5)}*/
main {
  position: relative;
  padding: 16px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.header-background{
  padding: 0;
  margin: 0 auto;
  display: block;
  width: 100%;
  height: 180px;
  text-align: center;
}
.header-background img{
  width: 100%;
}
.header-logo{
  min-width: 200px;
  text-align: center;
  margin: 0 auto;
  background: #FFFFFF;
  box-shadow: 0px 0px 7px rgba(128, 128, 128, 0.25);
}
.slash{
font-size: 30px;
  font-weight: 300;
  color: #ccc;
}
.header-logo .logo{
  width: 45%;
  float: left;
  text-align: center;
  position: relative;
  padding: 12px 5px;
}
.header-logo .logo2{
  float: right;
}
.header-logo .logo1:after{
  position: absolute;
  right: 0;
  top: 25px;
  height: 22px;
  background-color: #BCBCBC;
  content: '';
  width: 1px;
  -webkit-transform: rotate(20deg);
  -moz-transform: rotate(20deg);
  -o-transform: rotate(20deg);
  -ms-transform: rotate(20deg);
  transform: rotate(20deg);
}
.mT90{
  margin-top: 90px;
}
.gift-name{
  text-align: center;
  font-weight: bold;
  width: 70%;
  margin: 0 auto;
  font-style: normal;
  line-height: normal;
  font-size: 18px;
}
.gift-code-input{
  position: relative;
}
.gift-code-input label{
  display: block;
  font-style: normal;
  font-weight: bold;
  line-height: 18px;
  font-size: 13px;
  position: absolute;
  top: -10px;
  left: 5px;
  padding: 0 10px;
  background: #FFF;
}
.gift-code-input .input-text{
  padding: 15px;
  width: 100%;
  height: 40px;
  font-size: 12px;
  line-height: 40px;
  left: 28px;
  background-color: #FFFFFF;
  border: 1px solid #D0D0D0;
  box-sizing: border-box;
  border-radius: 7px;
}
.gift-code-input select.input-text {
  padding-top: 0;
  padding-bottom: 0;
}
.gift-code-input label span{
  color: #FD0000;
  font-weight: 400
}
.gift-code-input .input-text.error{
  border: 1px solid #FD0000;
}
.gift-code-input .input-text:-moz-placeholder,
.gift-code-input .input-text::-moz-placeholder,
.gift-code-input .input-text:-ms-input-placeholder,
.gift-code-input .input-text::-ms-input-placeholder,
.gift-code-input .input-text::-webkit-input-placeholder,
.gift-code-input .input-text::placeholder
{
  
  color: #BCBCBC;
}
.error-message{
  font-style: italic;
  font-weight: normal;
  line-height: 16px;
  font-size: 14px;
  color: #FD0000;
}
.mT125{
  margin-top: 125px;
}
.btn{
  height:46px;
  line-height: 42px;
  display: inline-block;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  text-align: center;
  border: 2px solid #007dc5;
  padding: 0 20px;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.5);
  border-radius: 25px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.btn-active{
  background: #007dc5;
}
.btn-gray{
  background: #ccc;
}
.hotline {
  font-style: normal;
  font-weight: bold;
  line-height: 18px;
  font-size: 14px;
  text-align: center;
  color: #1787FB;
}
.gift-title{
  position: relative;
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  width: 100%;
  text-align: center;
  color: #1787FB;
}
.gift-title .btn-back{
  position: absolute;
  float: left;
  left: -3px;
  top: 3px;
}
.gift-desc{
  padding: 0 10px;
  font-style: italic;
  font-weight: normal;
  line-height: 18px;
  font-size: 13px;
  text-align: center;
  
  color: #313131;
}
.gift-desc span{
font-weight: bold;
}
.select-box{
  height: 46px;
  width: 100%;
  font-size: 14px;
  padding-left: 10px;
  background: #FFFFFF;
  border: 1px solid #D0D0D0;
  box-sizing: border-box;
  border-radius: 7px;
  -webkit-appearance: menulist !important;
  -moz-appearance: menulist !important;
  appearance: menulist !important;
}
.label-notes{
  font-size: 14px;
  color: #313131;
  /* Text */
  opacity: 0.5;
}
.input-textarea{
  width: 100%;
  height: 130px;
  padding: 15px;
  background: #FFFFFF;
  border: 1px solid #D0D0D0;
  font-size: 14px;
  box-sizing: border-box;
  border-radius: 7px;
}
.form-notes{
  font-style: italic;
  font-weight: normal;
  line-height: 18px;
  font-size: 13px;
  color: #313131;
  opacity: 0.7;
}
.icon-checkbox {
  transition-duration: .2s;
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-left: 0 !important;
}
.item-inner {
  position: relative;
  width: 100%;
  min-width: 0;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  align-self: stretch;
  
  padding-top: 8px;
  padding-bottom: 8px;
  min-height: 48px;
  padding-right: 16px;
}
.item-title {
  min-width: 0;
  flex-shrink: 1;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  font-style: normal;
  font-weight: normal;
  line-height: 18px;
  font-size: 12px;
  
  color: #313131;
}
.item-content {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
}
.ios .radial-bg{
  background: #007dc5 !important;
}
.ios .page.radial-bg-topup{
  background-color: #219653 !important;
}
.block-content{
  text-align: center;
}
.content-center{
  margin: 0 auto;
}
.color-white{
  color:white;
}
.p-title{
  font-style: normal;
  font-weight: bold;
  line-height: 31px;
  font-size: 28px;
  text-align: center;
  width: 70%;
  margin: auto;
  color: #FFFFFF;
}
.p-desc{
  font-weight: 300;
  line-height: 20px;
  font-size: 16px;
  text-align: center;
  color: #FFFFFF;
  width: 70%;
  margin: 20px auto 190px;
  
}
.mT100{
  margin-top: 100px;
}
.block-content .hotline{
}
.ios .page{background-color: #FFF !important;}
.btn-topup{
  display: block;
  width: 100%;
  -webkit-box-sizing: border-box;
  text-align: center;
  font-weight: bold;
  color:#FFF !important;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.5);
  background-color: #bcbcbc;
}
.list-card{

}
.list-card a{
  opacity:.6;
  cursor: pointer;
  height: 26px;
  padding: 4px 4px 5px 4px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 59px;
  border-radius: 4px;
  display: inline-block;
  border: solid 1px rgba(208,208,208,.6);
  background-color: #ffffff;
}
.list-card a.active{
  opacity: 1;
}
.over-hidden{
  overflow: hidden;
}
.hr {
  height: 1px;
  border-bottom: 1px solid
}
.hr:after {
  content: '';
  position: absolute;
  background-color: var(--f7-list-item-border-color);
  display: block;
  z-index: 15;
  top: auto;
  right: auto;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  transform-origin: 50% 100%;
  transform: scaleY(calc(1 / var(--f7-device-pixel-ratio)));
}
.f-300{
  font-weight: 300 !important;
}
.f-400{
  font-weight: 400 !important;
}
.f-500{
  font-weight: 500 !important;
}
.f-600{
  font-weight: 600 !important;
}
.f-700{
  font-weight: 700 !important;
}
.f-800{
  font-weight: 800 !important;
}
.f-900{
  font-weight: 900 !important;
}

.w45p{
  width: 45%;
}
.w25p{
  width: 25%;
}
.w30p{
  width: 30%;
}
.confirm-block{
  border-radius: 10px;
  padding: 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.confirm-block .gift-code-input{}
.value-text{
  -ms-word-break: break-all;
  word-break: break-all;
}



@media screen and (max-width: 420px) {
  .container {
    padding: 0 15px;
  }
  .custom-btn{
    font-size: 16px ;
    padding: 0 15px ;
  }
}
@media screen and (max-width: 376px) {
  .container {
    padding: 0 15px;
  }
  .custom-btn{
    font-size: 14px ;
    padding: 0 15px ;
  }
}
@media screen and (max-width: 320px) {
  .container {
    padding: 0 15px;
  }
}