<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="130" height="130" viewBox="0 0 130 130">
  <defs>
    <filter id="Ellipse_1121" x="0" y="0" width="130" height="130" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-color="#163c84" flood-opacity="0.102"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="check" transform="translate(22683 21124)">
    <g transform="matrix(1, 0, 0, 1, -22683, -21124)" filter="url(#Ellipse_1121)">
      <circle id="Ellipse_1121-2" data-name="Ellipse 1121" cx="50" cy="50" r="50" transform="translate(15 12)" fill="#fff"/>
    </g>
    <g id="checked" transform="translate(-22658 -21102)">
      <path id="Path_12695" data-name="Path 12695" d="M180.285,65.213a3.014,3.014,0,0,0-4.263-.005l-36.171,36.075L126.8,87.109a3.015,3.015,0,1,0-4.436,4.084l15.176,16.481a3.011,3.011,0,0,0,2.154.972h.064a3.018,3.018,0,0,0,2.129-.879l38.392-38.291A3.015,3.015,0,0,0,180.285,65.213Z" transform="translate(-102.573 -54.276)" fill="#f5821f"/>
      <path id="Path_12696" data-name="Path 12696" d="M76.985,36.985A3.015,3.015,0,0,0,73.97,40,33.97,33.97,0,1,1,40,6.03,3.015,3.015,0,1,0,40,0,40,40,0,1,0,80,40,3.015,3.015,0,0,0,76.985,36.985Z" fill="#007dc5"/>
    </g>
  </g>
</svg>
