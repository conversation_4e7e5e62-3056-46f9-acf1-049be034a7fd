/*start expired*/
.card-content{
	position: relative;
	padding: 0 40px;
	margin: 70px auto 0;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	box-shadow: 0px 3px 6px #163C8429;
	border-radius: 12px;
	background: #fff;
	width: 100%;
}
.block-img-card{
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	position: absolute;
	z-index: auto;
}

.block-img-card{

}
.logo-pti img{
	height: 60px;
}
.card-time-number{
	position: absolute;
	bottom: 0px;
	left: 0px;
	padding-bottom: 30px;
	padding-left: 40px;
	text-align: left;
	width: 100%;
	color: #FFF;
	font-size: 26px;
	font-weight: 600;
	background: transparent linear-gradient(180deg, #00000000 0%, #00000098 100%) 0% 0% no-repeat padding-box;
	border-radius: 0px 0px .8rem .8rem;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.page-expired {
	background: #F0F3F5 url("./images/BG.png") top 375px center no-repeat;
	background-size: cover;
	position: relative;
}
.block-card-img{
	position: relative;
	font-size: 0;
	top: -50px;
}

.logout-card{
	position: absolute;
	bottom: 35px;
	width: calc(100% - 80px);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.main-card-img{
	border-radius: 0px 0px .8rem .8rem;
	font-size: 0;
}
.card-time-number span{
	font-size: 12px;
	padding: 2px 8px;
	display: inline-block;
	border-radius: 12px;
	font-weight: 400;
	margin-left: 10px;
	border: 1px solid #FFFFFF9A;
	background: rgba(0,0,0,.2);
	cursor: pointer
}
.card-time-number a{
	color: #FFF;
}
.valid-time-block{
	padding-bottom: 110px;
	margin-top: -15vw;
}
.page-expired footer{
	position: absolute;
	bottom: 0px;
	width: 100%;
	left: 0;
	text-align: center;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.page-login main{
	min-height: calc(100vh - 57px);
}
footer{
	padding: 8px 10px;
	font-size: 12px;
	color: #a01095;
	background: #fff;
	font-weight: bold;
}
footer .spacing{
	width: 1px;
	height: 20px;
	margin: 0 10px;
	background: #a01095;
}
.fixed-btn{
	position: fixed;
	bottom: 40px;
	left: 0;
	width: calc(100% - 30px);
	z-index: 8888;
	background-color: #fff;
	padding: 10px;
	margin: 15px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 40px;
	-moz-border-radius: 40px;
	border-radius: 40px;
}
.result{
	display: flex;
	justify-content: center;
	align-items: center;
	height: calc(100% - 70px);
	background: #007dc5 url("./images/BG.png");
	-webkit-background-size: cover;
	background-size: cover;
}
.color-app2{
	color: #A01095
}
.block-content{
	position: relative;
	width: 100%;
	padding-bottom: 10px;
	margin: 10px;
	background: #fff;
	-webkit-border-radius: 24px;
	-moz-border-radius: 24px;
	border-radius: 24px;
	-webkit-box-shadow: 0 3px 20px #163C8429;
	-moz-box-shadow: 0 3px 20px #163C8429;
	box-shadow: 0 3px 20px #163C8429;
}
.icon-done{
	position: absolute;
	top: -70px;
	width: 140px;
	height: 140px;
	padding: 10px;
	left: calc(50% - 70px);
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.icon-done img{
	-webkit-box-shadow: 0 3px 20px #163C8429;
	-moz-box-shadow: 0 3px 20px #163C8429;
	box-shadow: 0 3px 20px #163C8429;
	width: 100%;
	height: 100%;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	padding: 0px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.gift-code-input label span{
	padding-left: 5px;
}
.page-login .gift-code-input .input-text{
	height: 50px;
	line-height: 50px;
}
.bg-gray{
	background: #F0F3F5;
}
.box-white{
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	border-radius: 8px;
	-webkit-box-shadow: 0 3px 20px #163C8429;
	-moz-box-shadow: 0 3px 20px #163C8429;
	box-shadow: 0 3px 20px #163C8429;
}
.pB30{
	padding-bottom: 30px;
}
.pB20{
	padding-bottom: 20px;
}
.color-gray{
	color: #969EB2;
}
.gift-code-input label span{
	padding-left: 5px;
	color :#F00;
}
footer img{
	width: 255px;
}
.gift-code-input select.input-text{
	background-image: url("./images/dropdown.svg");
	background-repeat: no-repeat;
	background-position: center right 10px;
}
.color-red{
	color: #FF0000;
}