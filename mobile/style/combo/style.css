@import url('https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,500,500i,700,700i,900,900i&subset=vietnamese');
:root {
  --app-bg-color: #0560A7;
  --app-border-color: #0560A7;
  --app-color: #720D5D;
  --text-color: #000;
  --red-color: #FD2020;
  --blue-color: #1787FB;
  --white-color: #fff;
  --gray-color: #828282;
  --blue-white-color: #F3F5F9;
  --expired-color: #4a4a4a;
  --border-gift: #DADADA;
  --f7-dialog-text-color: #313131;
  --f7-navbar-height: 70px;
  --f7-dialog-width: 90%;
  --f7-toolbar-bottom-shadow-image: transparent;
  --f7-toolbar-height: 60px;
}
.navbar a,
a{color: var(--text-color);}
a:hover,a:focus,a:active{text-decoration: none}
body {
  color: var(--text-color);
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
    margin: 0 auto;
    max-width: 768px;
}
img{font-size: 0;}
.f-300{
  font-weight: 300 !important;
}
.f-400{
  font-weight: 400 !important;
}
.f-500{
  font-weight: 500 !important;
}
.f-600{
  font-weight: 600 !important;
}
.f-700{
  font-weight: 700 !important;
}


.toolbar~* .page-content, .toolbar~.page-content{padding-bottom: 95px}
.navbar~.page-content, .navbar~:not(.no-navbar) .page-content{padding-top: var(--f7-navbar-height);}
.toolbar:before{display: none !important;}
.container{padding: 0 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.toolbar{
  background: #fff;
  box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.25);}
.toolbar-inner{padding: 0 20px;}
.navbar.navbar-hidden~.page-content, .navbar.navbar-hidden~:not(.no-navbar) .page-content{padding-top: 0 !important;}
.navbar-hidden {
  -webkit-transform: translate3d(0,-110%,0);
  transform: translate3d(0,-110%,0);
}
.navbar{
  background-color: #fff;
  height: var(--f7-navbar-height);
  border-bottom: transparent;
}
.navbar:before{
  /*background: transparent;*/
}
.navbar-inner{
  padding: 0 20px;
  align-items: center !important;
}
/*.navbar:after{
  background-color: transparent;
  box-shadow: 0px 1px 1px rgba(188, 188, 188, 0.25);
}*/
.navbar-inner .logo{
  padding: 10px;
  height: 100%;
  box-sizing: border-box
}
.navbar-inner .logo img{
  height: 100%;
}
.navbar .left, .navbar .right{
}
.page{
  background-color: #FFF;
  padding-top: 0;
}
.navbar .right {
  margin-left: 10px;
  margin-right: 15px;
}
.app-logo{
  height: 35px;
  width: calc(100% - 200px);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.ur-logo{
  padding-top: 5px;
  margin: 0 auto;
}
.ur-logo img{
  height: 30px;
}
.navbar .left{
  margin-right: 0;
  font-size: 15px;
  height: 30px;
  position: relative;
  
}
.color-app{
  color: var(--app-color);
}
.color-313131{
  color: #313131;
}
.color-white{
  color: var(--white-color) !important;;
}
.color-red{
  color: var(--red-color);
}
.color-blue{
  color: var(--blue-color)
}
.color-gray{
  color: var(--gray-color) !important;
}
.color-green{
  color: #007aff
}
.color-organ{
  color: #F78F00
}

.page-bg{
     background-size: cover;
}
.flex-direction-column{
  flex-direction: column;
}
.toolbar-inner .tab-link img{
  font-size: 0;
}
input.form-control{
  border: 1px solid #BDBDBD;
  font-size: 14px;
  height: 40px;
  box-sizing: border-box;
  border-radius: 3px;
  padding: 5px 10px;
}
input.form-control.input-error{
  border: 1px solid #FD2020;
}
.btn
{
  box-sizing: border-box;
  height: 50px;
  line-height: 48px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  text-align: center;
  display: inline-block;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.5);
  border: 2px solid var(--text-color);
  color: var(--text-color);
}
.btn-sm{
  box-sizing: border-box;
  height: 42px;
  line-height: 40px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  text-align: center;
  display: inline-block;
  font-size: 12px;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.5);
  border: 2px solid var(--text-color);
  color: var(--text-color);
}
.btn-active{
  background: var(--app-bg-color) !important;
  color: var(--white-color) !important;
  border: 0;
  font-weight: 500;
}
.btn-active-white{
  background: var(--white-color) !important;
  color: var(--app-color) !important;
  border: 1px solid var(--app-color);
}

.voucher-img{
  position: relative;
  width: 100%;
  background-size: cover;
  background-position: center;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}
.voucher-img:before{
  content: '';
  padding-top: 50%;
  display: block;
}
.toolbar-inner .tab-link span{
  text-transform: initial;
}
.tab-link-highlight{
  display: none;
}
.tab-link{
  padding-top: 38px;
}
.over-hidden{
  overflow:hidden;
}
.form-group input[type='text']{
  height: 36px;
  width: 100%;
  border-bottom: 2px solid #BCBCBC;
}
input.input-error{
    color: var(--red-color);
  border-bottom: 2px solid #f00 !important;
  
}
.promo-logo {
  width: 36px;
  height: 36px;
  padding: 5px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
  border-radius: 5px;
}

.card-logo {
  position: absolute;
  bottom: 10px;
  left: 10px;
  width: 50px;
  height: 50px;
  padding: 6px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 5px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
  background-color: #ffffff;
}
.m-auto{
  margin: auto;
}
header,main,footer{color: #474747;}
.page{background: #F0F3F5;overflow: auto}
header > .container{
  margin: 0 auto;display: flex;justify-content: center;align-items: center;padding: 10px;position: relative;
}
header .navbar-brand img{height: 40px;display: block}
header .region{position: absolute;right: 10px;top: 0;bottom: 0;height: 18px;margin: auto;background: #F0F3F5;
  border: 1px solid #E6ECF0;
  border-radius: 24px;padding: 3px;}
header .region a{
  border-radius: 24px;
  font-size:13px;
  padding:1px 7px;
  color: #969EB2;
}
header .region a.active{
  background: #FFF;
  box-shadow: 0 3px 6px #163C8429;
  color: #720D5D;
}
header{background: #fff;position: relative}
header .backpage{display: flex;width: 100%;padding: 4px 0}
header .backpage>div{flex: 1;padding: 0 24px 0 0;text-align: center;font-size: 17px;color: #000;font-weight: 500}
header .backpage>span{position: absolute;right: 10px}
header .backpage>span a{color: #1074FF;font-size: 14px;}
.giftcard_box .box{background: #fff;border-radius: 12px;box-shadow: 0 3px 6px #163C8429;padding: 15px;text-align: center}
.giftcard_box .box h4{color: #720D5D;font-size: 22px;margin: 5px 0}
.giftcard_box .box .des{color: #474747;line-height: 22px;margin: 0 0 20px;}
.giftcard_box .box .form-control-1{margin: 0 0 20px;}
.form-control-1{border-radius: 12px;border:1px solid #E6ECF0;background: #fff;height:60px;position: relative;}
.form-control-1 label{position: absolute;background: #fff;left: 15px;top: -10px;color: #969EB2;font-size: 14px;padding: 0 7px;}
.form-control-1 .form-control{width: 100%;height: 100%;border: 0;box-shadow: none;font-size: 24px;padding: 5px 15px;}
.form-control-1 .form-control::placeholder{color: #E0E0E0;}
.form-control-1 .form-control[type=password]{font-size: 50px;color: #000}
.btn-xn{height: 48px;width: 100%;background: #720D5D;border-radius: 28px;color: #fff;font-size: 16px;text-align: center;border:0}
.giftcard_box .box .btn-xn{margin: 0 0 20px;}
.giftcard_box .box p{margin: 0 0 5px;}
.giftcard_box .box .call{font-size: 20px;}

.listbrand_box h2{font-size:20px;color:#000;font-weight:700;text-align: center;margin: 0 0 10px;}
.listbrand_box .tabx{}
.listbrand_box .tabx .tab_h{overflow: auto;padding: 0 0 1px;}
.listbrand_box .tabx .tab_h div{width: 10000em;border-bottom: 1px solid #E6ECF0;height: 25px;}
.listbrand_box .tabx .tab_h a{color: #969EB2;margin:0 34px 0 0;border-bottom: 3px solid transparent;position: relative;padding: 0;line-height: 27px;font-size: 15px;}
.listbrand_box .tabx .tab_h a.tab-link-active{color: #720D5D;font-weight: 500;}
.listbrand_box .tabx .tab_h a.tab-link-active:after{content:'';background:#720D5D;width: 60%;bottom:0;height: 3px;margin: auto;position: absolute;border-radius: 1px;}
.listbrand_box .tabx .tab_h a:last-child{margin: 0}

.listbrand_box .tabx .tabs_c{
  padding: 0 0 20px;
}
.swiper-container.swiper-containerxxx{
  margin: 0 -5px;
  padding: 15px 0 40px;
}
img{display: block;margin: auto}
.listbrand_box .tabx .tabs_c .page-content{
  overflow: initial;
}
.swiper-container.swiper-containerxxx .swiper-slide .brand-1{width: calc(100%/3);float:left;margin:0 0 5px;position: relative;padding: 0 0 10px;height: calc(100vw / 3.3);}
.swiper-container.swiper-containerxxx .swiper-slide .brand-1:nth-child(3n+1){margin:0 0 5px}
.brand-1>a:nth-child(1){width: calc(100vw / 5.7);height:calc(100vw / 5.7);border-radius: 50%;overflow: hidden;border:3px solid #fff;box-shadow: 0 3px 10px #163C841A;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;background:#fff;}
.brand-1>a:nth-child(1) img{width: 100%;display: block;max-width: 100%}
.brand-1>div{margin: 5px 10px}
.brand-1>div a{font-size: 12px;line-height: 16px;height: 32px;display: inline-block}
.swiper-container.swiper-containerxxx .swiper-pagination-bullet{opacity: 1;background:#720D5D;width: 8px;height: 8px;}
.swiper-container.swiper-containerxxx .swiper-pagination-bullet:nth-child(2){background:#D3008D}
.swiper-container.swiper-containerxxx .swiper-pagination-bullet:nth-child(3){background:#37B34A}
.swiper-container.swiper-containerxxx .swiper-pagination-bullet:nth-child(4){background:#F7BE00}
.swiper-container.swiper-containerxxx .swiper-pagination-bullet.swiper-pagination-bullet-active{width: 16px;height: 16px;position: relative;top: 3px}

header.hsearch{box-shadow: 0 3px 6px #163C8429;}
header.hsearch > .container{padding:14px 10px;justify-content: flex-start}
header.hsearch .search{
    height: 32px;background: #F0F3F5;border: 1px solid #E6ECF0;border-radius: 16px;
}

header.hsearch .search input::placeholder{color: #969EB2}

.cardnumber_box{position: relative;margin: 0 0 40px;}
.cardnumber_box:before{content:'';position: absolute;left: 0;right: 0;height: 60%;background: #fff;z-index: -1}
.cardnumber_box .lg{display: block;padding: 18px 0 16px;}
.cardnumber_box .lg img{max-height: 40px;}
.cardX{margin:0 10px 40px;border-radius: 12px;box-shadow: 0 3px 16px #163C8433;background: #fff;position: relative;overflow: hidden}
.cardX .bh>.lgg{max-height:24px;margin: 0 0 15px;}
.cardX .bh>span{color: #FFF;text-transform: uppercase;font-size:20px;line-height: 24px;font-weight: 300;display: block;}
.cardX .bh>label{color: #FFF;font-size:24px;font-weight:700;display: block}
.cardX .bt{padding:11px 16px}
.cardX .bt .btl span{color: #000;display: block;font-size:14px;line-height:16px}
.cardX .bt .btl strong{color: #720D5D;font-size:20px;line-height:24px}
.cardX .bt .btn_qua{margin: 2px 0 0}
.cardX_box{margin: 0 0 40px;}
.cardX_box .cardY{position:relative;margin: 1px 0 0;}
.cardY{padding:8px 10px;background: #fff;box-shadow: 0 3px 6px #163C8429;}
.cardY .btl span{color: #000;display: block;font-size:14px;line-height:16px}
.cardY .btl strong{color: #720D5D;font-size:20px;line-height:24px}
.cardY .btn_qua{margin: 2px 0 0}
.btn_qua{background: #720D5D;border-radius: 18px;height: 36px;padding: 0 19px;line-height:36px;border:0;color:#fff}
.downapp1_box h3{color: #FFF;text-align: center;font-size:14px;margin:0 0 10px}
.downapp1_box >div{display: flex;justify-content: center;}
.downapp1_box >div a{margin:0 5px;}
.downapp1_box >div a img{max-height: 50px;}
.btn-vall{height: 48px;width: calc(100% - 4px);border: 1px solid #D8DFE3;
    border-radius: 12px;background: #E6ECF0;color: #720D5D;font-size:16px;text-align: center;line-height:46px;margin-left: 2px;}
.mainbg{background: url(images/BG.svg) no-repeat center bottom;background-size: contain}

.pd-10{padding-bottom: 10px!important;}
.popup_install_app{width: calc(100vw - 20px);height: auto!important;
    left: 0!important;
    top: 50px!important;
    right: 0!important;
    margin: auto!important;
    background: #FFFFFF!important;
    border-radius: 24px!important;
    min-height: 297px;
    box-shadow: none !important;}
.popup_install_app .popup_content>img{width: 100%;margin: 0 0 15px;}
.popup_install_app .popup_content>strong{color: #632466;font-size: 20px;font-weight: 700;text-align: center;margin: 0 25px 5px;display: block}
.popup_install_app .popup_content>p{color: #474747;font-size: 16px;margin: 0 10px 15px;text-align: center;display: block}
.popup_install_app .popup_content>p a{color: #720D5D;font-weight:700}
.btn-xxxc{height: 56px;
    line-height: 56px;
    width: calc(100% - 30px);
    margin: 0 15px 5px;
    border: 0;color: #FFF;
    font-size:20px;font-weight:700;
    background: #720D5D;
    border-radius: 12px;
}
.btn-xxxc1{
    text-align: center;color: #969EB2;font-size:16px;line-height: 19px;
    display: block;margin: 16px auto;
}
.popup_install_app .popup_content .bt{margin: 0 10px;border-top: 1px solid #E6ECF0;padding:20px 0 20px 15px;text-align: center;color: #969EB2;font-size:14px;}
.popup_install_app .popup_content .bt input[type=radio]{display: none}
.popup_install_app .popup_content .bt label{position: relative;}
.popup_install_app .popup_content .bt input[type=radio] + label:before{content:'';position: absolute;border: 1px solid #E0E0E0;width: 20px;height: 20px;background: #fff;left: -27px;
    top: -4px;border-radius: 50%}
.popup_install_app .popup_content .bt input[type=radio]:checked + label:before{border: 1px solid #1289db;}
.popup_install_app .popup_content .bt input[type=radio]:checked + label:after{content:'';position: absolute;width: 16px;height: 16px;background: #1289db;left: -24px;
    top: -1px;border-radius: 50%}

.popup_open_app{width: calc(100vw - 20px);height: auto!important;
    left: 0!important;
    top: 50px!important;
    right: 0!important;
    margin: auto!important;
    background: #FFFFFF!important;
    border-radius: 24px!important;
    min-height: 297px;
    box-shadow: none !important;}
.popup_open_app .popup_content>img{width: calc(100% - 30px);margin: 15px}
.popup_open_app .popup_content>p{color: #474747;font-size: 16px;margin: 0 10px 15px;text-align: center;display: block}
.popup_open_app .popup_content>p a{color: #720D5D;font-weight:700}

/*LOGIN PAGE*/
.balancelogin_box{text-align: center;min-height: calc(100vh - 120px);
  position: relative;}
.balancelogin_box .container>h2{color: #474747;font-size: 24px;font-weight:700;line-height: 32px;padding: 30px 0 5px;display: block;margin: 0}
.balancelogin_box .container>.cap{font-size: 16px;line-height: 22px;margin: 0 0 10px;}
.balancelogin_box .container>.cap strong{color: #632466;}
.balancelogin_box .formx1{margin: 65px 0 50px;}
.formx1{background: #fff;padding: 15px 15px 0;box-shadow: 0 3px 6px #163C8429;border-radius: 12px;}
.formx1 .cardX1{border-radius: 12px;box-shadow: 0 15px 20px -12px #163C8429}
.formx1 .lb{margin: 24px 0}
.formx1 .form-control-1{margin: 0 0 7px}
.formx1 .btn-xn{margin: 18px 0 24px}
.formx1 .signout{border-top: 1px solid #E6ECF0;padding: 20px 0;display: flex;font-size: 16px;text-align: left;}
.formx1 .signout label{flex: 1;color: #000;}
.formx1 .signout2{padding: 5px 0 20px;font-size: 16px;text-align: left;}
.formx1 .signout2 label{flex: 1;color: #000;}
.formx1 .signout2 a{color: #1074FF;text-align: center;display: block;background-size: auto 100%;}
.cardX1{position: relative;}
.cardX1 img{width: 100%;border-radius: 6px}
.cardX1 div label{display: block;text-transform: uppercase;font-size: 20px;font-weight: 300;line-height: 24px;}
.cardX1 div strong{display: block;line-height: 30px;font-size: 24px;font-weight: 700;}

.balancelogin_box .ft{color: #fff;padding: 0 0 5px;
  position: absolute;
  width: 100%;
  bottom: 0;}
.balancelogin_box .ft p{margin: 0;font-size:16px;}
.balancelogin_box .ft .call a{font-size: 20px;color: #fff}
.balancelogin_box2 .ft{color: #fff;background: url(images/BG.svg) repeat-x center top;text-align: center;padding:15px 0}
.balancelogin_box2 .ft p{margin: 0;font-size:16px;}
.balancelogin_box2 .ft .call a{font-size: 20px;color: #fff}
.flex-1{flex:1}
.warningxx hr{margin: 10px 0}
/*END LOGIN PAGE*/

/*UrBox_Link_Balance_Popup – Voucher*/
.popup_confirm_voucher{
  height: auto!important;
  top: auto!important;
  right: 0!important;
  margin: auto!important;
  background: #FFFFFF!important;
  border-radius: 32px 32px 0 0 !important;
  box-shadow: none !important;
  bottom: 0;
  padding:20px 15px;
}
.box_list_voucher{
  box-shadow: 0 3px 10px #163C8429;background: #fff;
  border-radius: 15px;
  padding: 10px;
  position: relative;;
}
.item_vc1{margin: -10px;padding: 10px;}
.item_vc1 .thumb{width: 68px;height: 68px;border-radius: 1%;overflow: hidden;flex-shrink: 0}
.item_vc1 .thumb img{width: 100%;}
.item_vc1 .info{flex: 1;}
.item_vc1 .info>span h3,.item_vc1 .info>a h3{
  color: #380606;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}
.item_vc1 .info>strong{display: block;color: #720D5D;font-size: 14px;}
.item_vc1 .info>span{display: block;color: #969EB2;font-size: 14px;}
.item_vc1 .info>span b{color: #ED1B2E;font-weight: 500;}
.item_vc1 .info>span b.ssc{color: #37B34A;}
.item_voucher{display: flex;}
.item_voucher>a img{
  width: calc(100vw / 2.7);
  background: #000;
  height: calc(100vw / 5);
  border: 0;
  outline: 0;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: none;
}
.item_voucher .info{margin: 0 0 0 15px;display: flex;flex-direction: column;justify-content: center;}
.item_voucher .info h3{color: #380606;font-size: 14px;line-height: 16px;margin: 5px 0 0;font-weight: 400}
.item_voucher .info strong{color: #720D5D;font-size: 17px;display: block;margin: 0;}
.item_voucher .info span{color: #969EB2;font-size: 14px;}
.hr1{position: relative;overflow: inherit;margin: 0 -20px;height: 16px;padding: 0 25px;}
.hr1 span{display: block;border-bottom: 1px dashed #E0E0E0;padding: 7px 0 0;}

.mb-5{margin-bottom: 5px!important;}
.mb-10{margin-bottom: 10px!important;}
.mb-15{margin-bottom: 15px!important;}
.mb-20{margin-bottom: 20px!important;}
.btn_confirm{
  width: 100%;
  height: 64px;
  box-shadow: 0 0 20px #163C8429;
  border-radius: 40px;
  border: 8px solid #fff;
  background: #720D5D;
  display: flex;
  padding: 0 15px;color: #FFF;
  font-size: 16px;
  font-weight:700;
  text-align: left;
}
.btn_confirm label{flex:1;}
/*END UrBox_Link_Balance_Popup – Voucher*/

/*UrBox_Link_Balance_Popup – Voucher2*/
.popup_term_voucher{width: calc(100vw - 30px);
  left: 0!important;
  top: 70px!important;
  right: 0!important;
  margin: auto!important;
  background: transparent!important;
  min-height: auto;
  box-shadow: none !important;text-align: center;
}
.popup_term_voucher .popup_content{background: #fff;
  border-radius: 24px!important;
  height: calc(100vh / 1.5)!important;
  text-align: left;
}
.popup_term_voucher .popup_content .pph{color: #000;font-size: 17px;font-weight: 500;line-height: 22px;padding: 15px 0;border-bottom: 1px solid #E0E0E0;text-align: center;}
.popup_term_voucher .popup_content .ppc{margin: 10px 5px 10px;overflow: auto;max-height: calc(100vh / 1.5 - 75px);padding:  0 0 0 5px;}
.popup_term_voucher .popup_content .ppc h3{font-size: 17px;color: #000;margin: 0 0 5px;}
.popup_term_voucher .popup_content .ppc .xvv{font-size: 15px;margin: 0 0 10px;}
.popup_term_voucher .popup_content .ppc::-webkit-scrollbar {
  width: 4px;
  height:8px
}

.popup_term_voucher .popup_content .ppc::-webkit-scrollbar-track {
  box-shadow: none;
  background: #F0F3F5;
  border-radius: 3px;
  border: 0.5px solid #F0F3F5;
}

.popup_term_voucher .popup_content .ppc::-webkit-scrollbar-thumb {
  background: #E0E0E0;
  border-width: 0 0 0 11px;
  border-radius: 3px
}
.btn-closexxxx{margin: 24px auto;width: 40px;height: 40px;display: inline-block;background: url(images/cancel.svg) no-repeat center center rgba(0,0,0,.4);border-radius: 50%;}
/*EndUrBox_Link_Balance_Popup – Voucher2*/

/*UrBox_Link_Balance_voucher*/
.egift_box{margin: 20px 0 24px;}
.gift_item .thmb{border-radius: 10px;overflow: hidden;margin: 0 0 15px;}
.gift_item .thmb img{width: 100%}
.gift_item>a{display: block;color: #380606;font-size: 16px;text-align: center}
.gift_item>strong{color: #720D5D;display: block;text-align: center;font-size: 24px;font-weight: 700;margin: 0 0 5px;}
.qr_box .lgl{display: block;margin: 0 0 15px;}
.qr_box .lgl img{max-height: 36px;}
.qr_box .imgqr{border-radius: 12px;padding: 10px;background: #fff;box-shadow: 0 3px 10px #163C8429;margin: 0 50px 13px;}
.qr_box .imgqr img{max-width: 100%;}
.qr_box .divcopy{border-radius: 12px;padding: 10px;background: #fff;box-shadow: 0 3px 10px #163C8429;margin: 0 0 15px;text-align: center;}
.qr_box .divcopy label{display: block;color: #969EB2;font-size:15px;}
.qr_box .divcopy strong{display: block;color: #380606;font-size: 20px;}
.copysvscl.dialog{width: 122px;box-shadow: 0 3px 20px #163C8429;border-radius: 24px;left: 0;right: 0;margin: auto;background: #fff;}
.copysvscl.dialog .dialog-inner{padding: 20px 0}
.qr_box .info{text-align: center;}
.qr_box .info b{display: block;color: #380606;font-size: 20px;font-weight: 500;margin: 0 0 5px;}
.qr_box .info ul{font-size: 14px;line-height: 20px;margin: 0 0 5px;}
.qr_box .info ul label{color: #969EB2;}
.qr_box .info ul span{color: #ED1B2E;font-weight: 700}
.qr_box .info ul strong{color: #1074FF;}
.qr_box .act{text-align: center;padding: 10px 0}
.store_box .tab_h>div{display: flex}
.store_box .tab_h a{flex: 1;height: 48px;padding: 0;line-height: 46px;text-align: center;font-weight: 500;font-size: 17px;position: relative;color: #969EB2;margin: 0 5px;}
.store_box .tab_h a.tab-link-active{color: #632066;}
.store_box .tab_h a.tab-link-active:after{content:'';height: 4px;background: #632066;border-radius: 4px;position: absolute;left: 0;right: 0;bottom: 0}
.store_box .tabs_c{background: #fff;margin: 0 -10px;padding: 15px 10px;}

.gift_me_box .tab_h{margin: 1px -10px 0;position: relative}
.gift_me_box .tab_h>div{display: flex;background: #fff;padding: 0 10px;justify-content: center}
.gift_me_box .tab_h a{padding: 22px 5px 18px;line-height: 20px;text-align: center;font-weight: 500;font-size: 14px;position: relative;color: #969EB2;margin: 0 13px;}
.gift_me_box .tab_h a.tab-link-active{color: #632066;}
.gift_me_box .tab_h a:after{content:'';height: 4px;background: transparent;border-radius: 4px;position: absolute;left: 0;right: 0;bottom: 0}
.gift_me_box .tab_h a.tab-link-active:after{background: #632066;}
.gift_me_box .tabs_c{margin: 0 -10px;padding: 15px 10px;}

.tab_1_c{color: #474747;font-size: 15px;}
.item_storex{box-shadow: 0 3px 6px #163C8429;padding: 5px;margin: 0 0 15px;
  border-radius: 12px;background: #FAFBFF;display: flex;}
.item_storex .stor{background: rgba(114,13,93,.06);border-radius: 8px;width: 64px;
  height: 64px;display: flex;justify-content: center;align-items: center;}
.item_storex .stor img{max-width: 100%;max-height: 100%;}
.item_storex .info{margin: 0 10px 0 12px;flex: 1;padding: 12px 0 0}
.item_storex .info a{display: block;color: #474747;font-size: 14px;}
/*End UrBox_Link_Balance_voucher*/
.tab_ready_used .box_list_voucher{margin: 0 0 15px;}
.tab_ready_used .swiper-container{margin: -10px -10px 0;padding: 10px 10px 30px;}
.tab_ready_used .swiper-container-horizontal>.swiper-pagination-bullets{
  bottom: 15px;background: #fff;
  left: 20px;
  right: 20px;
  width: auto;border-radius: 0 0 12px 12px;box-shadow: 0 3px 10px #163C8429;
  height: 30px;z-index: -1;
}
.actxx{padding: 10px 0 0;}
.actxx a{color: #380606;font-size: 17px;height: 30px;line-height: 30px;border-right: 1px solid #ddd;padding: 0 25px;}
.actxx a:last-child{border: 0}
.actxx a img{margin: 0 12px 0 0}
.actxx a span{white-space: nowrap}
.swiper-sk{
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 16px;
  width: auto;
  background: #fff;
  border-radius: 0 0 10px 10px;
  display: flex;
  box-shadow: 0 3px 6px #163C8429;
}
.swiper-sk .swiper-pagination-progressbar{
  position: relative;
  flex: 1;
  margin: 14px 5px 13px 10px;
  background: #F0F3F5;
  height: 2px;
}
.swiper-sk .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background: #720D5D}
.swiper-sk .swiper-pagination-page{
  color: #969EB2;font-size: 12px;margin: 7px 10px 0;
}

.popup_used_vc{width: calc(100vw - 20px);height: auto!important;
  left: 0!important;
  top: 50px!important;
  right: 0!important;
  margin: auto!important;
  background: #FFFFFF!important;
  border-radius: 15px!important;
  padding: 10px;
  box-shadow: none !important;}
.popup_used_vc .popup_content>img{width: 100%;margin: 0 0 15px;}
.popup_used_vc .popup_content>strong{color: #632466;font-size: 20px;font-weight: 700;text-align: center;margin: 0 25px 5px;display: block}
.popup_used_vc .popup_content>p{color: #474747;font-size: 16px;margin: 0 10px 15px;text-align: center;display: block}
.popup_used_vc .popup_content>p a{color: #720D5D;font-weight:700}
.used_x1{font-size: 17px;color: #474747;line-height:24px;padding: 10px 0;}
.used_x1 .btnx{padding: 15px 0;}
.btn-used-now{background: #720D5D;border: 1px solid #8E1675;border-radius: 12px;height: 56px;line-height: 54px;color:#fff;font-size: 17px;width: 163px;text-align: center;margin-right: 10px;}
.btn-cancel{background: #FFF;border: 2px solid #8E1675;border-radius: 12px;height: 56px;line-height: 52px;font-size: 17px;color: #720D5D;width: 163px;text-align: center;}
.buy-combo{display: none;bottom: 30px;position: fixed;z-index: 9999; right: 10px;background:transparent url(images/Combo.png) no-repeat center center;background-size:contain;width:130px;height: 130px}
.boxImgx{margin-top: 20px;border-radius: 6px}

.t-title-vourcher{color:#720D5D;font-size: 16px;margin-bottom: 15px;text-align: left}
.swiper-slide.swiper-slide2{background:none;box-shadow:none}

.swiper-slide{
  width: 95%;
  background: #FFFFFF;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 5px;
  /*overflow: hidden;*/
}
.single-swiper .swiper-slide{
  width: 100%;
}

.single-swiper .swiper-slide{
  margin-right: 0;
}
.swiper-slide .detail-content .gift-name {
  width: 100%;
  float: none;
}
.swiper-slide .detail-content .gift-price{
  float: none;
  width: 100%;
  text-align: left;
}

.link-hidden{
  overflow: hidden;
  white-space: nowrap;
  display: block;
  text-decoration: underline;
}
.cur_pointer{
  cursor: pointer;
}
.qr_codeBoxBar_copy{
  width: 80%;
  padding:10px;
  margin: 20px auto;
  border: 1px solid rgba(151,151,151,0.5);
  border-radius: 2px;
  font-size: 12px;
  text-align: center;
  background-color: #fbfbfb;
}
input.qr_codeBoxBar_text{
  border: 0;
  height: auto;
  text-align: center;
  background: transparent;
}
.hidden-area{
  position: absolute;
  opacity: 0;
  left: -9999px;
}
.gift-onecode{
  width: 100%;
  
}
.gift-onecode .gift-code{
  box-shadow: 0 0 12px 0 rgba(235, 235, 235, 0.5);
  width: 95%;
  margin: 30px auto;
  text-align: center;
}
.qr_codeBoxBar_copy_text{
  width: 100%;
  margin: 0 auto;
  padding: 10px;
  box-sizing: border-box;
  border-top: 1px dashed #ccc;
}
.expired-voucher{
  filter: grayscale(1);
  opacity: .9;
}
.brand_top{
  border-top: 1px solid var(--border-gift);
}
.brand_top_img{
  width: 60px;
  height: 60px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  padding: 5px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: flex;
  flex-shrink: 0;
  
}
.brand_top_img img{
  width: 100%;
}
.brand_top_content{
  -webkit-box-sizing: border-box;
  text-overflow: ellipsis;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 60px;
}

.brand-address{
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  height: 50px;
  line-height: 50px;
  border: 1px solid #eaeaea;
  border-right: 0;
}

.my-location{
  text-overflow: ellipsis;
  white-space: nowrap;
  width: calc(100% - 50px);
}
.border-box{
  box-sizing: border-box;
}
.catalog_filter{
  padding-bottom: 10px;
  border-left: 1px solid var(--border-gift);
}
.catalog_filter h5{
  margin: 0;
  padding: 0;
}
.catalog_location{
  background-position: center right 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-left: 0;
  border-bottom: 1px solid var(--border-gift);
  border-top: 1px solid var(--border-gift);
}


.list-address{
  display: block;
  clear: both;
}
.list-office{
  cursor: pointer;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 10px;
  display: block;
}
.list-office .item-address{
  flex-shrink: 0;
  width: 50px;
  text-align: right;
  justify-content: right;
}

.list-province .radio{
  float: right;
  margin-top: 5px;
}
.list-province li{
  border-bottom: 1px solid #eeeeee;
  padding: 10px 0;
  font-size: 14px;
  text-align: left;
}
.list-province li:last-child{
  border-bottom: 0;
}
.radio i{
  vertical-align: middle;
}
#map{
  width: 100%;
  height: 40%;
}

.dialog-expired{
  text-align: center;
  padding: 40px;
}
.btn-start{
  display: block;
  text-align: center;
  height: 50px;
  line-height: 50px;
  font-weight: bold;
  color : #FFF;
  border-radius: 6px;
  cursor: pointer;
  background-color: #720D5D;
}
.color-green{
  color: #007aff
}
.mx-0{
  margin: 0 auto;
}
.pR5{
  padding-right: 5px;
}
.combo-title{
  font-size: 24px;
  font-weight: 600;
}
.voucher-title{
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #380606;
}
.detail-img{
  position: relative;
  background-position: center center;
  -webkit-background-size: cover;
  background-size: cover;
}
.detail-img:before{
  content: '';
  padding-top: 50% ;
  display: block;
}
.pB20{
  padding-bottom: 20px;
}
.bottom-tab .tab-link{
  font-weight: bold;
  font-size: 16px;
  padding-top: 0;
  color: #969EB2;
  width: 50%;
  text-align: center;
  padding-bottom: 10px;
}
.bottom-tab .tab-link.tab-link-active{
  color: var(--app-color);border-bottom: 2px solid var(--app-color);
}
.bottom-tab .tabs{
  padding: 10px;
  background: #FFF;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bottom-tab{
  margin-top: 20px;
}
.store-icon{
  background: rgba(114, 13, 93, 0.1);
  padding: 5px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  margin-right: 10px;
}
.topup-block{
  padding: 20px;
  text-align: left;
  background: #FFF;
}
.topup-title{
  font-size: 20px;
  color: var(--app-color)
}
.topup-step{
  padding: 5px 0;
  font-size: 16px;
}
.carrier{
  box-shadow: 0px 3px 10px rgba(22, 60, 132, 0.16);
  padding: 3px;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  border-radius: 12px;
  width: 30%;
  margin-right: 2.3%;
  float: left;
  margin-bottom: 20px;
}
.carrier:nth-child(3n){
  margin-right: 0;
}
.carrier-inner{
  border: 2px solid #E6ECF0;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  border-radius: 12px;
  padding: 10px;
  color: #969EB2;
  font-size: 14px;
}
.carrier-inner img{
  width: 65%;
  box-shadow: 0px 3px 10px rgba(22, 60, 132, 0.16);
  margin-bottom: 5px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
}
.list-carrier {
  overflow: hidden;
  margin: 0 -20px;
  padding: 0 20px;
}
.carrier.selected-carrier{
  opacity: 1;
}
.selected-carrier .carrier-inner{
  border: 2px solid #720D5D;
  
}
.topup-note{
  color:#F78F00;
}
.toppup-form{
  margin-top: 10px;
  position: relative;
}
.toppup-form input{
  background: #F2F4F5;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  border-radius: 12px;
  height: 56px;
  line-height: 56px;
  padding-left: 10px;
  padding-right: 50px;
  width: 100%;
  font-size: 24px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.toppup-form a{
  position: absolute;
  right: 0;
  top:0;
  display: flex;
  align-items: center;
  width: 50px;
  height: 56px;
  line-height: 56px;
  vertical-align: middle;
  text-align: center;
  background: #969EB2;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}
.error-topup{
  color: #F00;
}
.item_vc1{position: relative;}
.item_vc1:before{
  content: '';
  width: 17px;
  height: 16px;
  position: absolute;
  left: 80px;
  top: -8px;
  background: url(images/ticket2.png) no-repeat 0 0;
  background-size: 100% 100%;
}
.item_vc1:after{
  content: '';
  width: 17px;
  height: 16px;
  position: absolute;
  left: 80px;
  bottom: -8px;
  background: url(images/ticket3.png) no-repeat 0 0;
  background-size: 100% 100%;
}
.tab_ready_used .swiper-container .item_vc1:after {
  background: url(images/ticket4.png) no-repeat 0 0;
  background-size: 100% 100%;
}
.bgShowTim{background-color: #720D5D !important;}
.top-img{
  width: 140px;
  height: 140px;
  margin: 0 auto;
  margin-top: -80px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  padding: 20px;
  background: #fff;
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.1);
}
.top-img img{
  width: 100%;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
.balancelogin_box2P{text-align: center;
  min-height: calc(100vh - 175px);
  position: relative;background-size:cover;
  background: url(images/BG.svg) no-repeat center bottom;}


/*start .login page*/
.block-card-img{
  position: relative;
  background-position: center center;
  background-size: cover;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
}
.block-card-img:before{
  content: "";
  padding-top: 50%;
  display:block;
}
.card-content{
  margin: 20px;
  padding: 15px;
  background: #FFF;
  -webkit-box-shadow: 0 3px  6px 0 #163C8429;
  -moz-box-shadow: 0 3px  6px 0 #163C8429;
  box-shadow: 0 3px  6px 0 #163C8429;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  border-radius: 12px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.combo-title{
  text-align: center;
  margin-top: 20px;
  font-size: 24px;
  font-weight: 600;
  color: #474747;
}
.pin-title{
  text-align: center;
}
.gift-code-input{
  border: 1px solid #E6ECF0;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  border-radius: 12px;
}
.gift-code-input legend{
  color: #969EB2;
  text-align: left;
}
.gift-code-input input{
  height: 25px;
  line-height: 25px;
}
.btn-login-active{
  background: #720D5D;
  border: 0;
  font-size: 16px;
  -webkit-border-radius: 28px;
  -moz-border-radius: 28px;
  border-radius: 28px;
}
.btn-deactived{
  font-size: 16px;
  background: #969EB2;
  border: 0;
  -webkit-border-radius: 28px;
  -moz-border-radius: 28px;
  border-radius: 28px;
  pointer-events: none
}
.term-cond{
  padding-top: 20px;
  padding-bottom: 10px;
  display: block;
  color: #2679FF;
}
.page-login{
  background: url("./images/BG.svg") bottom center no-repeat;
  background-size: contain;
}
#EPinError{
  padding-left: 20px;
  background: url("./images/ico-err.svg") left center no-repeat;
  background-size: 16px 16px;
}
.combo-price{
  font-size: 24px;
  text-align: center;
  font-weight: 600;
}
.code-img{
  margin: 0 auto;
  box-shadow: 0px 3px 10px #163C8429;
  padding: 15px;
  display: inline-block;
  border-radius: 12px;
}
.code-name{
  color:#380606;
}
.hr1 {
  position: relative;
  overflow: inherit;
  margin: 10px -20px;
  height: 16px;
  padding: 0 25px;
}
.hr1:before {
  background: url(./images/ticket1.png) no-repeat 0 0;
  background-size: 100% 100%;
  content: "";
  width: 17px;
  height: 16px;
  position: absolute;
  left: -3px;
  top: 0;
}
.hr1fix:after {
  background: url(./images/ticket1.png) no-repeat 0 0;
  background-size: 100% 100%;
  right: -3px;
  content: "";
  width: 17px;
  height: 16px;
  position: absolute;
  top: 0;
  transform: scaleX(-1);
}


@media (max-width: 376px) {
    .carrier{
      margin-right: 2%;
    }
  .carrier .carrier-inner{
    font-size: 12px;
  }
}
@media (max-width: 349px) {
  .carrier{
    margin-right: 1.8%;
  }
  .carrier .carrier-inner{
    font-size: 10px;
  }
}

.hr1 span {
  display: block;
  border-bottom: 1px dashed #E0E0E0;
  padding: 7px 0 0;
}
.hr2 {
  position: relative;
  overflow: inherit;
  margin: 0 -20px;
  height: 16px;
  padding: 0 25px;
}

.hr2 span {
  display: block;
  border-bottom: 1px dashed #E0E0E0;
  padding: 7px 0 0;
}


/*detail voucher*/

.vd_sec {
  position: relative;
  min-height: calc(100vh - 82px);
  padding: 50px 0 0;
}

.vd_sec:before {
  content: '';
  position: absolute;
  bottom: 0;
  background: #fff;
  width: calc(100vw / 2 - 360px);
  left: 0;
  top: 0;
  height: 100%;
  z-index: -1
}

.vd_sec:after {
  content: '';
  position: absolute;
  bottom: 0;
  background: #F0F3F5;
  width: calc(100vw / 2 + 360px);
  right: 0;
  top: 0;
  height: 100%;
  z-index: -1
}

.vd_sec .auto_x {
  width: 1170px;
  margin: 0 auto;
  position: relative;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.vd_sec .auto_x .container {
  padding: 0
}

.vd_sec .auto_x .swiper-init-s-1 {
  width: 427px;
  margin: -10px -10px 0;
  padding: 10px 10px 10px;
}

.vd_sec .auto_x .egift_box {
  width: 427px;
  margin: 0
}

.vd_sec .auto_x .egift_box .hr1:before {
  background: url(./images/ticket6.png) no-repeat 0 0;
  background-size: 100% 100%;
}

.qr_box .imgqr {
  width: 140px;
  margin: 0 auto 13px;
  text-align: center;
}

.barcode1 {
  width: auto !important;
}

.recharge-grab {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 76px;
  border-radius: 12px;
  box-shadow: 0 3px 10px #163C8429;
  margin-bottom: 17px;
}

.recharge-grab img:last-child {
  transform: scaleX(-1);
}

.recharge-grab a {
  font-size: 17px;
  font-weight: 500;
}

.vd_sec .auto_x .voucher_box {
  position: sticky;
  top: 0
}

.vd_sec .auto_x .inner_voucher_box {
  position: relative;
  min-height: 1000px;
  padding: 0 0 20px;
}

.vd_sec .auto_x .store_box {
  width: 692px;
}

.vd_sec .auto_x .store_box .tab-link {
  color: #032136;
  font-size: 20px;
  font-weight: 700;
  padding-top: 15px;
}

.vd_sec .auto_x .store_box .tab_1_c {
  padding: 10px 0 15px;
  color: #032136;
  font-size: 16px;
}

.vd_sec .auto_x .store_box .tab_2_c {
  background: #fff;
  box-shadow: 0 3px 10px #0054901A;
  border-radius: 12px;
  padding: 20px 5px 20px 20px;
  margin: 20px 0
}

.vd_sec .auto_x .store_box .tab_2_c > div {
  overflow: auto;
  max-height: 360px;
}

.vd_sec .auto_x .store_box .tab_2_c > div::-webkit-scrollbar {
  width: 4px;
  height: 8px
}

.vd_sec .auto_x .store_box .tab_2_c > div::-webkit-scrollbar-track {
  box-shadow: none;
  background: #F0F3F5;
  border-radius: 3px;
  border: 0.5px solid #F0F3F5;
}

.vd_sec .auto_x .store_box .tab_2_c > div::-webkit-scrollbar-thumb {
  background: #E0E0E0;
  border-width: 0 0 0 11px;
  border-radius: 3px
}

.vd_sec .auto_x .store_box .tab_2_c .item_storex {
  margin-right: 15px;
  padding: 3px 4px;
}

.vd_sec .auto_x .store_box .tab_2_c .item_storex .stor {
  height: 42px;
  width: 42px;
}

.vd_sec .auto_x .store_box .tab_2_c .item_storex .stor img {
  height: 32px;
  width: 32px;
}

.vd_sec .auto_x .swiper-init-s-1 .swiper-sk {
  position: static;
  margin-top: 25px;
  padding-bottom: 20px;
}

.swiper-init-s-1 .swiper-sk {
  background: transparent;
  box-shadow: none;
  text-align: center;
  bottom: 20px;
}

.swiper-init-s-1 .swiper-sk .swiper-pagination-page {
  font-size: 16px;
  color: #8195A3;
  margin: 7px auto 0
}

.swiper-init-s-1 .swiper-sk .swiper-pagination-page span {
  color: #720D5D;
}

.swiper-init-s-1 .swiper-button-next,
.swiper-init-s-1 .swiper-button-prev {
  height: 48px;
  width: 48px;
}

.swiper-init-s-1 .swiper-button-prev {
  height: 48px;
  width: 48px;
}

.swiper-init-s-1 .swiper-button-next:after,
.swiper-init-s-1 .swiper-button-prev:after {
  content: '';
  background: url(./images/nb.png) no-repeat center center #fff;
  background-size: 24px 16px;
  box-shadow: 0 3px 10px #001C261A;
  border-radius: 50%;
}

.swiper-init-s-1 .swiper-button-next,
.swiper-init-s-1 .swiper-container-rtl .swiper-button-prev {
  right: 120px;
}

.swiper-button-next {
  transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
}

.swiper-init-s-1 .swiper-button-prev,
.swiper-init-s-1 .swiper-container-rtl .swiper-button-next {
  left: 120px;
}

.box_list_voucher {
  box-shadow: 0 3px 10px #163C8429;
  background: #fff;
  border-radius: 15px;
  padding: 10px;
  position: relative;;
}

.box_list_voucher.new_buy:before {
  content: '';
  width: 76px;
  height: 23px;
  background: url(./images/tag.svg) no-repeat center center;
  position: absolute;
  left: 6px;
  top: -2px;
}

.box_list_voucher.new_buy_en:before {
  content: '';
  width: 76px;
  height: 23px;
  background: url(./images/tag_new.svg) no-repeat center center;
  position: absolute;
  left: 6px;
  top: -2px;
}

.box_list_voucher.expried_vc:before {
  content: '';
  width: 65px;
  height: 23px;
  background: url(./images/tag1.png) no-repeat center center;
  position: absolute;
  left: 6px;
  top: -2px;
}

.box_list_voucher.expried_vc_en:before {
  content: '';
  width: 65px;
  height: 23px;
  background: url(./images/tag_expired.svg) no-repeat center center;
  position: absolute;
  left: 6px;
  top: -2px;
}

/*UrBox_Link_Balance_voucher*/
.egift_box {
  margin: 20px 0 24px;
}

.gift_item .thmb {
  padding-top: 50%;
  background-position: center center;
  background-size: 100%;
  border-radius: 10px;
  margin: 0 0 15px;
}

.gift_item > a {
  display: block;
  color: #380606;
  font-size: 16px;
  text-align: center
}

.gift_item > strong {
  color: #720D5D;
  display: block;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 5px;
}

.qr_box .lgl {
  display: block;
  margin: 0 0 15px;
}

.qr_box .lgl img {
  max-height: 36px;
}

.qr_box .imgqr {
  border-radius: 12px;
  padding: 10px;
  background: #fff;
  box-shadow: 0 3px 10px #163C8429;
}

.qr_box .imgqr img {
  max-width: 100%;
}

.qr_box .divcopy {
  border-radius: 12px;
  padding: 10px;
  background: #fff;
  box-shadow: 0 3px 10px #163C8429;
  margin: 0 0 15px;
  text-align: center;
}

.qr_box .divcopy label {
  display: block;
  color: #969EB2;
  font-size: 15px;
}

.qr_box .divcopy strong {
  display: block;
  color: #380606;
  font-size: 20px;
}

.copysvscl.dialog {
  width: 122px;
  box-shadow: 0 3px 20px #163C8429;
  border-radius: 24px;
  left: 0;
  right: 0;
  margin: auto;
  background: #fff;
}

.copyCard.dialog {
  width: 150px !important;
}

.copysvscl.dialog .dialog-inner {
  padding: 20px 0
}

.qr_box .info {
  text-align: center;
}

.qr_box .info b {
  display: block;
  color: #380606;
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 5px;
}

.qr_box .info ul {
  font-size: 14px;
  line-height: 20px;
  margin: 0 0 5px;
}

.qr_box .info ul label {
  color: #969EB2;
}

.qr_box .info ul span {
  color: #ED1B2E;
  font-weight: 700
}

.qr_box .info ul strong a {
  color: #1074FF;
}

.qr_box .act {
  text-align: center;
  padding: 10px 0
}

.btn-gift {
  display: inline-block;
  color: #474747;
  font-size: 14px;
  line-height: 20px;
  padding: 0 0 0 35px;
  background: url(./images/ico_gift.svg) no-repeat 0 center transparent;
  border: 0;
  height: auto;
  margin: auto
}

.item_storex {
  box-shadow: 0 3px 6px #163C8429;
  padding: 5px;
  margin: 0 0 15px;
  border-radius: 12px;
  background: #FAFBFF;
  display: flex;
}

.item_storex .stor {
  background: rgba(114, 13, 93, .06);
  border-radius: 8px;
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item_storex .stor img {
  max-width: 100%;
  max-height: 100%;
}

.item_storex .info {
  margin: 0 10px 0 12px;
  flex: 1;
  padding: 0 0;
  align-items: center;
  display: flex;
}

.item_storex .info a {
  display: block;
  color: #474747;
  font-size: 14px;
}
.detail-warning{
  display: flex;
  color: #969EB2;
  font-size: 14px;
  line-height: 20px;
  position: relative;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 20px;
}
.detail-warning img{
  margin: 0;
  padding-right: 5px;
}

.breadcrumb_ib .breadcrumb {
  display: flex;
  margin: 0;
  padding: 0;
}

.breadcrumb_ib li {
  list-style: none;
  font-size: 16px;
  margin-right: 4px;
}

.breadcrumb_ib li a {
  color: #8195A3;
}

.breadcrumb_ib li.active {
  color: #032136;
  background: transparent
}



@media (max-width: 1440px) {
  .vd_sec .auto_x .egift_box {
    width: 360px;
    margin: 0 auto;
  }
  .vd_sec .auto_x {
    width: 1150px;
  }
}

@media (max-width: 1000px) {
  .vd_sec .auto_x {
    width: auto;
    margin: 0 20px;
  }
  .vd_sec .auto_x .store_box {
    width: calc(50vw);
  }
  .vd_sec .auto_x .swiper-init-s-1 {
    width: 360px;
  }
  .auto_x .icon-next-slide {
    left: 200px;
  }
  .vd_sec .auto_x .egift_box {
    width: calc(40vw);
    max-width: 320px;
    margin: 0 auto;
  }
}

@media (max-width: 692px) {
  .vd_sec .auto_x .store_box {
    width: 100%;
  }
  .vd_sec .auto_x .egift_box {
    width: 427px;
    margin: 0 auto;
  }
}

@media (min-width: 1100px) and (max-width: 1200px) {
  .vd_sec .auto_x .egift_box {
    width: calc(35vw);
    max-width: 360px;
    margin: 0 auto;
  }
  .vd_sec .auto_x {
    width: auto;
    margin: 0 50px;
  }
  .vd_sec .auto_x .store_box {
    width: calc(52vw);
  }
}

@media (min-width: 1000px) and (max-width: 1100px) {
  .vd_sec .auto_x .egift_box {
    width: calc(35vw);
    max-width: 320px;
    margin: 0 auto;
  }
  .vd_sec .auto_x {
    width: auto;
    margin: 0 50px;
  }
  .vd_sec .auto_x .store_box {
    width: calc(45vw);
  }
}

.page-content-ver8{
  height: 100% !important;
  background: url(images/background.png) repeat-x center top 450px;
  background-size: 100% 100%;
  text-align: center;
}
.title-success {
  letter-spacing: 0;
  color: #720d5d;
  opacity: 1;
  font-size: 32px;
  font-weight: 700;
  margin-top: 100px;
}
.box-success {
  margin: 40px 15px 0 15px;
  background: #fff 0 0 no-repeat padding-box;
  box-shadow: 0 3px 6px #163C8429;
  border-radius: 24px;
  opacity: 1;
  position: relative;
  width: auto;
  padding: 12px;
  color: #474747;
}

.color-green{
  color:#37B34A;
}
.color-gray {
  color: #969EB2;
}

.f-400 {
  font-weight: 400;
}

.f-600 {
  font-weight: 600;
}

.color-blue {
  color: #1074FF;
}

.color-red {
  color: #FF0000;
}

.button-back{
  margin-top:20px;
  height: 48px;
  background: #720D5D;
  border: 1px solid #8E1675;
  border-radius: 28px;
  opacity: 1;
  cursor:pointer;
  color:#fff;
  font-size:16px;
  display:flex;
  align-items:center;
  justify-content:center;
}

.max-width-700{
  max-width:700px !important;
  min-height:588px;
}
.container.max-width-700 .tabs{
  width:100%;
}
.list-transaction{

}
.color-pink{
  color:#720D5D;
}
.justify-content-between{
  justify-content:space-between;
}
.item-history{
  padding:5px 5px 5px 10px;
  position:relative;
}
.item-history:before{
  content:"";
  width:6px;
  height:6px;
  border-radius:3px;
  background:#720D5D;
  position:absolute;
  left:0;
  bottom:12px;
}
.border-bottom{
  width:100%;
  height:4px;
  background:#F0F3F5;
}
.card_image{
  position:relative;
}
.card_image .card_content{
  position:absolute;
  left:24px;
  bottom:16px;
  color:#FFFFFF;
}
.gift-name{
  height:20px;
  overflow:hidden;
}

.total-price{
  border: 1px solid #D7E0E5;
  border-radius: 12px;
  padding: 10px 0;
}
.total-price .display-flex{
  padding: 10px 0;
  border-bottom: 1px solid #D7E0E5;
}
.total-price .display-flex:last-child{
  border-bottom: 0px;
}