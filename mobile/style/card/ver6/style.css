@import url('https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i&display=swap');

:root {
    --app-bg-color: #720D5D;
    --app-color: #720D5D;
    --text-color: rgba(71, 71, 71, 1);
    --red-color: #E72001;
    --orange-color: rgba(255, 198, 70, 1);
    --gray-color: #BCBCBC;
    --gray1-color: #7B7B7B;
    --gray2-color: rgba(199, 193, 206, 1);
    --white-color: #FFFFFF;
    --green-color: rgba(8, 213, 0, 1);
    --green1-color: rgba(36, 133, 32, 1);
    --new-color: #720D5D;
    --pink-color: #F49495;
    --f7-navbar-bg-color: #FFF;
    --f7-navbar-height: 60px;
    --f7-toolbar-height: 70px;
    --border-input-color: #d9d9d9;
    --border-gift: #DADADA;
    --f7-dialog-bg-color: #FFF;
    --f7-dialog-width: 335px;
    --f7-dialog-text-color: #000;
    --f7-dialog-font-size: 14px;
    --f7-list-margin-vertical: 15px;
    --f7-dialog-border-radius: 5px;
    --f7-tabbar-link-text-transform: unset;
    --f7-searchbar-input-height: 40px;
}

a:hover, a:focus, a:active {
    text-decoration: none
}

body {
    color: var(--text-color);
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    margin: 0 auto;
}

.color-gray {
    color: #969EB2;
}

.f-400 {
    font-weight: 400;
}

.f-600 {
    font-weight: 600;
}

.color-blue {
    color: #1074FF;
}

.color-red {
    color: #FF0000;
}

header {
    box-shadow: 0 3px 6px #163c8429;
    background-color: #ffffff !important;
    padding: 0 10px;
}

.page {
    background-color: #F0F3F5;
    padding-top: 0;
}

header {
    box-shadow: 0 3px 6px #163c8429;
    background-color: #ffffff !important;
}

header .header {
    padding: 0 10px;
    justify-content: center !important;
    position: relative;
}

header .header .logo img {
    height: 40px;
    object-fit: cover;
}

header .nav-right {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    align-items: center;
    display: flex;
}

header .region {
    height: 18px;
    margin-right: 6px;
    background: #F0F3F5;
    border: 1px solid #E6ECF0;
    border-radius: 24px;
    padding: 3px;
}

header .region a {
    border-radius: 24px;
    font-size: 13px;
    padding: 1px 7px;
    color: #969EB2;
}

header .region a.active {
    background: #FFF;
    box-shadow: 0 3px 6px #163C8429;
    color: #720D5D;
}
.navbar-brand img{
    height:40px;
    object-fit:cover;
}
.page-content-ver-6 {
    background: url(images/background.svg) repeat-x center top 390px;
    background-size: 100%;
    text-align: center;
}

.title {
    letter-spacing: 0;
    color: #720d5d;
    opacity: 1;
    font-size: 32px;
    font-weight: 700;
    margin-top: 50px;
}

.description {
    font-size: 16px;
    color: #474747;
}

.box-voucher {
    margin: 28px auto 0 auto;
    background: #fff 0 0 no-repeat padding-box;
    box-shadow: 0 3px 6px #163C8429;
    border-radius: 10px 10px 0 0;
    opacity: 1;
    position: relative;
    width: calc(100vw - 44px);
    padding: 12px;
    color: #474747;
}

.box-voucher .image {
    padding-top: 40%;
    background-position: 50%;
    background-size: 100%;
    border-radius: 12px;
}

.box-voucher .voucher-title {
    margin-top: 14px;
    font-size: 18px;
}

.box-voucher .price {
    font-size: 24px;
    color: #720d5d;
    font-weight: bold;
}

.box-voucher .brand-title {
    font-size: 16px;
    color: #969EB2;
}

.box-voucher .expired {
    font-size: 16px;
    color: #474747;
}

.box-voucher .expired span {
    color: #FF0000;
    font-weight: bold;
}

.line {
    overflow: hidden;
    margin: 0 auto;
    max-width: calc(100vw - 44px);
    position: relative;
    height: 16px;
    padding: 0 12px;
}

.line:before {
    content: '';
    display: block;
    position: absolute;
    height: 34px;
    width: 38px;
    left: -9px;
    top: -6px;
    background: url(images/circleL.svg) left center no-repeat;
    -webkit-background-size: 100%;
    background-size: 100%;
    z-index: 1;
}

.line:after {
    content: '';
    display: block;
    position: absolute;
    height: 34px;
    width: 38px;
    right: -9px;
    top: -12px;
    background: url(images/circleL.svg) right center no-repeat;
    transform: rotate(180deg);
    -webkit-background-size: 100%;
    background-size: 100%;
    z-index: 1;
}

.line .line-inner {
    position: absolute;
    background: #fff;
    height: 100%;
    width: calc(100% - 20px);
    z-index: 2;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.line .line-inner .hr {
    border: 1px dashed #E4EAEF;
    width: 100%;
}

.box-button {
    margin: 0 auto;
    background: #fff 0 0 no-repeat padding-box;
    box-shadow: 0 3px 6px #163C8429;
    border-radius: 0 0 10px 10px;
    opacity: 1;
    position: relative;
    width: calc(100vw - 44px);
    padding: 12px;
    color: #474747;
}

.box-button .open-now-button {
    background: #720D5D 0 0 no-repeat padding-box;
    border-radius: 28px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.box-button .open-now-button a {
    font-size: 16px;
    color: #FFFFFF;
    font-weight: bold;
}

.contact {
    font-size: 16px;
    margin-top: 100px;
    text-align: center;
    color: #ffffff;
    padding-bottom: 20px;
}

.contact a {
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
}

/*detail*/
.swiper-voucher .gift-swiper {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 3px 6px #163C8429;
    color: #474747;
}

.swiper-voucher .gift-swiper .gift-detail {
    padding: 12px 12px 2px 12px;
}

.swiper-voucher .gift-swiper .gift-detail .detail-img {
    padding-top: 50%;
    background-position: 50%;
    background-size: 100%;
    border-radius: 10px;
    margin: 0 0 15px;
}

.swiper-voucher .gift-swiper .gift-detail .detail-content {
    text-align: center;
}

.swiper-voucher .gift-swiper .gift-detail .detail-content .gift-name {
    font-size: 17px;
    color: #474747;
}

.swiper-voucher .gift-swiper .gift-detail .detail-content .gift-price {
    font-size: 24px;
    color: #720D5D;
    font-weight: bold;
}

.hr1 {
    position: relative;
    overflow: inherit;
    margin: 0 -20px;
    height: 16px;
    padding: 0 25px;
}

.hr1 span {
    display: block;
    border-bottom: 1px dashed #E0E0E0;
    padding: 7px 0 0;
}

.hr1:before {
    content: '';
    width: 17px;
    height: 16px;
    position: absolute;
    left: 1px;
    top: 0;
    background: url("images/ticket1.png") no-repeat 0 0;
    background-size: 100% 100%;
}

.hr1:after {
    content: '';
    width: 17px;
    height: 16px;
    position: absolute;
    right: 1px;
    top: 0;
    background: url(images/ticket1.png) no-repeat 0 0;
    background-size: 100% 100%;
    transform: scaleX(-1);
}

.swiper-voucher .gift-swiper .gift-detail .gift-code .code-qr {
    width: 142px;
    height: 142px;
    box-shadow: 0px 3px 10px #163C8429;
    border-radius: 12px;
    padding: 9px;
    margin: 0 auto;
}

.swiper-voucher .gift-swiper .gift-detail .gift-code .code-qr img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.swiper-voucher .gift-swiper .gift-detail .gift-code .item-code {
    font-size: 20px;
    font-weight: bold;
    margin-top: 15px;
}

.tabbar .tab-link {
    padding: 15px 0 11px;
    width: 100%;
    letter-spacing: 0;
    color: #969eb2;
    padding-bottom: 6px;
    font-size: 14px;
}

.tabbar .tab-link-active {
    color: #720d5d;
    font-weight: 700;
}

.tabbar .tab-link-active:after {
    content: "";
    background: #720d5d;
    width: 90%;
    bottom: 0;
    height: 4px;
    margin: auto;
    position: absolute;
    border-radius: 4px;
    left: 5%;
}

.background-white {
    padding: 15px;
    background: #FFFFFF;
}

.item_storex {
    box-shadow: 0 3px 6px #163C8429;
    padding: 5px;
    margin: 0 0 15px;
    border-radius: 12px;
    background: #FAFBFF;
    display: flex;
}

.item_storex .stor {
    background: rgba(114, 13, 93, .06);
    border-radius: 8px;
    width: 64px;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.item_storex .stor img {
    max-width: 100%;
    max-height: 100%;
}

.item_storex .info {
    margin: 0 10px 0 12px;
    flex: 1;
    padding: 0 0;
    align-items: center;
    display: flex;
}

.item_storex .info a {
    display: block;
    color: #474747;
    font-size: 14px;
}

.friend-phone {
    background: #FFFFFF;
    border: 0.5px solid rgba(151, 151, 151, 0.3);
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 8px;
    padding:25px 10px;
    position: relative;
    margin: 0 5px;

}
.friend-phone label{
    position:absolute;
    background: #fff;
    left: 35px;
    top: 10px;
    color: #969eb2;
    font-size: 14px;
    padding: 0 7px;
}
.friend-phone input {
    width:100%;
    border-radius: 12px;
    border: 1px solid #e6ecf0;
    background: #fff;
    height: 48px;
    box-shadow: none;
    font-size: 24px;
    padding: 5px 15px;
    color:#474747;
}

.friend-phone input::placeholder {
    color: #E0E0E0;
}

.btn-showVoucher {
    width: 48px;
    height: 48px;
    display: block;
    text-align: center;
    line-height: 48px;
    -webkit-border-radius: 0 12px 12px 0;
    -moz-border-radius: 0 12px 12px 0;
    border-radius: 0 12px 12px 0;
    background: #720D5D;
    position:absolute;
    top: 25px;
    right:10px;
}

.page-content-ver8 {
    height: 100% !important;
    background: url(images/background.png) repeat-x center top 390px;
    background-size: 100% 100%;
    text-align: center;
}

.title-success {
    letter-spacing: 0;
    color: #720d5d;
    opacity: 1;
    font-size: 32px;
    font-weight: 700;
    margin-top: 40px;
}

.box-success {
    margin: 40px 16px 0 16px;
    background: #fff 0 0 no-repeat padding-box;
    box-shadow: 0 3px 6px #163C8429;
    border-radius: 24px;
    opacity: 1;
    position: relative;
    padding: 12px;
    color: #474747;
}

.button-back {
    margin-top: 20px;
    height: 48px;
    background: #720D5D;
    border: 1px solid #8E1675;
    border-radius: 28px;
    opacity: 1;
    cursor: pointer;
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr_codeBoxBar_copy {
    margin: 0 auto;
    box-shadow: 0 3px 10px #163C8429;
    padding: 15px 0;
    border-radius: 12px;
}
.border-bottom{
    border-bottom:1px dashed #E0E0E0;
}


.sheet-modalConfirm {
    background: #fff;
    padding: 1px 15px 1px 15px;
    border-radius: 32px 32px 0 0 !important;
    height: 425px;
}

.sheet-modalConfirm .card-img div{
    background-position: center;
    padding-top: 50%;
    border-radius:8px;
}

.sheet-modalConfirm .card-gift-name {
    width: 100%;
    object-fit: cover;
    color: #474747;
}

.sheet-modalConfirm .card-gift-name div {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.sheet-modalConfirm .card-gift-name .price {
    color: #720D5D;
    font-size: 17px;
    display: block;
    font-weight: bold;
}

.sheet-modalConfirm .card-gift-name .color-app {
    color: #969EB2 !important;
}

.sheet-modalConfirm .hr {
    position: relative;
    overflow: inherit;
    margin: 0 -20px;
    height: 16px;
    padding: 0 25px;
}

.sheet-modalConfirm .hr span {
    display: block;
    border-bottom: 1px dashed #E0E0E0;
    padding: 7px 0 0;
}

.sheet-modalConfirm .hr:before {
    content: "";
    width: 17px;
    height: 16px;
    position: absolute;
    left: 0;
    top: 0;
    background: url(images/ticket.png) no-repeat 0 0;
    background-size: 100% 100%;
}

.sheet-modalConfirm .hr:after {
    content: "";
    width: 17px;
    height: 16px;
    position: absolute;
    right: 0;
    top: 0;
    background: url(images/ticket.png) no-repeat 0 0;
    background-size: 100% 100%;
    transform: scaleX(-1);
}

.sheet-modalConfirm .hr span {
    display: block;
    border-bottom: 1px dashed #E0E0E0;
    padding: 7px 0 0;
}

.sheet-modalConfirm .condition {
    font-size:14px;
    text-align:center;
    padding:5px;
}

.sheet-modalConfirm .condition a{
    color:#1074FF;
}
.sheet-modal .toolbar-bottom{
    width: 100%;
    height: 64px;
    box-shadow: 0 0 20px #163c8429;
    border-radius: 40px;
    border: 8px solid #fff;
    background: #720D5D;
    padding: 0 15px;
    color: #FFF;
    font-size: 16px;
    font-weight: 700;
    text-align: left;
    bottom:25px;
}
.sheet-modal .toolbar-bottom .toolbar-inner a{
    color:#FFFFFF;
}

#sheet-content{
    width:calc(100vw - 30px);
    top: 20%;
    left:15px;
    background: #fff;
    padding: 20px 15px;
    border-radius: 32px !important;
    height: calc(100vh / 1.5 - 93px);
    max-height: 400px;
    color:#474747;
}
#sheet-content .sheet-modal-inner{
    overflow:auto;
}
#sheet-content .sheet-modal-inner .title{
    color: #000;
    font-size: 17px;
    font-weight: 500;
    line-height: 22px;
    padding: 10px 0;
    border-bottom: 1px solid #E0E0E0;
    text-align: center;
}


.gift-sheet{
    background: #FFF;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    padding: 15px;
}
.sheet-gift-content{
    display: flex;
    align-items: center;
    font-size: 16px;
    padding-bottom: 15px;
    color: #474747;
    border-bottom: 1px solid #eceff2;
    margin-bottom: 20px;
}
.sheet-gift-content img{
    margin-right: 10px;
}

.btn-confirm-gift{
    margin: 10px 0;
    border: 8px solid #FFF;
    border-radius: 40px;
    box-shadow: 0 0 20px 0 rgba(22, 60, 132, 0.16);
    background-color: #720d5d;
    color: #ffffff;
    display: block;
    width: 100%;
    left: 15px;
    font-size: 17px;
    z-index: 9999;
    height: 64px;
    box-sizing: border-box;
    text-align: center;
    line-height: 48px;
}
input[type='number'],
input[type='text']{
    background: #FFFFFF;
    /* Border */
    border: 1px solid var(--border-input-color);
    box-sizing: border-box;
    border-radius: 6px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    font-size: 14px;
    height: 48px;
    width: 100%;
    padding: 0 20px;
    color: var(--text-color);
}