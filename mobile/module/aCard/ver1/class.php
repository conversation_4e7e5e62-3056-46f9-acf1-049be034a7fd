<?php

class App
{
	function __construct($block,$options = [])
    {
        Module::initialize($block);
		$token = $options['token'];
        if ($token == '') {
            Url::redirect('home');
        }
        $card = Card::getByToken($token);
		if (!in_array($card['status'], [CARD_ACTIVE, CARD_DEACTIVATE])) {
			Url::redirect('home');
		}
		System::$data['card'] = $card;
        Card::trackingCard($card['id']);
        $app = Balance::getApp($card);
        System::$data['apps'] = $app;
        if($card['app_id'] == 0) $card['app_id'] = 34;
        if($card['gift_id'] == 0){
            $card['gift_id'] = $app['gift_id'];
        }
        System::$data['card'] = $card;
        if($card['status'] == CARD_ACTIVE){
            if($card['phone'] != ''){
                if(Card::preAdd($card,$card['phone'])){
                    System::$data['card'] = Card::getByToken($token);
                }
            }
            if(Card::topupCardByGiftCode($card)){
                System::$data['card'] = Card::getByToken($token);
            }
        }
        $file_name = "";
        if($app != false && !empty($app)){
            if(!is_dir('./_cache/file/card/')){
                mkdir('./_cache/file/card',0755);
            }
            if(!is_dir('./_cache/file/card/css/')){
                mkdir('./_cache/file/card/css',0755);
            }
            $file_name = "./_cache/file/card/css/root-".md5($app['id'].$app['name']).'.css';
            if(!file_exists($file_name)) {
                if(!empty($app['style_card'])) {
                    $f = fopen($file_name, 'w');
                    $content = $app['style_card'];
                    fwrite($f, $content);
                    fclose($f);
                }
            }
        }
        Form::addCss("mobile/style/card/{$options['version']}root.css");
        if(is_file($file_name)) {
            Form::addCss($file_name);
        }
        Form::addCss("mobile/style/card/{$options['version']}default.css");
        Form::addCss("mobile/style/card/{$options['version']}style.css");
        Form::addJs("mobile/javascript/card/{$options['version']}routes.js");
        Form::addCss("mobile/libs/_f7/plugins/welcome/f7.welcomescreen.css");
        Form::addJs("mobile/libs/_f7/plugins/welcome/f7.welcomescreen.js");
        Form::addJs("mobile/javascript/card/{$options['version']}cart.js");
        Form::addJs("mobile/javascript/card/{$options['version']}app.js");
        if(in_array($card['status'],[CARD_DEACTIVATE,CARD_ACTIVE])) {
            $page = strtolower($options["page"]);
            if(!Balance::checkCardPrefix($card['number'])){
                $page = 'open_in_app';
            }
			switch ($page) {
				case 'ulogin':
					require_once 'ULogIn.php';
					$form = 'ULogInForm';
					break;
				case 'onsetupphone':
					require_once 'onSetupPhone.php';
					$form = 'OnSetupPhoneForm';
					break;
				case 'onsetuppin':
					require_once 'onSetupPin.php';
					$form = 'OnSetupPinForm';
					break;
				case 'catalog':
					require_once 'catalog.php';
					$form = 'CatalogForm';
					break;
		
				case 'login':
					require_once 'LogIn.php';
					$form = 'LogInForm';
					break;
				case 'brand':
					require_once 'browseByBrand.php';
					$form = 'BrandForm';
					break;
				case 'brandoffice':
					require_once 'brandOffice.php';
					$form = 'BrandOfficeForm';
					break;
				case 'account':
					require_once 'account.php';
					$form = 'AccountForm';
					break;
				case 'welcome':
					require_once 'welcome.php';
					$form = 'WelcomeForm';
					break;
				case 'detail':
					require_once 'detail.php';
					$form = 'DetailForm';
					break;
				case 'expired':
					require_once dirname(__DIR__). '/Element/Expired.php';
					$form = 'ExpiredForm';
					break;
				case 'valid':
					require_once dirname(__DIR__). '/Element/ValidTime.php';
					$form = 'ValidTimeForm';
					break;
				case 'open_in_app':
					require_once dirname(__DIR__). '/Element/OpenPopupApp.php';
					$form = 'OpenPopupAppForm';
					break;
				default:
					require_once 'home.php';
					$form = 'HomeForm';
					break;
			}
			if ($card['survey_id'] > 0 && $card['status'] == CARD_ACTIVE) {
				$survey = Balance::checkSurvey($token);
				if ($survey > 0) {
					require_once 'survey.php';
					$form = 'SurveyForm';
				}
			}
            Module::addForm(new $form());
        }else{
            Url::redirect('home');
        }
    }
}
