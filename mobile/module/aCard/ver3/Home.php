<?php
class HomeForm extends Form
{
    function draw() {
        global $display; $class = System::$data['namespace'];
        
        $parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);
		#DB::query('select id from test_mail');
        $card = System::$data['card'];
        $app = System::$data['apps'];
        $allVoucher = BalanceVer3::allVoucher($card['id']);
		#$isPhysicalGift = BalanceVer3::checkPhysicalGift($card);
		$viewedBrand = BalanceVer3::getBrandFromCache();
		
		/*brand*/
		$per_page = Url::getParamInt('per_page', 18);
		$cat_id = Url::getParamInt('cat_id',0);
		$order = Url::getParam('order','popular');
		$allow_loc = BalanceVer3::getAccessLoc();
		$my_loc = BalanceVer3::getCurrentLoc();
		$physicalGift = BalanceVer3::giftPhysical($card);
		// lấy danh sách category
		$cate1 = BalanceVer3::getCategory($parent_id);
		// $cate2 = BalanceVer3::physicalCategory();
		$category = $cate1;
		if($allow_loc == 0 || count($my_loc) != 2) {
			$allowLoc = 0;
			$group_by = true;
		}else{
			$allowLoc = 1;
			$group_by = false;
		}
		$params = [];
		$params['paging'] = true;
		$params['cat'] = $cat_id;
		$params['row_per_page'] = $per_page;
		$params['page_no'] = 1;
		$params['group_by'] = $group_by;
		$params['keyword'] = "";
		$params['order'] = $order;
        $params['getOnline'] = true;

		$all_brands = BalanceVer3::listBrands(System::$data['card'],$params);
		$city = BalanceVer3::getCityName();
		$province = BalanceVer3::allProvide(true);
		if($cat_id > 0)
		$cate  = BalanceVer3::viewCate($cat_id);
		else $cate['title'] = "Tất cả";
		$filter = [
			//'default'=>"Mặc định",
			'hot'=>"Nổi bật",
			'popular'=>"Phổ biến nhất",
			'new'=>"Mới cập nhật",
			// 'priceDesc'=>"Giá cao đến thấp",
			// 'priceAsc'=>"Giá thấp đến cao"
		];
		$selected_filter = $filter[$order];
		/*endBrand*/
		//$allowBrand = Balance::brandInGiftSet(421,$card['gift_id']);
		$allowBrand = false;
		$display->add('allowBrand',$allowBrand);
		$display->add('viewedBrand',$viewedBrand);
		$display->add('allVoucher',$allVoucher);
		$display->add('physicalGift',$physicalGift);
		$display->add('card',$card);
		$display->add('app',$app);
		///////////////////////
		$display->add('cate',$cate);
		$display->add('province',$province);
		$display->add('selected_filter',$selected_filter);
		$display->add('order',$order);
		$display->add('filter',$filter);
		$display->add('category',$category);
		$display->add('city',$city);
		$display->add('allow_loc',$allowLoc);
		$display->add('all_brands',$all_brands);
		$display->add('per_page',$per_page);
		$display->add('cat_id',$cat_id);
		return $display->output(System::$data['version']."Home");
    }
}