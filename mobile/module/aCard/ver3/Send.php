<?php
class SendForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];
	
		$id = Url::getParamInt('id',0);
        if($id <= 0 ){
			Url::redirect("/card/?page=account");
		}
		$cart = Balance::getCartDetail($id);
        if(!$cart){
			Url::redirect("/card/?page=account");
		}
		$gift = BalanceVer3::getGift($cart['gift_detail_id']);
        $display->add('cart',$cart);
        $display->add('gift',$gift);
		$display->add('app',System::$data['apps']);
        return $display->output(System::$data['version']."Send");
    }
}