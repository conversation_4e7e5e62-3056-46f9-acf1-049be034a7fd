<?php

class ConfirmForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display;
		$id = Url::getParamInt('id',0);
		$address_id = Url::getParamInt('address',0);
        $card = System::$data['card'];
        $gift = BalanceVer3::getGift($id);
        if(empty($gift)) Url::redirect("card/{$card['token']}?page=catalog&type=2");
        if($address_id <=0) Url::redirect("card/{$card['token']}?page=receiver&id=$id");
        $address = ReceiverCore::viewAddress($address_id);

        $qty = CookieLib::get('c_qty');
        $isFreeShip = Cart::isFreeShip($card['app_id'], $qty * $gift['price']);
        $shipPrice = 0;
        if(!$isFreeShip) {
            $shipPrice = Cart::calcPhysical($card['app_id'], $address_id, $id, $qty);
        }
        if($qty <=0) $qty = 1;
		$display->add('app',System::$data['apps']);
        $display->add("qty",$qty);
        $display->add("address",$address);
        $display->add("shipPrice",$shipPrice);
        $display->add("gift",$gift);
        $display->add("address_id",$address_id);
        $display->add("card",$card);
        $display->add("id",$id);
        return $display->output(System::$data['version']."Confirm");
	}
}