<?php

class NewAddressForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display; $class = System::$data['namespace'];
		$id = Url::getParamInt('id',0);
        $card = System::$data['card'];
        $gift = BalanceVer3::getGift($id);
        $city = BalanceVer3::allProvide(false);
        $display->add("city",$city);
        $display->add("gift",$gift);
        $display->add("card",$card);
		$display->add('app',System::$data['apps']);
        $display->add("quantity",CookieLib::get('c_qty',0));
        $display->add("id",$id);
        return $display->output(System::$data['version']."NewAddress");
	}
}