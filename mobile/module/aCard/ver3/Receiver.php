<?php

class ReceiverForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display; $class = System::$data['namespace'];
		$id = Url::getParamInt('id',0);
        $card = System::$data['card'];
        $gift = BalanceVer3::getGift($id);
        $address = ReceiverCore::listAddress($card['id']);
        $display->add("address",$address);
        $display->add("gift",$gift);
        $display->add("card",$card);
        $display->add("id",$id);
		$display->add('app',System::$data['apps']);
        $display->add("quantity",CookieLib::get('c_qty',0));
        return $display->output(System::$data['version']."Receiver");
	}
}