<?php

class HomeForm extends Form
{

    function draw()
    {
        global $display;
        #$type = Url::getParam('type','money');
        $app = System::$data['apps'];
        $card = System::$data['card'];
        $titleData = [
            'labelPhone' => "Nạp tới số",
            'labelPrice' => "<PERSON><PERSON>n mệnh gi<PERSON> nạp (VND)"
        ];


        $gifts = BalanceVer3::giftToupByType($card['gift_id'], GIFT_TYPE_TOPUP_V8);
//        echo "<pre>";
//        print_r($gifts);
//        die(123);
        $display->add('gifts', $gifts);
        $display->add('app', $app);
        $display->add('titleData', $titleData);
        $display->add('card', $card);
        $output = System::$data['version'] . "Home";
        return $display->output($output);
    }
}
