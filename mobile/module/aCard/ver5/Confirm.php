<?php

class ConfirmForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display;

		$id = Url::getParamInt('id');
        $card = System::$data['card'];
        $gift = Balance::getGift($id);
        $isUseCard = Balance::isUseCard($card['id']);
        $deliveryInfo = CookieLib::get('card-gift-delivery');
//        echo "<pre>";
//        print_r(($deliveryInfo));
//        die(123);
        if(empty($id) || $id <=0 || $isUseCard > 0){
            Url::redirect('card');
        }
        if(empty($deliveryInfo)){
            Url::redirect('card?page=receiver&id='.$id);
        }
        $deliveryInfo = urldecode($deliveryInfo);
        $display->add("delivery",json_decode($deliveryInfo,true));
        $display->add("gift",$gift);
        $display->add("card",$card);
        return $display->output(System::$data['version']. "Confirm");
	}
}
