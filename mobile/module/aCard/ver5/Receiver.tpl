<div class="page" data-name="receiver">
    <div class="navbar" style="height: 70px">
        <div class="navbar-inner" style="height: 70px">
            <a href="javascript:void(0)" id="backlink" class="back left f14"><img
                        src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/icon-back.svg" alt=""></a>
            <span class="display-flex align-items-center f-600">
                Thông tin nhận hàng
             </span>
            <span></span>
        </div>
    </div>
    <div class="page-content page-receiver">
        <div class="page-content-inner">
            <form action="" class="" id="theForm">
                <div class="form-input width-layout bg-layout mB20">
                    <div class="bl-container">

                        <div class="form-input-content">

                            <div class="body-form-input">
                                <div class="form-group">
                                    <fieldset>
                                        <legend>Tên của bạn</legend>
                                        <input type="text" id="name" class="form-control formRequire" name="name"
                                               data-require="name" placeholder="Tên của bạn" value="{$delivery.name}">

                                    </fieldset>
                                    <div id="E-name" class="error-message mT5 hidden">Họ và tên không được để trống
                                    </div>
                                </div>
                                <div class="form-group">
                                    <fieldset>
                                        <legend>Số điện thoại</legend>
                                        <input type="text" id="phone" class="form-control formRequire" name="phone"
                                               data-require="phone" placeholder="Số điện thoại của bạn" value="{$delivery.phone}">
                                    </fieldset>
                                    <div id="E-phone" class="error-message mT5 hidden">Số điện thoại không hợp lệ</div>
                                </div>

                                <div class="form-group">
                                    <fieldset>
                                        <legend>Địa chỉ Email</legend>
                                        <input type="text" id="email" class="form-control formRequire" name="email"
                                               data-require="email" placeholder="<EMAIL>" value="{$delivery.email}">

                                    </fieldset>
                                    <div id="E-email" class="error-message mT5 hidden">Email không hợp lệ</div>
                                </div>
                                <div class="form-group">
                                    <fieldset>
                                        <legend>Tỉnh/Thành phố</legend>
                                        <input type="hidden" class="formRequire" id="city" data-require="int" value="{$delivery.city}"
                                               name="city" required>
                                        <input type="text" onclick="_.app.popup.open('#popup-city')" readonly name="city_text" value="{$delivery.city_text}"
                                               id="text-city" placeholder="Chọn" class="form-control has-dropdown">

                                    </fieldset>
                                    <div id="E-city" class="error-message mT5 hidden">Bạn chưa chọn Tỉnh/Thành phố</div>
                                </div>
                                <div class="form-group">
                                    <fieldset>
                                        <legend>Quận/Huyện</legend>
                                        <input type="hidden" class="formRequire" id="district" data-require="int" value="{$delivery.district}"
                                               required name="district" data-name="district">
                                        <input type="text" onclick="_.app.popup.open('#popup-district')" name="district_text" value="{$delivery.district_text}"
                                               id="text-district" readonly placeholder="Chọn"
                                               class="form-control has-dropdown">

                                    </fieldset>
                                    <div id="E-district" class="error-message mT5 hidden">Bạn chưa chọn Quận/Huyện</div>
                                </div>
                                <div class="form-group">
                                    <fieldset>
                                        <legend>Phường/Xã</legend>
                                        <input type="hidden" class="formRequire" id="ward" data-require="int" required value="{$delivery.ward}"
                                               name="ward" data-name="ward">
                                        <input type="text" onclick="_.app.popup.open('#popup-ward')" id="text-ward" name="ward_text" value="{$delivery.ward_text}"
                                               readonly placeholder="Chọn" class="form-control has-dropdown">
                                    </fieldset>
                                    <div id="E-ward" class="error-message mT5 hidden">Phường/Xã không được để trống
                                    </div>
                                </div>
                                <div class="form-group">
                                    <fieldset>
                                        <legend>Địa chỉ chi tiết</legend>
                                        <input type="text" class="form-control formRequire" name="street" id="street" value="{$delivery.street}"
                                               data-require="name" placeholder="Số nhà, Tên đường">
                                    </fieldset>
                                    <div id="E-street" class="error-message mT5 hidden">Địa chỉ không được để trống
                                    </div>
                                </div>
                            </div>
                            <div style="border: 3px solid #FFD6B0; border-radius: 20px; background: #FFF6ED; padding: 24px 32px; color: #232323; text-align: justify;  font-weight: 400; line-height: 1.3; max-width: 800px; margin: auto; margin-top: 16px;">
                                <span>Quý khách vui lòng sử dụng địa chỉ theo <b>đơn vị hành chính cũ</b> trong thời gian này để đảm bảo việc xử lí đơn hàng được chính xác.</span> 
                            </div>
                            <div class="footer-form-input mT40">
                                <a class="btn-confirm-gift" id="btn-submit" onclick="_.mod.click.validate()">Xác
                                    nhận</a>
                            </div>
                        </div>

                    </div>
                </div>
            </form>

            <div class="sheet-modal confirm-buy-sheet" data-backdrop="static" data-backdrop-close="true">
                <form action="" class="" id="theFormNote">
                    <div class="sheet-modal-inner p15">
                        <div class="sheet-title">Lưu ý cho đơn vị giao hàng</div>
                        <div class="form-group f15 color-app mB20">
                            <label class="checkbox checkbox-label" for="checkbox-1"><input type="checkbox"
                                                                                           id="checkbox-1" value="Gọi điện thoại trước khi giao hàng"
                                                                                           name="notes"><i
                                        class="icon-checkbox"></i> Gọi điện thoại trước khi giao hàng</label>
                        </div>
                        <div class="form-group f15 color-app mB20">
                            <label class="checkbox checkbox-label" for="checkbox-2"><input type="checkbox"
                                                                                           id="checkbox-2" value="Giao hàng trong giờ hành chính từ thứ 2 đến thứ 6"
                                                                                           name="notes"><i
                                        class="icon-checkbox"></i> Giao hàng trong giờ hành chính từ thứ 2 đến thứ
                                6</label>
                        </div>
                        <div>
                            <strong>Lưu ý khác: </strong>
                            <textarea name="notes_input" id="notes_input" class="notes-input"
                                      placeholder="Viết lưu ý ..."></textarea>
                        </div>

                        <div class="sheet-button mT20 mB20">
                            <a href="javascript: _.mod.click.confirm({$gift.id})"
                               class="btn-popup-gift">Gửi</a>
                            <a href="javascript: _.app.sheet.close()" class="btn-close-gift">Thoát</a>
                        </div>
                    </div>
                </form>
            </div>

            <div class="popup" id="popup-city">
                <div class="page">
                    <div class="navbar">
                        <div class="navbar-inner">
                            <div class="text-align-center w100p pL15 pR15">
                                <div class="text-align-center f17 f-700">Địa điểm</div>
                                <input id="input-city" onkeyup="_.mod.click.searchDis(this.id)" class="input-search"
                                       type="text" placeholder="Tìm tỉnh thành">
                            </div>
                        </div>
                    </div>
                    <div class="page-content">
                        <div class="list listSort">
                            <ul id="ul-input-city">
                                {foreach from=$city item=cat name=i}
                                    <li class="mB0"
                                        onclick="_.mod.click.getDisOfCity('#text-city','{$cat.title}',{$cat.id})">
                                        <label class="item-radio item-content pR15">
                                            <input type="radio" name="radio-city" data-name="{$cat.title}"
                                                   value="{$cat.id}"/>
                                            <div class="item-inner">
                                                <div class="display-flex pL5">
                                                    <img src="/mobile/style/card/ver5/images/pin.svg" alt="">
                                                    <div class="item-title f16 pL15 text-align-left">
                                                        <span>{$cat.title}</span></div>
                                                </div>
                                                <img src="/mobile/style/card/ver5/images/ico-arrow.svg" width="16"
                                                     alt="">
                                            </div>

                                        </label>
                                    </li>
                                {/foreach}
                            </ul>
                        </div>
                    </div>
                    <div class="toolbar toolbar-bottom delivery-toolbar">
                        <div class="toolbar-inner">
                            <div class="popup-footer">
                                <a href="javascript:void(0)" onclick="_.app.popup.close()"><img
                                            src="/mobile/style/card/ver5/images/cancel.svg" alt=""></a>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="popup" id="popup-district">
                <div class="page">
                    <div class="navbar">
                        <div class="navbar-inner">
                            <div class="text-align-center w100p pL15 pR15">
                                <div class="text-align-center f17 f-700">Địa điểm</div>
                                <input id="dis" onkeyup="_.mod.click.searchDis(this.id)" class="input-search"
                                       type="text" placeholder="Tìm Quận/Huyện">
                            </div>
                        </div>
                    </div>
                    <div class="page-content">
                        <div class="list listSort">
                            <ul id="ul-dis">
                                <li class="pT20 pB20 text-align-center">Bạn cần chọn Tỉnh/Thành phố trước </li>
                            </ul>
                        </div>
                    </div>
                    <div class="toolbar toolbar-bottom delivery-toolbar">
                        <div class="toolbar-inner">
                            <div class="popup-footer">
                                <a href="javascript:void(0)" onclick="_.app.popup.close()"><img
                                            src="/mobile/style/card/ver5/images/cancel.svg" alt=""></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="popup" id="popup-ward">
                <div class="page">
                    <div class="navbar">
                        <div class="navbar-inner">
                            <div class="text-align-center w100p pL15 pR15">
                                <div class="text-align-center f17 f-700">Địa điểm</div>
                                <input id="input-ward" onkeyup="_.mod.click.searchDis(this.id)" class="input-search"
                                       type="text" placeholder="Tìm Xã/Phường">
                            </div>
                        </div>
                    </div>
                    <div class="page-content">
                        <div class="list listSort">
                            <ul id="ul-input-ward">
                                <li class="pT20 pB20 text-align-center">Bạn cần chọn Xã/Phường trước</li>
                            </ul>
                        </div>
                    </div>
                    <div class="toolbar toolbar-bottom delivery-toolbar">
                        <div class="toolbar-inner">
                            <div class="popup-footer">
                                <a href="javascript:void(0)" onclick="_.app.popup.close()"><img
                                            src="/mobile/style/card/ver5/images/cancel.svg" alt=""></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
