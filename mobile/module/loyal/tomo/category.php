<?php
class CategoryForm extends Form
{

    function draw() {
        global $display;

        $per_page = Url::getParamInt('per_page', 10);
        $parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);
		$cat_id = Url::getParamInt('id',0);
		$type = Url::getParamInt('type',1);
		$order = Url::getParam('order',"default");
		$range = Url::getParam('range',"");
        $pages = [
                "per_page" => $per_page,
                "page_num" => 1
        ];
        if(!in_array($type,[1,2])) $type = 1;
		$card = System::$data['card'];
		$app = System::$data['apps'];
		$campaign = System::$data['campaign'];
        $city = Loyalty::getCityName();
        $category = Loyalty::getCategory($parent_id);
        if($cat_id > 0)
        $cate  = Loyalty::viewCate($cat_id);
        else $cate['title'] = "Tất cả";
        //$tag = Loyalty::getCategory($cat_id);
        $data = Loyalty::getListGifts($card,$cat_id,$pages,['isPhysical'=>$type,'orderBy'=>$order,'priceRange'=>$range,'length'=>TEXT_LENGTH]);
        
        $message = StringLib::post_db_parse_html($campaign['description']);
        $tag = [];
        $array_range = explode('-',$range);
        if(count($array_range) !=2){
            $array_range[0] = 0;
            $array_range[1] = 60000;
        }
        $filter = [
            'default'=>"Mặc định",
            'popular'=>"Phổ biến nhất",
            'new'=>"Mới cập nhật",
            'priceDesc'=>"Giá cao đến thấp",
            'priceAsc'=>"Giá thấp đến cao"
        ];
        $selected_filter = $filter[$order];
        $display->add('cate',$cate);
        $display->add('selected_filter',$selected_filter);
        $display->add('city',$city);
        $display->add('category',$category);
        $display->add('tags',$tag);
        $display->add('message',$message);
        $display->add('data',$data);
        $display->add('range',$array_range);
        $display->add('order',$order);
        $display->add('filter',$filter);
        $display->add('card',$card);
        $display->add('app',$app);
        $display->add('cat_id',$cat_id);
        $display->add('per_page',$per_page);
        $display->add('type',$type);
        $display->add('campaign',$campaign);
        return $display->output(System::$data['version']."category");
    }
}