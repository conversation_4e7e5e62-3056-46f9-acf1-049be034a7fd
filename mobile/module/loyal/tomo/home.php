<?php
class HomeForm extends Form
{

    const LIMIT = 10;
    const ISVOUCHER = 1;
    const ISPHYSICAL = 2;

    function draw() {
        global $display;
		$card = System::$data['card'];
		$app = System::$data['apps'];
		$campaign = System::$data['campaign'];
        $parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);

        $category = Loyalty::getCategory($parent_id);
        $message = StringLib::post_db_parse_html($campaign['description']);
        $promoVoucher = Loyalty::hotVoucher($card, self::LIMIT, self::ISVOUCHER, TEXT_LENGTH+ 20);
        $promoPhysical = Loyalty::hotVoucher($card, self::LIMIT, self::ISPHYSICAL, TEXT_LENGTH+ 20);
        foreach ($category as &$cat){
            $cat['items'] = Loyalty::getListGifts($card,$cat['id'],[],["isPhysical"=> 1,'length'=> TEXT_LENGTH+ 20]);
        }
        $viewed_gift = Loyalty::getGiftFromCache($card,1,0,$campaign['id']);
        $display->add('promoVoucher',$promoVoucher);
        $display->add('promoPhysical',$promoPhysical);
        $display->add('category',$category);
        $display->add('viewed_gift',$viewed_gift);
        $display->add('message',$message);
        $display->add('card',$card);
        $display->add('app',$app);
        $display->add('campaign',$campaign);

        return $display->output(System::$data['version']."home");
    }
}