<?php

class App extends Module
{
	function __construct($block, $options = [])
	{
		Module::initialize($block);
		#Get Token roi check
		$token = $options['token'];
		if ($token == '') {
			Url::redirect('home');
		}
		$card = Loyalty::getToken($token);
		if(empty($card)){
			Url::redirect('home');
		}
      
        $app = Loyalty::getApp($card['site_id']);
        if(empty($app)){
            Url::redirect('home');
        }
		
        $file_name= "";
        if($app != false && !empty($app)){
            if(!is_dir('./_cache/file/loyal/')){
                mkdir('./_cache/file/loyal',0755);
            }
            if(!is_dir('./_cache/file/loyal/css/')){
                mkdir('./_cache/file/loyal/css',0775);
            }
            $file_name = "./_cache/file/loyal/css/root-".md5($app['id'].$app['name']).'.css';
            if(!file_exists($file_name)) {
                if(!empty($app['style'])) {
                    $f = fopen($file_name, 'a+');
                   // $content = ":root{\n";
                    $content = $app['style'] . "\n";
                    //$content .= "}";
                    fwrite($f, $content);
                    fclose($f);
                }
            }
        }

        //init style and js
        Form::addCss('mobile/libs/_f7/css/framework7.bundle.min.css');
        Form::addCss('mobile/libs/_f7/plugins/keyboard/css/framework7.keypad.css');
        Form::addCss("mobile/style/loyal/{$options['version']}default.css");
        Form::addCss("mobile/style/loyal/{$options['version']}style.css");
        if(is_file($file_name)) {
            Form::addCss($file_name);
        }
        Form::addJs('mobile/libs/_f7/js/framework7.bundle.js');
        Form::addJs('mobile/libs/_f7/plugins/keyboard/js/framework7.keypad.min.js');
        Form::addJs("mobile/javascript/loyal/{$options['version']}routes.js");
        Form::addJs("mobile/javascript/loyal/{$options['version']}app.js");
        #page.urbox.vn/loyal/{token}
        Language::$activeLang = $card['lang'];
        Language::loadWordsFromLang(Language::$activeLang);
        Language::cookie(true,$card['lang']);

		System::$data['card'] = $card;
		System::$data['apps'] = $app;
		System::$data['campaign'] = Loyalty::getCampaign($card,2);
		$page = Url::getParam('page',"home");
		if($page != 'error') {
			$isLastToken = Loyalty::getLastToken($card);
			if ($isLastToken == false || ($card['expired'] > 0 && $card['expired'] < TIME_NOW && System::$data['campaign']['hasApiExpired'] == 2)) {
				Url::redirect( "loyal/{$card['token']}?page=error&type=session_expired" );
			}
		}
        switch ($page) {
            case 'category':
                require_once 'category.php';
                $form = 'CategoryForm';
                break;
            case 'gift':
                require_once 'gift.php';
                $form = 'GiftForm';
                break;
            case 'account':
                require_once 'account.php';
                $form = 'AccountForm';
                break;
            case 'detail':
                require_once 'detail.php';
                $form = 'DetailForm';
                break;
            case 'receiver':
               // require_once 'receiver.php';
               // $form = 'ReceiverForm';
               // break;
			case 'error':
				require_once 'Error.php';
				$form = 'ErrorForm';
				break;
            default:
                require_once 'home.php';
                $form = 'HomeForm';
                break;
        }
        Module::addForm(new $form());
    }
}