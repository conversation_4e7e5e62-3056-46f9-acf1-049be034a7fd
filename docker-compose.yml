services:
  # PHP-FPM Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: urpage_php
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
    networks:
      - urpage_network

  # Nginx Service
  nginx:
    image: nginx:1.19-alpine
    container_name: urpage_nginx
    restart: unless-stopped
    ports:
      - "8880:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - urpage_network

# Docker Networks
networks:
  urpage_network:
    driver: bridge
