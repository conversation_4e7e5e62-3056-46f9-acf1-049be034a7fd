<?php
ob_start();//start buffering
@ini_set('session.cookie_httponly', true);
@ini_set('session.cookie_secure', true);
use Dotenv\Dotenv;
use \Psr\Http\Message\ServerRequestInterface as Request;
use \Psr\Http\Message\ResponseInterface as Response;
require 'vendor/autoload.php';

// The argument specifies the directory where the ".env" file is located
$dotenv = Dotenv::create(__DIR__);
$dotenv->load();
ini_set('session.save_handler', 'redis');
ini_set('session.save_path', getenv('REDIS_PROTOCOL') . "://" . getenv('REDIS_IP') . ":" . getenv('REDIS_PORT')."/0");
session_start();
$config = [
    'settings' => [
        'displayErrorDetails' => false,
        'logger' => [
            'name' => 'slim-app',
            'level' => Monolog\Logger::DEBUG,
            'path' => __DIR__ . '/logs/app.log',
        ],
    ],
];
$app = new \Slim\App($config);
$c = $app->getContainer();
$c['errorHandler'] = function ($c) {
    return function ($request,\Slim\Http\Response $response,\Exception $exception) use ($c) {
        $client = (new Raven_Client([
            'dsn' => DSN_SENTRY_PAGE,
            'verify_ssl' => false
        ]))->install();
        $event_id = $client->captureException($exception);
        $template = "
            <title>Error</title>
            <link href='https://page.urbox.vn/website/style/default.css?v=2.8' rel='stylesheet'>
            <link href='https://page.urbox.vn/website/style/home/<USER>' rel='stylesheet'>
            <div class='framework7-root' id='app'>
                <div class='view view-main view-init ios-edges'>
                    <div class='page' data-name='notice'>
                        <div class='page page-current'>
                            <div class='page-content bg-f text-center bg-g' style='height:100%'>
                                <div class='container'>
                                    <div class='text-center pT20 mB20'>
                                        <div class='mB20'><img src='/mobile/style/home/<USER>/logo.svg'></div>
                                        <div class='mB20'><img src='/mobile/style/home/<USER>/image_overload.svg'></div>
                                        <div class='color-violet2 f32 f-700'>Rất tiếc!</div>
                                        <div class='f17 mB5'>Kết nối hệ thống đang tạm thời gián đoạn. Vui lòng thử lại sau.</div>
                                        <div class='f17 pRL20'>Hoặc gọi đến hotline <a href='tel:1900299232' class='external color-blue1'>1900 299 232</a> để được hỗ trợ.</div>
                                        <div>EventId: $event_id</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            ";
        try {
            $log = new Monolog\Logger('error');
            $log_file = __DIR__.'/_save_error_x/'.date('Y-m-d').'.log';
            $log->pushHandler(new Monolog\Handler\StreamHandler($log_file, Monolog\Logger::ERROR));
            $log->error( "File: ".$exception->getFile(). "-------------Line:" . $exception->getLine(). '-------------Message:'.$exception->getMessage(),$exception->getTrace());
            //var_dump($exception->getRequest());
        }catch (Exception $e){}
        return $response->withStatus(500)
            ->withHeader('Content-Type', 'text/html')
            ->write($template );
    };
};

require_once 'config/config.php';
FunctionLib::isIpBlack();
$app->any('[/{params:.*}]', function ($request, $response, $args) {
	# Device
	$request  = new Slim\Http\MobileRequest($request);
	$device = [];
	$device['isMobile'] = $request->isMobile();
	$device['isTablet'] = $request->isTablet();
	$device['isiOS'] = $request->isiOS();
	$device['isAndroidOS'] = $request->isAndroidOS();
	# Url Query String
	$params = explode('/', $args['params']);
    if(!empty($params)){
        foreach ($params as  &$value){
            $value = StringLib::clean_key(StringLib::clean_value($value));
        }
    }
	System::$data['url']['query'] = $params;
	System::$data['device'] = $device;
	System::$data['post'] = $request->getParsedBody();
	System::$data['request'] = $request;
	System::run();
});

$app->run();
