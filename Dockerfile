############################ Install Webserver #############################
FROM 848535873250.dkr.ecr.ap-southeast-1.amazonaws.com/hn-docker-slim:5.6-alpine


WORKDIR $WEBROOT

RUN php -r "readfile('http://getcomposer.org/installer');" | php -- --install-dir=/usr/bin/ --filename=composer
COPY composer.json composer.json
RUN composer install

COPY . /var/www/html
# Set the port to 80
EXPOSE 80

# Executing supervisord
ENTRYPOINT ["supervisord", "--nodaemon", "--configuration", "/etc/supervisord.conf"]
