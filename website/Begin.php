<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <?php if (isset(System::$data['page']['title'])) { ?>
        <title><?= System::$data['page']['title'] ?></title>
    <?php } else { ?>
        <title>Quà tặng điện tử :: URBOX</title>
    <?php } ?>

    <meta name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="EXPIRES" content="0"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="RESOURCE-TYPE" content="DOCUMENT"/>
    <meta name="DISTRIBUTION" content="GLOBAL"/>
    <meta name="AUTHOR" content="UrBox"/>
    <meta name="KEYWORDS" content=""/>
    <?php if (isset(System::$data['page']['description'])) { ?>
        <meta name="DESCRIPTION" content="<?= System::$data['page']['description'] ?>"/>
    <?php } ?>
    <meta name="COPYRIGHT" content=""/>

    <?php if (isset(System::$data['page']['favicon'])) { ?>
        <link href="<?= WEB_ROOT . System::$data['page']['favicon'] ?>" rel="shortcut icon"/>
    <?php } else { ?>
        <link href="<?= WEB_ROOT ?>website/style/favicon.ico" rel="shortcut icon"/>
    <?php } ?>
    <meta name="ROBOTS" content="INDEX, FOLLOW"/>
    <meta name="Googlebot" content="index,follow,archive"/>
    <?php if (isset(System::$data['page']['title'])) { ?>
        <meta property="og:title" content="<?= System::$data['page']['title'] ?>"/>
    <?php } ?>
    <meta property="og:locale" content="vi_VN"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content=""/>
    <meta property="og:image" content=""/>
    <meta property="og:site_name" content="UrBox"/>
    <meta property="og:description" content="<?= System::$data['page']['description'] ?>"/>
    <meta property="fb:app_id" content=""/>


    <?php if (!empty(System::$data['page']['css'])) {
        foreach (System::$data['page']['css'] as $v) { ?>
            <link href="<?= WEB_ROOT . $v ?>?v=3.4" rel="stylesheet">
        <?php } ?>
    <?php } ?>
    <link href="<?= WEB_ROOT ?>website/style/fonts.css" rel="stylesheet">
    <link href="<?= WEB_ROOT ?>website/style/style.css" rel="stylesheet">
    <link href="<?= WEB_ROOT ?>website/style/default.css" rel="stylesheet">
    <?php if (in_array(System::$data['page']['name'], ['combo', 'loyal'])) { ?>
        <script async defer
                src="https://maps.googleapis.com/maps/api/js?key=<?php echo GOOGLE_API_KEY ?>&callback">
        </script>
    <?php } ?>
    <?php if(System::$data['page']['name'] == 'grab.reward'){?>
        <script src="https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit"></script>
    <?php }?>
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-N2BZ42Q');</script>
    <!-- End Google Tag Manager -->

    <script src="<?= WEB_ROOT ?>website/javascript/easy.qrcode.min.js"></script>
    <script src="<?= WEB_ROOT ?>website/javascript/JsBarcode.all.min.js"></script>

    <script type="text/javascript">
        var query_string = "?<?=urlencode($_SERVER['QUERY_STRING']);?>",
            TIME_NOW = <?=TIME_NOW?>,
            BASE_TOKEN_NAME = "<?=TOKEN_NAME?>",
            BASE_TOKEN = "<?=CGlobal::$tokenData?>",
            COOKIE_ID = "<?=COOKIE_ID?>",
            BASE_URL = "<?=WEB_ROOT?>",
            DOMAIN_NAME = "<?=DOMAIN?>",
            DOMAIN_COOKIE_STRING = "<?=DOMAIN_COOKIE_STRING?>",
            GOOGLE_SITE_KEY = "<?=GOOGLE_ENP_SCORE_0?>",
        <?php
        echo 'DOMAIN_COOKIE_REG_VALUE = document.URL.search(/' . DOMAIN_NAME . '/i)';
        echo ';';
        echo " let CARD_RATE=" . CARD_RATE;
        ?>
    </script>
</head>
<body>
<!-- Google Tag Manager (noscript) -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N2BZ42Q" height="0" width="0"
            style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
