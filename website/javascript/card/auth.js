_.auth = {};
_.app = new Framework7({
    root: '#app',
    name: 'My App',
    id: 'com.page.card.auth',
    panel: false,
    iosTranslucentBars: false,
    iosTranslucentModals: false,
    routes: [
        {
            path: '/card',
            url: BASE_URL + '/card',
            name: 'home',
        },
        {
            path: '/card?page=:page&id=:id',
            url: BASE_URL + '/card?page={{page}}&id={{id}}',
            name: 'pages',
        }
    ],
    theme: 'ios',
    touch: {
        fastClicks: true,
    },
    view: {
        reloadPages: true,
        domCache: false,
        xhrCache: false,
        history: false,
        iosDynamicNavbar: true,
        pushState: true,
        pushStateRoot: window.location.origin,
        pushStateAnimate: true,
        pushStateOnLoad: false,
        pushStateAnimateOnLoad: true,
        pushStateSeparator: ""
    },
    init: false
});
let site_name = "";
let $$ = Dom7;
_.app.views.create('.view-main', {});
_.app.on('pageInit', function (page) {
    _.app.preloader.hide();
    _.app.popup.close();

    let versionIOS = iOSversion();

    if (versionIOS && versionIOS[0] <= 10) {
        console.log("version: ", versionIOS[0]);
        _.app.popup.open('.popup_check_ios_version');
    }

    //title brand font size
    if ($$('.brand-tit').length) {
        $$('.brand-tit').each(function () {
            if ($$(this).attr('data-value').length > 20) {
                $$(this).css('font-size', '10px');
            }
        });
    }

    //countdown time otp
    if (page.name === "otp") {
        _countDownOtp(59);
    }
    //end countdown

    $$('.close-logout').on('click', function () {
        _.app.popup.close('.popup-logout');
    })


    if ($$('.copyCardNumber').length) {
        $$('.copyCardNumber').on('click', function () {
            copyToClipboard($$('.card-number').text());
            _.app.dialog.create({
                cssClass: 'copysvscl copyCard',
                text: '<div style="text-align:center"><img alt="" src="/website/style/card/images/auth/icocopy.svg"/><span style="display: block;color: #969EB2;font-size:15px;">Đã copy!</span></div>',
                buttons: []
            }).open();
            setTimeout(function () {
                _.app.dialog.close();
            }, 1000);
        });
    }

    if (page.name === "register") {
        // $$("#inputCardNumber").focus();
        document.getElementById('inputCardNumber').focus();
    }
    if (page.name === "signIn") {
        //$$("#password-input").focus();
        document.getElementById('password-input').focus();
        $$('.page-content-scroll').scrollTop(150, 300);
    }
    if (page.name === 'otp') {
        // $$("#setOtpInput").focus();
        document.getElementById('setOtpInput').focus();
        $$('.page-content-scroll').scrollTop(150, 300);

    }
    if (page.name === 'login') {
        // console.log("ok");
        // $$("#input-pin").focus();
        document.getElementById('input-pin').focus();
        $$('.page-content-scroll').scrollTop(150, 300);
    }
    if (page.name === 'setpin') {
        // $$("#input-pin").focus();
        document.getElementById('input-pin').focus();
        $$('.page-content-scroll').scrollTop(150, 300);
    }
    if (page.name === 'activePhone') {
        // $$("#inputPhoneNumber").focus();
        document.getElementById('inputPhoneNumber').focus();
        $$('.page-content-scroll').scrollTop(150, 300);
    }
});
let _countDownOtp = function (timeLeft = 59,isReset = false) {
    let elem = $$('#countdown');
    if(isReset === true){
        try {
            clearTimeout(timerId);
        }catch(e){}
    }
    let timerId = setInterval(function () {
        if (timeLeft === -1) {
            clearTimeout(timerId);
            $$('#countdown').parent().hide();
            $$('.resend-OTP').show();
        } else {
            elem.html(timeLeft + 's');
            timeLeft--;
        }
    }, 1000);
}

function copyToClipboard(value) {
    value = value.replace(/ /gi, '')
    let tempInput = document.createElement("input");
    tempInput.style = "position: absolute; left: -1000px; top: -1000px";
    tempInput.value = value;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);
}

$$(document).on('page:init', function () {
    addHistory()
    let home = "/" + site_name;
    //  let a = _.app.views.main.router.previousRoute.url;
    let a = _.app.views.main.router.currentRoute.url;
    // a = a.replace("&dialog=2","");
    let his = sessionStorage.getItem('urbox_history');
    if (his === "" || his == null) {
        sessionStorage.setItem("urbox_history", JSON.stringify([]));
    }
    his = JSON.parse(his);
    let b = his ? his[his.length - 1] : [];
    if (typeof a !== 'undefined') {
        if (a === "/") a = home;
        if (a.charAt(0) === "/" && a.charAt(1) === "?") {
            let s = a.substr(1);
            a = home + s;
        }
        //xoá subfix trang voucher
        a = a.replace('&tab=2', '');
        a = a.replace('&tab=1', '');

        let order = a.split('&order=');
        a = order[0];
        if (his == null) his = [home]; // sửa lỗi session rỗng khi load trang
        if (his[0] != home) his.unshift(home);
        if (b !== a) his.push(a);
        if (a === home) {
            his = [a];
        }
    } else {
        a = home;
        his = [a];
    }
    sessionStorage.setItem("urbox_history", JSON.stringify(his));
    _.app.preloader.hide();
    window.history.pushState(null, null, location.href)
    window.onpopstate = function (e) {
        e.preventDefault();
        let history = sessionStorage.getItem("urbox_history");
        history = JSON.parse(history);
        if (history.length > 1) history.pop();
        let back_link = null;
        if (history.length >= 1) {
            back_link = history[history.length - 1];
            sessionStorage.setItem("urbox_history", JSON.stringify(history));
            _.app.views.main.router.navigate(back_link, {
                pushState: true,
                ignoreCache: true
            });
        }
        return true;
    };
});

//bấm nút back trong app
$$(document).on('click', '#backlink', function () {
    _.app.preloader.show();
    let history = sessionStorage.getItem("urbox_history");
    history = JSON.parse(history);
    history.pop();
    let backlink = null;
    if (history.length >= 1) {
        backlink = history[history.length - 1];
    }
    sessionStorage.setItem("urbox_history", JSON.stringify(history));
    _.app.views.main.router.navigate(backlink, {
        pushState: true,
        ignoreCache: true
    });
});

const KEY_CODE_DELETE = 8;
const KEY_CODE_TAB = 9;

const KEY_CODE_LEFT_ARROW = 37;
const KEY_CODE_RIGHT_ARROW = 39;

const KEY_CODE_HELP = 47;
const KEY_CODER_COLON = 58;

const KEY_CODE_SLEEP = 95;
const KEY_CODER_MULTIPLY = 106

const TOO_MUCH_LONG_PHONE_NUMBER = 15;

const INVALID_KEY_CODES = [
    KEY_CODE_LEFT_ARROW, KEY_CODE_RIGHT_ARROW, KEY_CODE_DELETE, KEY_CODE_TAB
];

function blockInvalidKey(event, item) {
    const isNumberFullKey = event.keyCode > KEY_CODE_SLEEP && event.keyCode < KEY_CODER_MULTIPLY;
    const isNumberLineKey = event.keyCode > KEY_CODE_HELP && event.keyCode < KEY_CODER_COLON;
    const isInvalidKey = isNumberLineKey || isNumberFullKey || INVALID_KEY_CODES.indexOf(event.keyCode);
    const isValidKey = !isInvalidKey;
    const isValidPhoneNumberLength = item.length < TOO_MUCH_LONG_PHONE_NUMBER;
    if (isValidPhoneNumberLength && isValidKey) {
        event.preventDefault();
    }
}

$$(document).on('keydown', '#inputCardNumber', function (event) {
    let cardNumber = $$("#inputCardNumber").val();
    cardNumber = cardNumber.replace(/ /gi, '');
    blockInvalidKey(event, cardNumber);
});

$$(document).on('input', '#inputCardNumber', function () {
    let card = $$("#inputCardNumber").val();
    card = card.replace(/[^0-9]/g, '');
    let card_text = card.replace(/(\d{4})/g, '$1 ').replace(/(^\s+|\s+$)/, '');
    $$('#inputCardNumber').val(card_text);
});


$$(document).on('keydown', '#inputPhoneNumber', function (event) {
    let phoneNumber = $$(".inputPhoneNumber").val();
    phoneNumber = phoneNumber.replace(/ /gi, '');
    blockInvalidKey(event, phoneNumber);
});


$$(document).on('input', '#inputPhoneNumber', function () {
    let phone = $$("#inputPhoneNumber").val();
    phone = phone.replace(/[^0-9]/g, '');
    $$('#inputPhoneNumber').val(phone);
    if (phone.length === 10) {
        $$('.button-login').addClass('phone_active');
        $$('.img-login-phone').addClass('hidden');
        $$('.img-active-phone').removeClass('hidden');
    } else {
        $$('.button-login').removeClass('phone_active');
        $$('.img-login-phone').removeClass('hidden');
        $$('.img-active-phone').addClass('hidden');
    }
});
// SignIn : Mã pin chỉ nhấp số
$$(document).on('keydown', '.format-password', function (event) {
    let pin = $$(".format-password").val();
    pin = pin.replace(/ /gi, '');
    blockInvalidKey(event, pin)
});
$$(document).on('input', '.format-password', function () {
    let pass = $$(".format-password").val();
    pass = pass.replace(/[^0-9]/g, '');
    $$('.format-password').val(pass);
});

//click không hiểm thị lại lần sau

$$(document).on('click', '#region', function () {
    let data = $$(this).attr('data-lang');
    $$('#lang-en').toggleClass('active');
    $$('#lang-vi').toggleClass('active');
    if (data === 'en') {
        window.location.href = "/lang/vi";
    } else {
        window.location.href = "/lang/en";
    }
});

let loginDebounce = debounce(function () {
    let pin = "";
    $$(".input-login").forEach(function (item) {
        pin += item.value;
    })
    if (pin.length === 6 && parseInt(pin) == pin) {
        $$("#password-input").val(pin)
        _.auth.clickSignIn('.wrong_pass')
    }
}, 500);
$$(document).on('keyup', ".input-login", loginDebounce)

//nút enter ở trang nhập mã thẻ
$$(document).on('keyup', '#inputCardNumber', function (event) {
    if (event.keyCode === 13 || event.which === 13 || event.code === "Enter") {
        event.preventDefault();
        $$("input").blur();
        _.auth.submitCardNumber()
    }
});
//nút enter ở trang signI8n
$$(document).on('keydown', '#password-input', function (event) {
    if (event.keyCode === 13 || event.which === 13 || event.code === "Enter") {
        event.preventDefault();
        $$("input").blur();
        _.auth.clickSignIn()
    }
});

//nút enter ở trang nhập mã thẻ
$$(document).on('keyup', '#inputCardNumber', function (event) {
    if (event.keyCode === 13 || event.which === 13 || event.code === "Enter") {
        event.preventDefault();
        $$("input").blur();
        _.auth.submitCardNumber()
    }
})

//nút enter ở trang nhập phone
$$(document).on('keyup', '#inputPhoneNumber', function (event) {
    let keyCode = event.keyCode || event.which;
    if (keyCode === 13) {
        event.preventDefault();
        $$("input").blur();
        _.auth.activePhone()
    }
});

//nút enter ở trang setpIn
$$(document).on('keyup', '.reset-pin', function (event) {
    let otp = "";
    $$(".input-otp").forEach(function (item) {
        otp += item.value;
    })
    if (event.keyCode === 13 && otp.length === 6) {
        $$("input").blur();
        _.auth.setPin()
    }
});


_.app.init();

_.link = function (link, time = 0) {
    _.app.preloader.show();
    if (time > 0) {
        setTimeout(function () {
            _.app.views.main.router.navigate(link, {
                reloadAll: true,
                reloadCurrent: true,
                ignoreCache: true
            });
        }, time)
    } else {
        _.app.views.main.router.navigate(link, {
            reloadAll: true,
            reloadCurrent: true,
            ignoreCache: true
        });
    }
}
_.resendOtp = function () {
    $$(".wrong_pass").hide();
    _.app.request({
        url: '/ajax/card/auth/resend',
        data: {},
        method: 'post',
        dataType: 'json', type: "post",
        beforeSend: function () {
            _.app.preloader.show();
        },
        complete: function () {
            _.app.preloader.hide();
        },
        error: function (j) {
            _.app.dialog.alert(handingErr(j), '')
        },
        success: function (j) {
            if (j.done !== 1) {
                if(j.msg.link && j.msg.link !== ""){
                    return _.redirect(j.msg.link)
                }
                $$('.input-otp').val('');
                try {
                    document.getElementById('setOtpInput').focus();
                } catch (e) {
                }
                $$(".wrong_pass").text(j.msg).show();
            } else {
                $$('#countdown').parent().show();
                $$('.resend-OTP').hide();
                _countDownOtp(59,true);
            }
        }
    })
}

_.auth = {
    submitCardNumber () {
        $$('.warning-home').hide();
        let cardInput = $$('#inputCardNumber');
        let _cardInput = cardInput.val().replace(/ /gi, '');
        if (_cardInput.length !== 16) {
            $$('.warning-home').show();
            cardInput.focus();
            return false;
        }
        let formData = _.app.form.convertToData('#form-register');
        _.app.request({
            url: $$('#form-register').attr('action'),
            data: formData,
            dataType: 'json',
            type: "post",
            beforeSend: function () {
                _.app.preloader.show();
            },
            complete: function () {
                _.app.preloader.hide();
            },
            error: function (j) {
                _.app.dialog.alert(handingErr(j), '')
            },
            success: function (a) {
                if (a.done === 1) {
                    if (a.data.link) {
                        if (a.data.reload === true)
                            _.redirect(a.data.link)
                        else {
                            _.link('/card')
                        }
                    } else {
                        _.redirect('/card')
                    }

                } else {
                    if (a.showPopup === 2) {
                        _.app.popup.open('.popup_install_app');
                    } else {
                        $$('.warning-home').text(a.msg).show();

                    }
                }
            }
        })
    },

    clickSignIn: function (errElement = '.warning-login') {
        $$(errElement).hide();
        let password = $$('#password-input');
        if (password.val().length !== 6) {
            if (errElement === '.warning-login') {
                $$(errElement).html("Mã PIN không chính xác").show();
            } else {
                $$(errElement).html("Mật khẩu không chính xác").show();
            }
            password.focus();
            return false;
        }
        let formData = _.app.form.convertToData('#form-login');
        _.app.request({
            url: $$('#form-login').attr('action'),
            data: formData,
            dataType: 'json',
            type: "post",
            beforeSend: function () {
                _.app.preloader.show();
            },
            complete: function () {
                _.app.preloader.hide();
            },
            error: function (j) {
                _.app.dialog.alert(handingErr(j), '')
            },
            success: function (a) {
                if (a.done === 1) {
                    if (a.data.reload === true) {
                        _.redirect('/card')
                    } else {
                        _.link('/card');
                    }
                } else {
                    $$('.input-pin').val('');
                    try {
                        document.getElementById('password-input').focus();

                    } catch (e) {
                    }
                    try {
                        document.getElementById('input-pin').focus();

                    } catch (e) {
                    }
                    $$(errElement).text(a.msg).show();
                }
            }
        });
    },

    activePhone: function () {
        let phoneNumber = $$("#inputPhoneNumber").val();
        if (phoneNumber.length === 10) {
            if (!_.is_phone(phoneNumber)) {
                $$("#phone-err-content").html("Số điện thoại không đúng");
                $$('#phone-err').removeClass('hidden');
                return false;
            }
            $$('#phone-err').addClass('hidden');
            let formData = _.app.form.convertToData('#form-activePhone');
            _.app.request({
                url: $$('#action').val(),
                data: formData,
                dataType: 'json',
                type: "post",
                beforeSend: function () {
                    _.app.preloader.show();
                },
                complete: function () {
                    _.app.preloader.hide();
                },
                error: function (j) {
                    _.app.dialog.alert(handingErr(j), '')
                },
                success: function (a) {
                    if (a.done === 1) {
                        _.link('/card?page=otp');
                    } else {
                        $$("#phone-err-content").html(a.msg);
                        $$('#phone-err').removeClass('hidden');
                    }
                }
            });
        } else {
            return false;
        }
    },
    setPin: function () {
        let pinA = "";
        let pinB = "";
        $$('.wrong_pass').hide()
        $$(".pinA").forEach(function (item) {
            pinA += item.value;
        })
        $$(".pinB").forEach(function (item) {
            pinB += item.value;
        })
        if (pinA.length !== 6) {
            $$('.wrong_pass').html("Mật khẩu không hợp lệ, vui lòng nhập lại").show()
            $$('.input-pin').val('');
            try {
                document.getElementById('input-pin').focus();

            } catch (e) {
            }
            return false;
        }
        if (pinA.length !== 6 || pinA != pinB || parseInt(pinA) != pinB) {
            $$('.input-pin').val('');
            try {
                document.getElementById('input-pin').focus();

            } catch (e) {
            }
            $$('.wrong_pass').html("Mật khẩu không đồng nhất, vui lòng nhập lại").show()
            return false;
        }

        let formData = _.app.form.convertToData('#form-setPin');
        formData.pinA = pinA;
        formData.pinB = pinB;
        _.app.request({
            url: $$('#form-setPin').attr('action'),
            data: formData,
            dataType: 'json',
            type: "post",
            beforeSend: function () {
                _.app.preloader.show();
            },
            complete: function () {
                _.app.preloader.hide();
            },
            error: function (j) {
                _.app.dialog.alert(handingErr(j), '')
            },
            success: function (a) {
                if (a.done === 1) {
                    _.link('/card');
                } else {
                    $$('.input-pin').val('');
                    try {
                        document.getElementById('input-pin').focus();

                    } catch (e) {
                    }
                    $$('.wrong_pass').text(a.msg).show();
                }
            }
        })
    },
    forgotPin: function (link) {

        _.app.request({
            url: link,
            data: {},
            dataType: 'json',
            type: "post",
            beforeSend: function () {
                _.app.preloader.show();
            },
            complete: function () {
                _.app.preloader.hide();
            },
            error: function (j) {
                _.app.dialog.alert(handingErr(j), '')
            },
            success: function (a) {
                if (a.done === 1) {
                    _.link(a.data.link);
                } else {
                    $$('.input-pin').val('');
                    try {
                        document.getElementById('input-pin').focus();
                    } catch (e) {
                    }
                    $$('.wrong_pass').text(a.msg).show();
                }
            }
        })
    },
    logout(){
        _.cookie.set('cardNum','',-1)
        _.app.request({
            url: '/ajax/card/auth/logout',
            data: {},
            dataType: 'json',
            type: "post",
            beforeSend: function () {
                _.app.preloader.show();
            },
            complete: function () {
                _.app.preloader.hide();
            },
            error: function (j) {
                window.location.href = '/card'
            },
            success: function (a) {
                window.location.href = '/card'
            }
        })

    }
}

//xu ly form nhap OTP, PIN
$$(document).on('keyup', ".input-otp", function () {
    let otp = "";
    $$(".input-otp").forEach(function (item) {
        otp += item.value;
    })
    $$('.wrong_pass').hide()
    if (otp.length === 6 && parseInt(otp) == otp) {
        setTimeout(function () {
            let formData = _.app.form.convertToData('#otp-form');
            formData.otp = otp;
            _.app.request({
                url: $$('#otp-form').attr('action'),
                data: formData,
                dataType: 'json',
                type: "post",
                beforeSend: function () {
                    _.app.preloader.show();
                },
                complete: function () {
                    _.app.preloader.hide();
                },
                error: function (j) {
                    _.app.dialog.alert(handingErr(j), '')
                },
                success: function (a) {
                    if (a.done === 1) {
                        $$('.wrong_pass').hide()
                        _.link('/card');

                    } else {
                        $$('.wrong_pass').text(a.msg).show();
                        $$('.input-otp').val('');
                        try {
                            document.getElementById('setOtpInput').focus();
                        } catch (e) {
                        }
                        _.app.utils.nextTick(function () {
                            $$('.wrong_pass').hide()
                        }, 3000);
                    }
                }
            })
        }, 1000)
    }
})


function getMobileOperatingSystem() {
    let userAgent = navigator.userAgent || navigator.vendor || window.opera;
    // Windows Phone must come first because its UA also contains "Android"
    if (/windows phone/i.test(userAgent)) {
        return "Windows Phone";
    }
    if (/android/i.test(userAgent)) {
        return "Android";
    }
    // iOS detection from: http://stackoverflow.com/a/9039885/177710
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return "iOs";
    }

    return null;
}

function selectStoreURL(operatingSystem) {
    let selector = {
        [IS_ANDROID]: URI_STORE_ANDROID,
        [IS_IOS]: URI_STORE_IOS,
        [IS_DESKTOP_ANDROID_BUTTON_PRESS]: URI_DIRECT_PLAYSTORE,
        [IS_DESKTOP_IOS_BUTTON_PRESS]: URI_DIRECT_APPSTORE
    }
    return selector[operatingSystem] ? selector[operatingSystem] : URI_DIRECT_PLAYSTORE;
}

_.onOpenClick = function (isDesktopButtonPress = null) {
    let operatingSystem = isDesktopButtonPress ? isDesktopButtonPress : getMobileOperatingSystem();
    let button = $$(".open-button");
    if (button != null && getMobileOperatingSystem() === IS_IOS) {
        button.attr("href", URI_UNIVERSAL_LINK);
    } else {
        _.app.utils.nextTick(function () {
            window.location.href = selectStoreURL(operatingSystem);
        }, 500);
    }
}
//init input
function inputInsideOtpInput  (item, event) {
    if (item.value.length > 1) {
        item.value = item.value[item.value.length - 1];
    }
    try {
        if (!_.is_int(item.value)) {
            item.value = '';
            return false;
        }
        if (item.value == null || item.value === '' || event.inputType === 'deleteContentBackward') {
            focusOnInput(item.previousElementSibling);
        } else {
            focusOnInput(item.nextElementSibling);
        }
    } catch (e) {
        // console.log(e);
    }
    //add class active OTP
    if ($$('.input-otp').length && item.value.length) {
        $$(item).addClass('input-otp-active');
    } else {
        $$(item).removeClass('input-otp-active');
    }

    // add class active resetPin
    if ($$('.reset-pin').length && $$('.reset-pin').val().length) {
        $$('.button-login').addClass('phone_active');
        $$('.img-login-phone').addClass('hidden');
        $$('.img-active-phone').removeClass('hidden');
    } else {
        $$('.button-login').removeClass('phone_active');
        $$('.img-login-phone').removeClass('hidden');
        $$('.img-active-phone').addClass('hidden');
    }
}

function focusOnInput(element) {
    element.focus();
    let val = element.value;
    element.value = "";
    setTimeout(() => {
        element.value = val;
    })
}

$$(document).on('keydown', '.focusToBackElement', onDocumentKeyDown);

function onDocumentKeyDown(event) {
    const target = event.target;
    let target_value = $$(target).val();
    const eventKeyPressValues = [event.which, event.keyCode];
    if (eventKeyPressValues.includes(KEY_CODE_DELETE) && target_value === '') {
        handleKeyCodeBackOnInput(target);
    }
}

function handleKeyCodeBackOnInput(target) {
    const previousElementSibling = target.previousElementSibling;
    if (previousElementSibling) {
        target.value = "";
        focusOnInput(previousElementSibling);
    }
}

let handingErr = function (j) {
    return j.status + " - Lỗi hệ thống";
}

function debounce(func, wait) {
    let timeout;

    return function () {
        let context = this,
            args = arguments;

        let executeFunction = function () {
            func.apply(context, args);
        };

        clearTimeout(timeout);
        timeout = setTimeout(executeFunction, wait);
    };
}


function addHistory() {
    let home = "/" + site_name;
    //  let a = _.app.views.main.router.previousRoute.url;
    let a = _.app.views.main.router.currentRoute.url;
    // a = a.replace("&dialog=2","");
    let his = sessionStorage.getItem('urbox_history');
    if (his === "" || his == null) {
        sessionStorage.setItem("urbox_history", JSON.stringify([]));
    }
    his = JSON.parse(his);
    let b = his ? his[his.length - 1] : [];
    if (typeof a !== 'undefined') {
        if (a === "/") a = home;
        if (a.charAt(0) === "/" && a.charAt(1) === "?") {
            let s = a.substr(1);
            a = home + s;
        }
        //xoá subfix trang voucher
        a = a.replace('&tab=2', '');
        a = a.replace('&tab=1', '');

        let order = a.split('&order=');
        a = order[0];
        if (his == null) his = [home]; // sửa lỗi session rỗng khi load trang
        if (his[0] != home) his.unshift(home);
        if (b !== a) his.push(a);
        if (a === home) {
            his = [a];
        }
    } else {
        a = home;
        his = [a];
    }
    sessionStorage.setItem("urbox_history", JSON.stringify(his));
    _.app.preloader.hide();
}

function iOSversion() {
    if (/iP(hone|od|ad)/.test(navigator.platform)) {
        // supports iOS 2.0 and later: <http://bit.ly/TJjs1V>
        var v = (navigator.appVersion).match(/OS (\d+)_(\d+)_?(\d+)?/);
        return [parseInt(v[1], 10), parseInt(v[2], 10), parseInt(v[3] || 0, 10)];
    }
}
