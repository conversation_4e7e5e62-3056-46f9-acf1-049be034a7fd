_.mod = {}
_.data = {}

// Dom7
var $ = Dom7;

// Init App
var app = new Framework7({
  id: 'vn.urbox.loyal',
  root: '#app',
  theme: 'ios',
  routes: routes,
  view: {
    reloadPages: true,
    domCache: true,
    history: false,
    iosDynamicNavbar: false,
    pushState: true,
    pushStateRoot: BASE_URL,
    pushStateAnimate: false,
    pushStateOnLoad: false,
    pushStateAnimateOnLoad: false,
    pushStateSeparator: ""
  },
  init: false
});
//config GRAB
const openIdUrl = GrabID.GrabUrls.STAGING
const URL_API_TOP_UP_GRAB_POINT = "https://apia.urbox.dev/api/v1/partner-connection/grab/plus-point";
const GRAB_CLIENT_ID = "a4a59d2fd88d4cb19625eeecf5b66040";
const GRAB_REDIRECT_URL = "https://page.urbox.dev/grab.reward/gift/redirect";
const URL_API_GET_USER_INFO = "https://api.stg-myteksi.com/grabid/v1/oauth2/userinfo";
let appConfig = {
  clientId: GRAB_CLIENT_ID,
  redirectUri: GRAB_REDIRECT_URL,
  scope: ['openid'].join(' '),
  acrValues: {
    service: 'PASSENGER',
    consentContext: {
      countryCode: 'VN'
    }
  },
}
let application = new GrabID(openIdUrl, appConfig)
//end config

app.on('pageInit', function (page) {
  // loadCaptcha();
  if (page.name === "redirect") {
    let returnUri = GrabID.getLoginReturnURI();
    if (window.location.search !== '') {
      try {
        GrabID.handleAuthorizationCodeFlowResponse()
        let tokenResult = GrabID.getResult();
        makeTestEndpointRequest(tokenResult, 2);
        // window.location.assign(returnUri)
      } catch (e) {
        let errObj = JSON.parse(e.message)
        let errorHtml = `<h2>${errObj.name}</h2><p>${errObj.message}</p>`
        errorHtml += `<p>Redirecting to ${returnUri} in 10 seconds.</p>`
        document.getElementById('errorMsg').innerHTML = errorHtml
        setTimeout(() => {
          window.location.assign(returnUri)
        }, 10000)
      }
    } else if (window.location.hash !== '') {
      try {
        GrabID.handleImplicitFlowResponse()
        let tokenResult = GrabID.getResult();
        makeTestEndpointRequest(tokenResult, 2);
        // window.location.assign(returnUri)
      } catch (e) {
        let errObj = JSON.parse(e.message)
        let errorHtml = `<h2>${errObj.name}</h2><p>${errObj.message}</p>`
        errorHtml += `<p>Redirecting to ${returnUri} in 10 seconds.</p>`
        document.getElementById('errorMsg').innerHTML = errorHtml
        setTimeout(() => {
          window.location.assign(returnUri)
        }, 10000)
      }
    }
  }
})
app.init();
_.mod.grab = {
  code: function (id, type) {
    let formData = app.form.convertToData('#token-form');
    var code = $('#code').val();
    var appName = $('#appName').val();
    $('.error-message').addClass('hidden');
    if (code === '') {
      $('#errPut').html('*Mã không tồn tại - Code does not exist.').removeClass('hidden');
      $('#code').focus();
      return false;
    }
    app.request({
      url: BASE_URL + '/ajax/index/code?rand' + Math.random() + '&' + BASE_TOKEN_NAME + '=' + _.getCSRFToken(),
      data: {id: id, code: code, appName: appName, token: formData.___token || ''},
      dataType: 'json', type: "post",
      beforeSend: function () {
        app.preloader.show();
      },
      complete: function () {
        loadCaptcha();
        app.preloader.hide();
      },
      success: function (j) {
        if (j.done == 1) {
          $('.btn').addClass("btn-active");
          if (type == 2) {
            _.redirect(j.data.link)
          }
        } else {
          $('#errPut').html(j.msg).removeClass('hidden');
          $('#code').focus();
        }
      }
    });

  },
  topup: function (id) {
    let formData = app.form.convertToData('#token-form');
    $('.btn').removeClass("btn-active");
    var code = $('#code').val();
    var appName = $('#appName').val();
    $('.error-message').addClass('hidden');
    if (code == '') {
      $('#errPut').html('*Mã không tồn tại - Code does not exist.').removeClass('hidden');
      $('#code').focus();
      return false;
    }
    app.request({
      url: BASE_URL + '/ajax/index/topUpGrab?rand' + Math.random() + '&' + BASE_TOKEN_NAME + '=' + _.getCSRFToken(),
      data: {id: id, code: code, appName: appName, token: formData.___token || ''},
      dataType: 'json', type: "post",
      beforeSend: function () {
        app.preloader.show();
      },
      complete: function () {
        loadCaptcha();
        app.preloader.hide();
      },
      success: function (response) {
        if (response.done === 1) {
          window.localStorage.removeItem('grabid:access_token');
          $('.btn').addClass("btn-active");
          sessionStorage.setItem('grab_point', response.data.grab_point)
          sessionStorage.setItem('grab_code', response.data.grab_code)
          application.makeImplicitAuthorizationRequest();
        } else {
          $('#errPut').html(response.msg).removeClass('hidden');
          $('#code').focus();
        }
      }

    });
  },
  receiver: function (id) {
    let formData = app.form.convertToData('#token-form');
    $('.error-message').addClass('hidden');
    var phone = $('#phone').val();
    var fullname = $('#fullname').val();
    var email = $('#email').val();
    if (phone == '') {
      $('#errPhone').html('Bạn chưa nhập số điện thoại-You did not enter a phone number.').removeClass('hidden');
      $('#phone').focus();
      return false;
    }
    if (!_.is_phone(phone)) {
      $('#errPhone').html('Số điện thoại bạn nhập chưa hợp lệ.').removeClass('hidden');
      $('#phone').focus();
      return false;
    }
    if (email == '') {
      $('#errEmail').html('Bạn chưa nhập email -You did not enter email.').removeClass('hidden');
      $('#email').focus();
      return false;
    }
    if (!_.is_email(email)) {
      $('#errEmail').html('Email bạn nhập chưa hợp lệ.').removeClass('hidden');
      $('#email').focus();
      return false;
    }
    var city = $('#city').val();
    if (city == 0) {
      $('#errCity').html('Bạn chưa chọn tỉnh thành phố - The phone number you entered is not valid.').removeClass('hidden');
      $('#city').focus();
      return false;
    }
    var district_id = $('#district_id').val();
    if (district_id == 0) {
      $('#errDistrict').html('Bạn chưa chọn quận huyện - You have not selected a district.').removeClass('hidden');
      $('#district_id').focus();
      return false;
    }
    var ward_id = $('#ward_id').val();
    var address = $('#address').val();
    var note = $('#note').val();
    var appName = $('#appName').val();
    if (address == '') {
      $('#eraddress').html('Bạn chưa nhập địa chỉ - You do not enter an address .').removeClass('hidden');
      $('#address').focus();
      return false;
    }
    if ($('#stickVal').is(':checked')) {
      app.request({
        url: BASE_URL + '/ajax/index/receiver?rand' + Math.random() + '&' + BASE_TOKEN_NAME + '=' + _.getCSRFToken(),
        data: {
          id: id,
          phone: phone,
          email: email,
          city: city,
          note: note,
          address: address,
          ward_id: ward_id,
          district_id: district_id,
          fullname: fullname,
          appName: appName,
          token: formData.___token || ''
        },
        dataType: 'json', type: "post",
        beforeSend: function () {
          app.preloader.show();
        },
        complete: function () {
          loadCaptcha();
          app.preloader.hide();
        },
        success: function (j) {
          if (j.done == 1) {
            _.redirect(j.data.link)
          } else {
            app.dialog.close();
            app.dialog.alert(j.msg, ['Thông báo']);
          }
        }
      });
    } else {
      app.dialog.close();
      app.dialog.alert('Tôi tự nguyện cung cấp thông tin cho đối tác UrBox với mục đích giao quà - I voluntarily provide information to UrBox for the purpose of delivering gifts', ['Thông báo']);
    }
  },
  stickYes: function () {
    if ($('#stickVal').is(':checked')) {
      $('.btn').addClass("btn-active");
    } else {
      $('.btn').removeClass("btn-active");
    }
  },
  district: function (obj) {
    var idCity = $(obj).val();
    if (idCity > 0) {
      app.request({
        url: BASE_URL + '/ajax/index/district?rand' + Math.random() + '&' + BASE_TOKEN_NAME + '=' + _.getCSRFToken(),
        data: {idCity: idCity},
        dataType: 'json', type: "post",
        beforeSend: function () {
          app.preloader.show();
        },
        complete: function () {
          app.preloader.hide();
        },
        success: function (j) {
          if (j.done == 1) {
            if (j.data.district) {
              var district = j.data.district;
              var optDis = '';
              for (var i = 0; i < district.length; i++) {
                if (district[i]['id']) {
                  optDis += '<option value="' + district[i]['id'] + '">' + district[i]['title'] + '</option>'
                }
              }
              if (optDis != '') {
                $('#district_id').html(' <option value="0">Chọn - Select </option>' + optDis);
              } else {
                $('#district_id').html(' <option value="0">Chọn - Select </option>');
              }
            }
          }
        }
      });
    }
  },
  ward: function (obj) {
    var idDis = $(obj).val();
    if (idDis > 0) {
      app.request({
        url: BASE_URL + '/ajax/index/ward?rand' + Math.random() + '&' + BASE_TOKEN_NAME + '=' + _.getCSRFToken(),
        data: {id: idDis},
        dataType: 'json', type: "post",
        beforeSend: function () {
          app.preloader.show();
        },
        complete: function () {
          app.preloader.hide();
        },
        success: function (j) {
          if (j.done == 1) {
            if (j.data.ward) {
              var ward = j.data.ward;
              var optWard = '';
              for (var i = 0; i < ward.length; i++) {
                if (ward[i]['id']) {
                  optWard += '<option value="' + ward[i]['id'] + '">' + ward[i]['title'] + '</option>'
                }
              }
              if (optWard != '') {
                $('#ward_id').html(' <option value="">Chọn - Select </option>' + optWard);
              } else {
                $('#ward_id').html(' <option value="">Chọn - Select </option>');
              }
            }
          }
        }
      });
    }
  },
  chooseNetwork: function (type) {
    $(".brand-logo").css("opacity", 0.4).css("filter", 'grayscale(100%)');
    $("#" + type).css("opacity", 1).css("filter", 'grayscale(0%)');
    $("#network").val(type)
  },
  receiverTopup: function (id) {
    $('.error-message').addClass('hidden');
    var phone = $('#phone').val();
    var network = $('#network').val();
    //var type = $('#network_type').prop('checked');
    var type = $('input[name=network_type]:checked').val();
    if (network === '') {
      $('#errNetwork').html('Vui lòng chọn mạng di động - Choose your mobile network.').removeClass('hidden');
      return false;
    }
    if (phone === '') {
      $('#errPhone').html('Bạn chưa nhập số điện thoại-You did not enter a phone number.').removeClass('hidden');
      return false;
    }
    if (!_.is_phone(phone)) {
      $('#errPhone').html('Số điện thoại bạn nhập chưa hợp lệ.').removeClass('hidden');
      return false;
    }
    app.dialog.confirm("Bạn có chắc chắn muốn nạp tiền vào thuê bao " + phone, "Xác nhận", function () {
      app.request({
        url: BASE_URL + '/ajax/index/payVTC?rand' + Math.random() + '&' + BASE_TOKEN_NAME + '=' + _.getCSRFToken(),
        data: {
          id: id,
          phone: phone,
          network: network,
          type: type,
        },
        dataType: 'json', type: "post",
        beforeSend: function () {
          app.preloader.show();
        },
        complete: function () {
          app.preloader.hide();
        },
        success: function (j) {
          if (j.done === 1) {
            _.redirect('/grab.reward/gift/finish/' + id + "/" + j.data.type)
          } else {
            app.dialog.close();
            app.dialog.alert(j.msg, ['Thông báo']);
          }
        }
      });
    })

  },
}

$("#code").on('change keyup', function () {
  $("#link-btn").removeClass('btn-gray').addClass('btn-active');
});

$("#phone").on('change keyup', function () {
  var phone = $('#phone').val();
  var network = $('#network').val();
  if (phone !== "" && network !== "")
    $("#link-btn").removeClass('btn-gray').addClass('btn-active');
});
$(".brand-logo").on('click', function () {
  var phone = $('#phone').val();
  var network = $('#network').val();
  if (phone !== "" && network !== "")
    $("#link-btn").removeClass('btn-gray').addClass('btn-active');
});


function makeTestEndpointRequest(tokenResult, typeException) {
  window.fetch(URL_API_GET_USER_INFO, {
    method: 'GET',
    headers: {
      'Accept-Content': 'application/json; charset=utf-8',
      Authorization: "Bearer " + tokenResult.accessToken,
    },
    mode: 'cors',
  })
      .then((response) => {
        if (response.ok) {
          response.json().then((userInfo) => {
            topUpGrabPoint(userInfo, typeException);
          });
        } else {
          application.makeImplicitAuthorizationRequest();
        }
      }).catch((exception) => {
    handleException(typeException, exception)
  })
}

function topUpGrabPoint(userInfo, typeException) {
  let partnerUserId = userInfo.sub;
  let point = sessionStorage.getItem('grab_point');
  let codex = sessionStorage.getItem('grab_code');

  app.request({
    url: URL_API_TOP_UP_GRAB_POINT,
    data: {
      partner_user_id: partnerUserId,
      point: point,
      codex: codex
    },
    dataType: 'json',
    type: "post",
    beforeSend: function () {
      app.preloader.show();
    },
    complete: function () {
      app.preloader.hide();
    },
    success: function (response) {
      if (response && response.status) {
        _.redirect('/grab.reward/gift/success')
      } else {
        handleException(typeException)
      }
    },
    error: function (error) {
      handleException(typeException)
    }
  });
}


turnstile.ready(function () {
  $('#cf-turnstile-token').hide();
  turnstile.render('#cf-turnstile-token', {
    callback: function (token) {
      if (!token){
        console.log(`Challenge Error ${token}`);
        $('#cf-turnstile-token').show();
      }
    },
  });
});

function loadCaptcha(){
  turnstile.reset('#cf-turnstile-token');
  $('#cf-turnstile-token').hide();
}

function handleException(typeException, exception = null) {
  let returnUri = GrabID.getLoginReturnURI();
  if (typeException === 1) {
    $('#errPut').html("Có lỗi xảy ra. Vui lòng tải lại trình duyệt").removeClass('hidden');
    $('#code').focus();
  } else {
    let errorHtml = `<h2>Đã xảy ra lỗi trong quá trình nạp điểm. Quý khách sẽ được chuyển về màn hình để nhập lại thông tin sau 10 giây</p>`
    errorHtml += `<br/><small>(${exception.message || ''})<small>`;
    // errorHtml += `<p>Redirecting to ${returnUri} in 10 seconds.</p>`
    document.getElementById('errorMsg').innerHTML = errorHtml
    setTimeout(() => {
      window.location.assign(returnUri)
    }, 10000)
  }
}