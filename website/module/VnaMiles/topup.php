<?php

class TopUpForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
        global $display;
		
        $card = System::$data['card'];
        $_token = $_SESSION['vna_authen'];

        #$_token = VnaMile::getAuth($card['token']);
        #if(empty($_token) || $_token != md5($card['id'].$card['token'])){
        if(empty($_token) || $_token != $card['token']){
            //Url::redirect('vna/miles/'.$card['token']);
        }
		$app = System::$data['app'];
		$display->add("app",$app);
		CookieLib::set('error_msg',"", TIME_NOW);
        $display->add("card",$card);
		$display->add('cur_lang', Language::$activeLang);
		$display->add('langs', Language::$listLangOptions);
        return $display->output('topup');
    }
}