<?php

class VnaMiles extends Module
{
    function __construct($block)
    {
        Module::initialize($block);
		Form::addCss('website/libs/_f7.4.5.10/css/framework7.bundle.min.css');
		Form::addJs('website/libs/_f7.4.5.10/js/framework7.bundle.js');
        Form::addCss('website/style/default.css');
        Form::addCss('website/style/vnaMiles/style.css');
        Form::addJs('website/libs/_f7/plugins/keyboard/js/framework7.keypad.min.js');
        Form::addJs('website/javascript/vnaMiles/routes.js');
        Form::addJs('website/javascript/vnaMiles/app.js');
		$page = Url::getParam('page','home');
//		$_SESSION['test']  ="redis-session";
//		ini_set('session.save_handler', 'redis');
//		ini_set('session.save_path', "tcp://**********:63791");
		
		$app = !empty(System::$data['url']['query'][1]) ? System::$data['url']['query'][1] : '';
		$app = preg_replace('/[^A-Za-z0-9\-]/', '', $app);
		$app = trim($app);
		
		$token = !empty(System::$data['url']['query'][2]) ? System::$data['url']['query'][2] : '';
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$token = trim($token);
		if($token == "" && $page != "error"){
			Url::redirect("vna/miles?page=error");
		}
		System::$data['token'] = $token;
		$card = Point::getByToken($token);
		#echo md5($card['id']."123456".CRYPT_KEY);
		if(empty($card) && !in_array($page,["error","lock"])){
			Url::redirect("vna/miles/{$token}?page=error");
		}
		if(!empty($card) && $card['process'] > 2 && $page != "success"){
			Url::redirect("vna/miles/{$card['token']}?page=success");
		}
		if((isset($card['lock_time'])) && $card['lock_time'] > TIME_NOW - 3600  && $page != "lock"){
			Url::redirect("vna/miles/{$token}?page=lock");
		}
		
		System::$data['card'] = $card;
		if(!in_array($page,["error","lock"])) {
			System::$data['app'] = Point::getApp( $card['app_id'] );
		}
		if((!isset(System::$data['app']) || empty(System::$data['app']) || System::$data['app']['status'] == 1) && !in_array($page,["error","lock"])){
			if($page != "error") Url::redirect("vna/miles/{$token}?page=error");
		}
		
        switch ($page) {
            case 'topup':
                require_once 'topup.php';
                $form = 'TopUpForm';
                break;
            case 'success':
                require_once 'success.php';
                $form = 'SuccessForm';
                break;
            case 'lock':
                require_once 'lock.php';
                $form = 'LockForm';
                break;
            case 'error':
                require_once 'error.php';
                $form = 'ErrorForm';
                break;
            default:
                require_once 'topup.php';
                $form = 'TopUpForm';
                break;
        }
        Module::addForm(new $form());
    }
}
