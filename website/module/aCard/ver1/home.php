<?php
class HomeForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        #Danh muc
        $parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);
        $all_brand = Url::getParam('all_brand',0);
        $per_page = Url::getParamInt('per_page', 10);
        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        // lấy danh sách category
        $category = BalanceVer1::getCategory($parent_id);

		$card = System::$data['card'];
        //lấy danh sách brand đã xem
        $viewed_brand = BalanceVer1::getBrandFromCache();
        // lấy danh sách brand nổi bật hiển thị ra trang chủ
        $data = BalanceVer1::HotBrand($token);
        $display->add('category',$category);
		$allowBrand = Balance::brandInGiftSet(421,$card['gift_id']);
        $display->add('allowBrand',$allowBrand);
        $display->add('viewed_brand',$viewed_brand);
        $display->add('token',$token);
        $display->add('all_brand',$all_brand);
        $display->add('data',$data);
        $display->add('per_page',$per_page);
        $display->add('card',$card);
        $display->add('app',System::$data['apps']);
        return $display->output(System::$data['version']."home");
    }
}