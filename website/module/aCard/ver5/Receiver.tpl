<div class="page" data-name="receiver">
    <header class="navbar has-shadow">
        <div class="navbar-inner navbar-center header display-flex sliding">
            <a href="" class="navbar-brand" onclick="_.click.link('/card')">
                {if $app.logo!=''}
                    <img src="{$app.logo}" alt="">
                {else}
                    <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/logo-urbox.svg" alt="">
                {/if}
            </a>
            <div class="nav-right">
                <strong> {$card.money|number_format:0:",":"."} {VIEW_CURRENCY}</strong>
                <span>Hạn sử dụng: <b>{if $card.expired_time > 0}{date('d/m/Y',$card.expired_time)}{else}Vô hạn{/if}</b></span>
            </div>
        </div>
    </header>
    <div class="page-content page-receiver">
        <div class="custom-container">
            <div class="section-receiver">
                <div class="row no-gap">
                    <div class="col-100 breadcrumb">
                        <a href="" onclick="_.click.link('/card')">Trang chủ</a>
                        <a href="" class="breadcrumb-active"> > Thông tin nhận hàng</a>
                    </div>
                </div>
                <div class="delivery-info">
                    <div class="form-delivery">
                        <div class="title">
                            Thông tin <span>nhận hàng</span>
                        </div>
                        <form action="" class="" id="theForm">
                            <div class="form-input width-layout bg-layout">
                                <div class="bl-container">
                                    <div class="form-input-content">
                                        <div class="body-form-input">
                                            <div class="row no-gap">
                                                <div class="form-group col-66">
                                                    <fieldset>
                                                        <legend>Tên của bạn</legend>
                                                        <input type="text" id="name" class="form-control formRequire"
                                                               name="name"
                                                               data-require="name" placeholder="Tên của bạn">

                                                    </fieldset>
                                                    <div id="E-name" class="error-message hidden mT5">
                                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                             alt="">
                                                        Họ và tên không được để trống
                                                    </div>
                                                </div>
                                                <div class="form-group col-33">
                                                    <fieldset>
                                                        <legend>Số điện thoại</legend>
                                                        <input type="text" id="phone" class="form-control formRequire"
                                                               name="phone"
                                                               data-require="phone" placeholder="Số điện thoại" required>
                                                    </fieldset>
                                                    <div id="E-phone" class="error-message mT5 hidden">
                                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                             alt="">
                                                        Số điện thoại không hợp lệ
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row no-gap">
                                                <div class="form-group col-100">
                                                    <fieldset>
                                                        <legend>Địa chỉ Email</legend>
                                                        <input type="text" id="email" class="form-control formRequire"
                                                               name="email"
                                                               data-require="email" placeholder="<EMAIL>" required>

                                                    </fieldset>
                                                    <div id="E-email" class="error-message mT5 hidden">
                                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                             alt="">
                                                        Email không hợp lệ
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row no-gap">
                                                <div class="form-group col">
                                                    <fieldset class="fieldset-type-2" onclick="_.app.popup.open('#popup-city')">
                                                        <legend>Tỉnh/Thành phố</legend>
                                                        <input type="hidden" class="formRequire" id="city"
                                                               data-require="int"
                                                               name="city" required>
                                                        <input type="text"
                                                               readonly
                                                               id="text-city" placeholder="Chọn"
                                                               class="form-control has-dropdown">

                                                    </fieldset>
                                                    <div id="E-city" class="error-message mT5 hidden">
                                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                             alt="">
                                                        Bạn chưa chọn Tỉnh/Thành phố
                                                    </div>
                                                </div>
                                                <div class="form-group col">
                                                    <fieldset class="fieldset-type-2" onclick="_.app.popup.open('#popup-district')">
                                                        <legend>Quận/Huyện</legend>
                                                        <input type="hidden" class="formRequire" id="district"
                                                               data-require="int"
                                                               required name="district" data-name="district">
                                                        <input type="text"
                                                               id="text-district" readonly placeholder="Chọn"
                                                               class="form-control has-dropdown">

                                                    </fieldset>
                                                    <div id="E-district" class="error-message mT5 hidden">
                                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                             alt="">
                                                        Bạn chưa chọn Quận/Huyện
                                                    </div>
                                                </div>
                                                <div class="form-group col">
                                                    <fieldset class="fieldset-type-2" onclick="_.app.popup.open('#popup-ward')">
                                                        <legend>Phường/Xã</legend>
                                                        <input type="hidden" class="formRequire" id="ward"
                                                               data-require="int" required
                                                               name="ward" data-name="ward">
                                                        <input type="text"
                                                               id="text-ward"
                                                               readonly placeholder="Chọn"
                                                               class="form-control has-dropdown">
                                                    </fieldset>
                                                    <div id="E-ward" class="error-message mT5 hidden">
                                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                             alt="">
                                                        Phường/Xã không được để trống
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <fieldset>
                                                    <legend>Địa chỉ chi tiết</legend>
                                                    <input type="text" class="form-control formRequire" name="street"
                                                           id="street"
                                                           data-require="name" placeholder="Số nhà, Tên đường">
                                                </fieldset>
                                                <div id="E-street" class="error-message mT5 hidden">
                                                    <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/error.svg"
                                                         alt="">
                                                    Địa chỉ không được để trống
                                                </div>
                                            </div>
                                        </div>
                                        <div style="border: 3px solid #FFD6B0; border-radius: 20px; background: #FFF6ED; padding: 24px 32px; color: #232323; text-align: justify;  font-weight: 400; line-height: 1.3; max-width: 800px; margin: auto; margin-top: 16px;">
                                            <span>Quý khách vui lòng sử dụng địa chỉ theo <b>đơn vị hành chính cũ</b> trong thời gian này để đảm bảo việc xử lí đơn hàng được chính xác.</span> 
                                        </div>
                                        <div class="footer-form-input">
                                            <div class="confirm" onclick="_.mod.click.validate()">
                                                <a class="btn-confirm-gift" id="btn-submit" href="javascript:void(0)">Xác
                                                    nhận</a>
                                            </div>
                                            <div class="back" onclick="_.click.link('/card?page=gift&id={$gift.id}')">
                                                <a class="btn-confirm-gift">
                                                    Trở lại
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </form>
                    </div>
                    <div class="gift-info">
                        <div class="title">
                            Đơn hàng <span>của bạn</span>
                        </div>
                        <div class="product">
                            <div class="image">
                                {if $gift.image}
                                    <img src="{$gift.image}" alt="">
                                {else}
                                    <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/image-placeholder.png"
                                         alt="">
                                {/if}
                            </div>
                            <div class="product-title">
                                {$gift.title}
                            </div>
                        </div>
                        {if time() > 1642697999 && time() < 1644512399}
{*                        <div class="notice-tet mT10">*}
{*                            Lưu ý: Do thời gian nghỉ Tết, các đơn hàng đặt sau ngày 22/1/2022 sẽ được giao đi từ ngày 8/2/2022*}
{*                        </div>*}
                        {/if}
                    </div>
                </div>
                <div class="popup popup-swipe-to-close confirm-buy-sheet">
                    <form action="" class="" id="theFormNote">
                        <div class="sheet-modal-inner">
                            <div class="sheet-title">Lưu ý cho đơn vị giao hàng</div>
                            <div class="form-group">
                                <label class="checkbox checkbox-label" for="checkbox-1">
                                    <input type="checkbox" id="checkbox-1" value="Gọi điện thoại trước khi giao hàng"
                                           name="notes">
                                    <i class="icon-checkbox"></i>
                                    Gọi điện thoại trước khi giao hàng
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox checkbox-label" for="checkbox-2">
                                    <input type="checkbox" id="checkbox-2"
                                           value="Giao hàng trong giờ hành chính từ thứ 2 đến thứ 6" name="notes">
                                    <i class="icon-checkbox"></i>
                                    Giao hàng trong giờ hành chính từ thứ 2 - thứ 6.
                                </label>
                            </div>
                            <div class="more-note">
                                <strong>Lưu ý khác: </strong>
                                <textarea name="notes_input" id="notes_input" rows="5" class="notes-input form-control"
                                          placeholder="Viết lưu ý ..."></textarea>
                            </div>

                            <div class="sheet-button mT20 mB20 row">
                                <div class="col confirm" onclick="_.mod.click.confirm({$gift.id})">
                                    <a href="javascript:void(0)"
                                       class="btn-popup-gift">Gửi</a>
                                </div>
                                <div class="col cancel" onclick=" _.app.popup.close()">
                                    <a href="javascript:void(0)" class="btn-close-gift">Đóng lưu ý</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="popup popup-swipe-to-close popup-success">
                    <div class="popup-content">
                        <div class="checked-image">
                            <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/checked.svg"
                                 alt="">
                        </div>
                        <div class="checked-content">
                            <div class="title">Đặt hàng thành công</div>
                            <div class="delivery-time">
                                Thời gian giao hàng dự kiến<br>
                                <span id="delivery-time"></span>
                            </div>
                        </div>
                        <div class="gift-content">
                            <div class="transaction-info">
                                <div>Mã đơn hàng: <span id="voucher-id"></span></div>
                                <div>Thời gian mua hàng: <span id="voucher-time"></span></div>
                            </div>
                            <div class="gift-info">
                                <div class="gift-image">
                                    {if $gift.image}
                                        <img src="{$gift.image}" alt="">
                                    {else}
                                        <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/image-placeholder.png"
                                             alt="">
                                    {/if}
                                </div>
                                <div class="name">
                                    {$gift.title}
                                </div>
                            </div>
                        </div>
                        <div class="countdown" onclick="_.click.link('/card?page=account')">
                            Thông tin đơn hàng &nbsp;(<span class="countdown-time" id="countdown-redirect">15s</span>)
                        </div>
                    </div>
                </div>
                <div class="popup custom-popup" id="popup-city">
                    <div class="page">
                        <div class="navbar">
                            <div class="navbar-inner">
                                <div class="text-align-center w100p pL15 pR15">
                                    <div class="text-align-center f18 f-600"> Tỉnh/Thành phố</div>
                                    <input id="input-city" onkeyup="_.mod.click.searchDis(this.id)" class="input-search"
                                           type="text" placeholder="Tìm tỉnh thành">
                                </div>
                            </div>
                        </div>
                        <div class="page-content">
                            <div class="list listSort">
                                <ul id="ul-input-city">
                                    {foreach from=$city item=cat name=i}
                                        <li class="mB0"
                                            onclick="_.mod.click.getDisOfCity('#text-city','{$cat.title}',{$cat.id})">
                                            <label class="item-radio item-content pR15">
                                                <input type="radio" name="radio-city" data-name="{$cat.title}"
                                                       value="{$cat.id}"/>
                                                <div class="item-inner">
                                                    <div class="display-flex pL5">
                                                        <img src="/mobile/style/card/ver5/images/pin.svg" alt="">
                                                        <div class="item-title f16 pL15 text-align-left">
                                                            <span>{$cat.title}</span></div>
                                                    </div>
                                                    <img src="/mobile/style/card/ver5/images/ico-arrow.svg" width="16"
                                                         alt="">
                                                </div>

                                            </label>
                                        </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                        <div class="popup-footer">
                            <a href="javascript:void(0)" onclick="_.app.popup.close()"><img
                                        src="/mobile/style/card/ver5/images/cancel.svg" alt=""></a>
                        </div>
                    </div>
                </div>
                <div class="popup custom-popup" id="popup-district">
                    <div class="page">
                        <div class="navbar">
                            <div class="navbar-inner">
                                <div class="text-align-center w100p pL15 pR15">
                                    <div class="text-align-center f17 f-700">Địa điểm</div>
                                    <input id="dis" onkeyup="_.mod.click.searchDis(this.id)" class="input-search"
                                           type="text" placeholder="Tìm Quận/Huyện">
                                </div>
                            </div>
                        </div>
                        <div class="page-content">
                            <div class="list listSort">
                                <ul id="ul-dis">
                                    <li class="pT20 pB20 text-align-center">Bạn cần chọn Tỉnh/Thành phố trước</li>
                                </ul>
                            </div>
                        </div>
                        <div class="popup-footer">
                            <a href="javascript:void(0)" onclick="_.app.popup.close()"><img
                                        src="/mobile/style/card/ver5/images/cancel.svg" alt=""></a>
                        </div>
                    </div>
                </div>
                <div class="popup custom-popup" id="popup-ward">
                    <div class="page">
                        <div class="navbar">
                            <div class="navbar-inner">
                                <div class="text-align-center w100p pL15 pR15">
                                    <div class="text-align-center f17 f-700">Địa điểm</div>
                                    <input id="input-ward" onkeyup="_.mod.click.searchDis(this.id)" class="input-search"
                                           type="text" placeholder="Tìm Xã/Phường">
                                </div>
                            </div>
                        </div>
                        <div class="page-content">
                            <div class="list listSort">
                                <ul id="ul-input-ward">
                                    <li class="pT20 pB20 text-align-center">Bạn cần chọn Xã/Phường trước</li>
                                </ul>
                            </div>
                        </div>
                        <div class="popup-footer">
                            <a href="javascript:void(0)" onclick="_.app.popup.close()"><img
                                        src="/mobile/style/card/ver5/images/cancel.svg" alt=""></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <section class="hotline-physical mT125">
            Quý khách cần hỗ trợ? Liên hệ hotline: <br>
            <a href="tel:{UB_CUSTOMER_PHONE_VALUE}">{UB_CUSTOMER_PHONE}</a>
        </section>
    </div>
</div>
