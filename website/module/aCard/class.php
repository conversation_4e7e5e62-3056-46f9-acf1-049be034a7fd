<?php

	class aCard extends Module
	{
		function __construct($block)
		{
			parent::__construct($block);

			Module::initialize($block);
			Form::addCss('website/libs/_f7.4.5.10/css/framework7.bundle.min.css');
			Form::addJs('website/libs/_f7.4.5.10/js/framework7.bundle.js');
            //SessionLib::del('auth');
			$token = CookieLib::get("cardNum");
			$token = $tokenCache = StringLib::clean_value($token);
			$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
			$token_url = !empty(System::$data['url']['query'][1]) ? System::$data['url']['query'][1] : '';
			$token_url = preg_replace('/[^A-Za-z0-9\-]/', '', $token_url);
			// nếu người dùng nhập link khác vào
			if($token_url != "" && $token_url != $tokenCache && $tokenCache != ""){
				$card_url = Card::getByToken($token_url);
				if (empty($card_url)) {
					Url::redirect('home/?type=card_lock&token='.$token_url.'&p=22');
				}
				CookieLib::set('c_qty', '', -1);
				CookieLib::set('auth', '', -1);
				CookieLib::set('accessTime', '', -1);
				CookieLib::set('requestNum', '', -1);
				CookieLib::set('cardNum', $token_url,time()+3600*24*15,true,false);
				SessionLib::del('card_token');
				SessionLib::del('upin');
                SessionLib::del('active_phone');
                SessionLib::del('request_otp');
				SessionLib::del('auth');
				//DB::update(T_CARD,['isLogined'=>1],"id={$card_url['id']}");
				//Url::redirect('card/'.$token_url);
			}
			if(!empty($token_url)) {
				$token = $token_url;
			}
			#echo System::encrypt($token,CRYPT_KEY);exit;
			$version = "ver3/";

			$page = Url::getParam('page','home');
			if(in_array($page,['topup'])){
				Url::redirect('card/'.$token);
			}
			if(!empty($token)) {
				$card = Card::getByToken( $token );
				if (!empty( $card ) && isset($card['id']) && in_array( $card['status'], [CARD_MIGRATED_URCARD, CARD_ACTIVE, CARD_DEACTIVATE,CARD_WALLET] )) {
                    if($card['status'] == CARD_MIGRATED_URCARD && $card['urcard_url'] != ''){
                        header( "Location: " . $card['urcard_url'] );
                        return;
                    }
                    if($card['version'] != ""){
						$version = $card['version'] . "/";
					}else {
						$app = Balance::getApp( $card );
						if (isset( $app['version'] ) && $app['version'] != "") {
							$version = $app['version'] . "/";
                            $card['version'] = $app['version'];
						}
					}
                    SessionLib::set('card_token',$token);
				} else {
					Url::redirect('home/?type=card_lock&token='.$token.'&p=65');
				}
			}else{
				$page = "Register";
			}

			$options = [
				'version' => $version,
				"token" => $token,
				"useWL" => 2
			];

			System::$data['version'] = $version;
			if(!empty($token)) {
				if ($card['expired_time'] > 0 && $card['expired_time'] < TIME_NOW - 3600 * 24 * 365) {
					$page = "expired";
				} elseif ($card['valid_time'] > 0 && $card['valid_time'] > TIME_NOW) {
					$page = "valid";
				} else {
					if ($card['status'] == CARD_ACTIVE) {
						if (!Card::isWhitelist( ['app_id' => $card['app_id'], 'phone' => System::decrypt( $card['phone'] ), 'phone_sub' => $card['phone_sub']] )) {
							$options["useWL"] = 1;
						}
					}
					$namespace = ucfirst( $version );
					$namespace = str_replace( '/', '', $namespace );
					System::$data['namespace'] = "Balance$namespace";
					/*CHeck page */

					$upin = SessionLib::get( 'upin' );
                    $hasUpin = 0;
					if (is_array( $upin ) && count( $upin ) == 2 && $upin['id'] == $card['id']) {
						$hasUpin = 1;
					}
					//xóa sesion phone để kích hoạt lại otp bằng số dt khác
                    $back_action = Url::getParam('back_action');
					if($back_action == 1) {
                        SessionLib::del('active_phone');
                    }elseif($back_action == 2){

                    }
					if ($card['status'] == CARD_DEACTIVATE) {
						if ($card['pin'] != "" && $hasUpin == 0) {
							$page = 'ULogIn';
						} elseif ($card['pin'] != "" && $card['phone'] != "" || $card['needActive'] == 2) {
                            $session_phone = SessionLib::get('active_phone');
                            $session_pin = SessionLib::get('request_otp');
                            if ((!FunctionLib::is_phone($session_phone) && $session_pin != 1) || $session_phone == "") {
                                $page = 'OnSetupPhone';
                            } else{
                                if($card['version'] =='ver3'){
                                    $page="OnSetupPhone";
                                }else{
                                    $page="Otp";
                                }
                           }
						}
					} else {
						#Da day du thong tin cho ra dang nhap
                        $isLogin = SessionLib::get('auth');
						if ($card['needActive'] == 2) {
							if ($hasUpin == 0 && $card['pin'] != "" && $card['phone'] == "") {
								$page = "ULogIn";
							}
							if ($hasUpin == 1 && $card['pin'] != "" && $card['phone'] == "") {
                                $session_phone = SessionLib::get('active_phone');
                                $session_pin = SessionLib::get('request_otp');
                                if ((!FunctionLib::is_phone($session_phone) && $session_pin != 1) || $session_phone == "") {
                                    $page = 'OnSetupPhone';
                                } else{
                                   if($card['version'] =='ver3'){
                                       $page="OnSetupPhone";
                                   }else{
                                       $page="Otp";
                                   }
                                }
							}
							if ($card['pin'] == "" && $card['phone'] != "") {
								$page = "OnSetupPin";
							}
							if ($card['pin'] != "" && $card['phone'] != "" && $isLogin == "") {
							    $page = "LogIn";
							}
						} else {
							if ($card['pin'] != "" && $hasUpin == 0 && $card['needActive'] == 1) {
								$page = "ULogIn";
							}
						}
						//trường hợp 2a. Không pin, không phone => cập nhật thời gian lần đầu tiên sử dụng thẻ
						if ($card['pin'] == "" && $card['phone'] == "" && $card['active_time'] == 0) {
							DB::update( T_CARD, ['active_time' => TIME_NOW], "id={$card['id']}" );
						}
					}
					if($card['needActive'] == IS_NO && $card['pin'] == ""){
                        SessionLib::set('auth',$card);
                    }
				}

				if(empty($card['version'])) {
					$upgradeVersion = $app['version'];
				}else{
					$upgradeVersion = $card['version'];
				}
                if(in_array($card['type'],[5,8]) && $card['version'] == 'ver12'){
                    if($card['status'] != 2){
                        Url::redirect('home/?type=card_lock&token='.$card['token'].'&p=170');
                        return;
                    }
                    $jwtSign = JWTService::buildJWT(
                        [
                            'token' => $card['token']
                        ],
                        3600 * 24 * 365,
                        JWT_KEY_FOR_PAPER_CARD
                    );
                    header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
                    header("Cache-Control: no-cache");
                    header("Pragma: no-cache");
                    header_remove();
                    header( "Location: " . CARD_URI . 'paper?token='.$jwtSign);
                    return;
                }
                if($card['type'] == 8){
                    Url::redirect('home/?type=card_lock&token='.$card['token'].'&p=188');
                    return;
                }
				$needRedirectToCard = Card::needUpgradeCard($card['app_id'],$upgradeVersion);
				if($needRedirectToCard){
					if($card['needActive'] == 2){
						if($card['phone'] == ''){
							if($card['pin'] != "") {
								header( "Location: " . CARD_URI . 'signin/' . $card['token'] );
							}else{
								header( "Location: " . CARD_URI . 'active/' . $card['token'] );
							}
						}else{
							header( "Location: " . CARD_URI . 'login/'. $card['token'] );
						}
					}else{
						header( "Location: " . CARD_URI . 'signin/'. $card['token'] );
					}
					return;
				}
			}
            require_once  dirname(__FILE__) . "/" .$version."class.php";
            if (isset($card)){
                if(!SessionLib::get('auth') && $card['version'] != 'ver3' && $card['version'] != ""){
                    Form::addCss("website/style/card/auth.css");
                    Form::addJs('website/javascript/card/auth.js');
                }
            }
			$options["page"] = $page;
			new App($block,$options);
		}
	}
