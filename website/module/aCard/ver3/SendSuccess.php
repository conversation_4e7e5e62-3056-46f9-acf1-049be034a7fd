<?php
class SendSuccessForm extends Form
{

    function draw() {
        global $display;
		$id = Url::getParamInt('id',0);
		if($id <=0){
			Url::redirect("/card/?page=account");
		}
	
		$gift = BalanceVer3::getGift($id);
		$display->add('gift',$gift);
		$phone = Url::getParam('phone',0);
		$id = Url::getParamInt('id',0);
		if($id <=0){
			Url::redirect("/card/?page=account");
		}
	
		$gift = BalanceVer3::getGift($id);
		$display->add('gift',$gift);
        $display->add('phone',$phone);
		$display->add('app',System::$data['apps']);
        return $display->output(System::$data['version']."SendSuccess");
    }
}
//