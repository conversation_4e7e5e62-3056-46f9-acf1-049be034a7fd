<?php
class TopupForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];
		$type = Url::getParam('type','money');
        $app = System::$data['apps'];
        $card = System::$data['card'];
		if($card['app_id'] != 29){
			Url::redirect('card');
		}
        $arrType = [
        	"money" => GIFT_TYPE_TOPUP_IO,
        	"card" => GIFT_TYPE_PHONE,
        	//"data" => GIFT_TYPE_CARD
		];
        
        
        
        if(isset($arrType[$type])) $giftType = $arrType[$type];
        else $giftType = GIFT_TYPE_TOPUP_IO;
	
		if($giftType == GIFT_TYPE_PHONE){
			$titleData = [
				'labelPhone' => "Chọn nhà mạng",
				'labelPrice' => "Chọn mệnh giá thẻ (VND)"
			];
		}else{
			$titleData = [
				'labelPhone' => "Nạp tới số",
				'labelPrice' => "Chọn mệnh giá nạp (VND)"
			];
		}
        
        $gifts = BalanceVer3::giftTopup($giftType);
        $display->add('gifts',$gifts);
        $display->add('app',$app);
        $display->add('titleData',$titleData);
        $display->add('giftType',$giftType);
        $display->add('card',$card);
		if($giftType == GIFT_TYPE_PHONE){
			$output = System::$data['version']."TopupPhone";
		}else{
			$output = System::$data['version']."TopupMoney";
		}
        return $display->output($output);
    }
}