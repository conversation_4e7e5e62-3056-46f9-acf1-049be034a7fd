<?php
class CatalogForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        $per_page = Url::getParamInt('per_page', 10);
        $parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);
		$cat_id = Url::getParamInt('cat_id',0);
		$type = Url::getParamInt('type',1);
		$order = Url::getParam('order','default');
        $pages = [
                "per_page" => $per_page,
                "page_num" => 1
        ];
        if(!in_array($type,[1,2])) $type = 1;
        $card = System::$data['card'];
        $city = BalanceVer3::getCityName();
        if($type == 1){
        	$category = BalanceVer3::getCategory($parent_id);
        }else{
			$category = BalanceVer3::physicalCategory();
		}
        if($cat_id > 0)
        $cate  = BalanceVer3::viewCate($cat_id);
        else $cate['title'] = "Tất cả";
        $data = BalanceVer3::getListGifts($card,$cat_id,$pages,['isPhysical'=>$type,'orderBy'=>$order,'length'=>TEXT_LENGTH]);
        $filter = [
            'default'=>"Mặc định",
            'popular'=>"Phổ biến nhất",
            'new'=>"Mới cập nhật",
           /// 'priceDesc'=>"Giá cao đến thấp",
           // 'priceAsc'=>"Giá thấp đến cao"
        ];
        $province = BalanceVer3::allProvide(true);
        $selected_filter = $filter[$order];
		$display->add('app',System::$data['apps']);
        $display->add('cate',$cate);
        $display->add('data',$data);
        $display->add('selected_filter',$selected_filter);
        $display->add('city',$city);
        $display->add('category',$category);
        $display->add('order',$order);
        $display->add('filter',$filter);
        $display->add('card',$card);
        $display->add('cat_id',$cat_id);
        $display->add('per_page',$per_page);
        $display->add('type',$type);
        $display->add('province',$province);
        if($type == 1)
            return $display->output(System::$data['version']."Catalog");
        return $display->output(System::$data['version']."CatalogPhysic");
    }
}