<?php
class AccountForm extends Form
{

    function draw() {
        global $display;

        $card = System::$data['card'];
		$tab = Url::getParamInt('tab',1);
        $vouchers = BalanceVer3::myVoucher($card['id']);
		$gift_used = BalanceVer3::usedVoucher($card['id']);
		$active_voucher = $vouchers['active_voucher'];
		$expired_voucher = $vouchers['expired_voucher'];
		$gift_sent = $vouchers['sent_voucher'];
        $history = BalanceVer3::history($card);
		$display->add('app',System::$data['apps']);
        $display->add('history',$history);
        $display->add('gift_used',$gift_used);
        $display->add('today',TIME_NOW);
        $display->add('card',$card);
        $display->add('active_voucher',$active_voucher);
        $display->add('tab',$tab);
        $display->add('expired_voucher',$expired_voucher);
        $display->add('gift_sent',$gift_sent);
        return $display->output(System::$data['version']."Account");

    }
}