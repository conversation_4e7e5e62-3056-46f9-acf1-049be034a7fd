<?php

class DetailForm extends Form
{
    function draw() {
        global $display;
        $id = Url::getParamInt('id',0);
        $brand_id = Url::getParamInt('brand_id',0);
        $price = Url::getParamInt('price',0);
        $type = Url::getParamInt('type',0);

        $card = System::$data['card'];
        $params = [
            'id' => $id,
            'brand_id' => $brand_id,
            'price' => $price,
            'type' => $type
        ];
        $data = BalanceVer3::detailVoucher($card,$params);
        if(count($data['gifts']) <=0)Url::redirect("/card/{$card['token']}?page=account");

        $item = current($data['gifts']);
        $office = BalanceVer3::getOffice($item['brand_id'],$item['gift_id']);
		$display->add('app',System::$data['apps']);
        $display->add('data',$data);
        $display->add('office',$office);
        $display->add('card',$card);
        $display->add('brand_id',$item['brand_id']);
        $single=1;
        if(count($data['gifts']) > 1) $single=2;
        $display->add('single',$single);
        return $display->output(System::$data['version']."Detail");
    }
}