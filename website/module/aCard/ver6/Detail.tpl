<div class="page" data-name="detail">
    <header class="navbar has-shadow">
        <div class="navbar-inner navbar-center header display-flex sliding">
            <a href="" class="navbar-brand">
                <img src="{WEB_ROOT}website/style/card/ver6/images/logo.svg" alt="">
            </a>
            <div class="nav-right">
                <div class="region display-flex justify-content-center" id="region">
                    <a href="" class="active">VN</a>
                    <a href="">EN</a>
                </div>
            </div>
        </div>
    </header>
    <div class="page-content">
        <section class="vd_sec">
            <div class="auto_x row">
                <div class="inner_voucher_box">
                    <section class="voucher_box">
                        <div style="position:relative">
                            {if $data.gifts}
                            <div class="swiper-container mT20 pL20 pR20 pT5 pB5 swiper-voucher">
                                <!-- Slides wrapper -->
                                <div class="swiper-wrapper">
                                    <!-- Slides -->
                                    {assign var="three_day_after" value=(3*86400) }
                                    {foreach from=$data.gifts item=$item}
                                    <div class="swiper-slide gift-swiper">
                                        <div class="gift-detail">
                                            <div class="detail-img"
                                                 style="background-image:url('{$item.image}')"></div>
                                            <div class="detail-content pL10 pR10 mB15">
                                                <div class="gift-name f14 f-400"><a target="_blank"
                                                                                    class="external"
                                                                                    href="{$item.link}">{$item.title}</a>
                                                </div>
                                                <div class="gift-price color-text f13 f-500 mT10">
                                                    {if !in_array($item.type,[10,11])}
                                                        {$item.price|number_format:0:",":"."} {VIEW_CURRENCY}
                                                    {/if}</div>
                                            </div>
                                            <div class="hr1 hr1fix mb-10"><span class=""></span></div>
                                            <div class="gift-code text-align-center">
                                                <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/logo-urbox-2.svg"
                                                     class="mB10 mT10 icons" alt="">
                                                {if $item.code !=""}
                                                    <input type="hidden" value="{$item.showbarcode}"
                                                           id="codeType">
                                                    {if $item.showbarcode==0}
                                                        <div class="qr_codeBoxBar_copy qr_copy cur_pointer w210"
                                                             id="div-{$item.cart_detail_id}"
                                                             data-id="qrCopy{$item.cart_detail_id}">
                                                            <div class="qr_codeBoxBar_copy_text f14 f-400">{'ẤN ĐỂ SAO CHÉP'|t}</div>
                                                            <div class="code-format mT10 f-600 f18">{$item.code}</div>
                                                            <input value="{$item.code}" readonly type="text"
                                                                   class="qr_codeBoxBar_text hidden-area"
                                                                   tabindex='-1'
                                                                   style="position:absolute; left: -9999px; text-indent: -9999px"
                                                                   aria-hidden='true'
                                                                   id="qrCopy{$item.cart_detail_id}"/>
                                                        </div>
                                                    {elseif $item.showbarcode == 1 || $item.showbarcode == 5}
                                                        <div class="code-img code-list code-qr"
                                                             data-value="{$item.code}"
                                                             data-type="{$item.showbarcode}"
                                                             id="qrcode{$item.cart_detail_id}"></div>
                                                        {if $item.showbarcode == 1}
                                                            <div class="f22 f-500 text-align-center mT10 mB10">{$item.code}</div>
                                                        {/if}
                                                    {elseif $item.showbarcode == 2}
                                                        <div class="voucher-code-item-code-qr"
                                                             id="div-{$item.cart_detail_id}">
                                                            <svg
                                                                    class="barcode code-list w100p"
                                                                    jsbarcode-value="{$item.code}"
                                                                    jsbarcode-textmargin="0"
                                                                    jsbarcode-fontoptions="bold"
                                                                    jsbarcode-displayValue="false"
                                                                    data-value=""
                                                                    id="qrcode{$item.cart_detail_id}"></svg>
                                                            <div class="f22 f-500 text-align-center mT10 mB10">{$item.code}</div>
                                                        </div>
                                                    {elseif $item.showbarcode == 6}
                                                        <div class="voucher-code-item-code-qr"
                                                             id="div-{$item.cart_detail_id}">
                                                            <img src="https://apib.urbox.vn/v1/api/qrbarcode?code={$item.code}" />
                                                            <div class="f22 f-500 text-align-center mT10 mB10">{$item.code}</div>
                                                        </div>
                                                    {/if}



                                                {else}
                                                    <div class="code-name text-align-center mT10 pT20">
                                                        <a href="{$item.link}" class="external color-blue"
                                                           target="_blank">{'Lấy code'|t}</a>
                                                    </div>
                                                    <img class="code-img"
                                                         src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/blank.png"
                                                         alt="">
                                                {/if}
                                                {if $item.code_type == 2}
                                                    <div id="note-logdiv-{$item.cart_detail_id}"
                                                         class="pT10 f10 color-text w200 mx {if $item.delivery != 9}hidden{/if}">
                                                        Lưu ý: Mã voucher có hiệu lực <span
                                                                class="color-red">trong ngày</span>
                                                        sau khi hiển thị
                                                    </div>
                                                {/if}
                                                <div class="color-gray f14 mT10">
                                                    {if $item.serial}
                                                        <div class="display-flex justify-content-center">
                                                            <img src="/website/style/card/images/serial.svg"
                                                                 class="w-16 mR5" alt="">
                                                            <span>Số serial: <strong
                                                                        class="color-serial f-600">{$item.serial}</strong></span>
                                                        </div>
                                                    {/if}
                                                    {if $item.pin}
                                                        <div class="display-flex justify-content-center">
                                                            <img src="/website/style/card/images/serial.svg"
                                                                 class="w-16 mR5" alt="">
                                                            <span>PIN: <strong
                                                                        class="color-serial f-600">{$item.pin}</strong></span>
                                                        </div>
                                                    {/if}
                                                </div>
                                                <div class="color-gray mT10 f14">
                                                    <img src="/website/style/card/ver6/images/icon_notice.svg"
                                                         style="vertical-align: text-top;"
                                                         alt="">
                                                    Không cung cấp ảnh chụp màn hình cho<br>nhân viên khi thanh
                                                    toán
                                                </div>
                                                {if $item.identity && $item.needIdentity == 2}
                                                    <div class="mT10 f14">{"CMT/Hộ chiếu"}: <span
                                                                class="f-400">{$item.identity }</span></div>
                                                {/if}
                                                <div class="mT20 color-gray f14 f-400">{'Hạn sử dụng'|t}: <span
                                                            class="color-red f-600">
                                            {if $item.expired != 0}
                                                {$item.expired|date_format:"d/m/Y"}
                                            {else}
                                                {if isset($item.expired)}
                                                    {'Vô thời hạn'|t}
                                                {else}
                                                    &nbsp;
                                                {/if}

                                            {/if}
                                        </span></div>
                                                {if $item.valid_time != 0}
                                                    <div class="mT5 f14">
                                                        <span class="color-gray">{'Hiệu lực sử dụng từ ngày'|t}:&nbsp;</span>
                                                        <span class="color-blue f-600">
                                                {$item.valid_time|date_format:"d/m/Y"}
                                            </span>
                                                    </div>
                                                {/if}
                                                <div class="f14 color-gray f-400 pT3">{"Ngày đặt"|t}: <span
                                                            class="f-600 color-blue">{$item.created|date_format:'d/m/Y'}</span>
                                                </div>
                                                <div class="f14 color-gray f-400 pT3">{"Hotline"|t}: <a
                                                            class="color-blue f-600"
                                                            style="text-decoration: none"
                                                            href="tel:{UB_CUSTOMER_PHONE_VALUE}">{UB_CUSTOMER_PHONE}</a>
                                                </div>
                                                <div class="border-bottom mT30"></div>
                                                <div class="display-flex">
                                                    <div class="p10 mx w100p box-sizing">
                                                        <div class="p10 mx w100p box-sizing">
                                                            <div class="cur_pointer qr_copy p10 mx w100p"
                                                                 data-id="qrCopyLink{$item.cart_detail_id}">
                                                                <div class="qr_codeBoxBar_copy_text justify-content-center display-flex align-items-center f12 f-500 over-hidden">
                                                                    <img style="padding-top: 3px;"
                                                                         src="{$smarty.const.WEB_ROOT}website/style/card/ver3/images/img_copy.svg"
                                                                         width="24" class="fl mR5 w20" alt="">
                                                                    <span> {'Tặng quà '|t}</span>
                                                                </div>
                                                                <input value="{$item.link}" readonly type="text"
                                                                       style="opacity:0; height:0"
                                                                       class="qr_codeBoxBar_text hidden-area"
                                                                       tabindex='-1'
                                                                       aria-hidden='true'
                                                                       id="qrCopyLink{$item.cart_detail_id}"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/foreach}
                                    </div>
                                    <!-- Pagination, if required -->
                                </div>
                                {/if}
                            </div>
                    </section>
                </div>
                <section class="store_box">
                    <div class="container m-auto">
                        <nav aria-label="breadcrumb" class="breadcrumb_ib">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="" class="color-gray">Home </a>
                                </li>
                                <li class="breadcrumb-item active"> > Voucher</li>
                            </ol>
                        </nav>
                        <div class="tab-link">Điều kiện áp dụng</div>
                        <div class="tab">
                            <div class="tab_1_c">
                                {$data.note}
                            </div>
                        </div>
                        <hr style="background: #8195A329"/>
                        {if $data.brand.online != 2}
                            <div class="tab-link">Địa chỉ cửa hàng</div>
                            <div class="tab_2_c">
                                <div>
                                    {foreach from=$office item=item name=i}
                                        <div class="item_storex">
                                            <div class="stor">
                                                <a href="#">
                                                    <img src="{$smarty.const.WEB_ROOT}website/style/card/ver5/images/ico_store.svg"/>
                                                </a>
                                            </div>
                                            <div class="info">
                                                <a href="#">
                                                    {$item.address}
                                                </a>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        {/if}
                        <div class="text-right color-gray footer_fe">
                            Quý khách cần hỗ trợ? Liên hệ hotline: <strong> <a style="color:#474747"
                                                                               href="tel:{UB_CUSTOMER_PHONE_VALUE}">{UB_CUSTOMER_PHONE}</a></strong>
                        </div>
                    </div>
                </section>

            </div>
        </section>
        {if $item.expired != 0 && $item.expired - time() <=0 || $item.active == 2}
            <div class="dialog" id="expired-dialog">
                <div class="dialog-inner pT50 pB55">
                    <div class="text-align-center pT20 pB20">
                        <img src="{WEB_ROOT}website/style/card/ver5/images/img-expired-voucher.svg" class="" alt="">
                    </div>
                    <div class="text-align-center p20 f-600">Quà đã được sử dụng hoặc đã hết hạn!</div>
                </div>
            </div>
        {/if}
    </div>
</div>
