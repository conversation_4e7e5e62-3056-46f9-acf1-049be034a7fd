<div class="page" data-name="account">
	<div class="navbar header">

			<div class="navbar-inner ">
				<div class="wl-container">
				<div class="container justify-content-space-between display-flex w100p">
					<div class="w20p display-flex">
						<a href="javascript:;" id="backlink" class="header-back-page f18 f-400"><img src="{$smarty.const.WEB_ROOT}website/style/loyal/tomo/images/arrow-left-big.svg" alt="" width="15px"> <span class="back-page pL5">{"Quay lại"|t}</span></a>
					</div>
					<div class="header-center">
						<span class="f18 f-500">
							{"Quà của tôi"|t}
						</span>
					</div>
					<div class="w20p wl-hide">
					</div>
				</div>
				</div>
			</div>
	</div>

	<div class="page-content bg-color-white">
		{if isset($voucher)}

		<div class=" hide-navbar-on-scroll mv-tab-content margin-right margin-left">
			<div class="wl-container flex-shrink-0" >
				<div class="box-gift flex-shrink-0 mT30 justify-content-flex-start" style="flex-wrap: wrap;">

					{foreach from=$voucher item=$v}
						<div class="item-thumbnails-item my-voucher">
							{if TIME_NOW - 86400 < $v.created}
								<div class="tag-new tag-voucher">{"Quà mới mua"|t}</div>
							{/if}
							<div class="item-inner" onclick="_.click.link('/loyal/{$card.token}?page=detail&id={$v.id}')">
								<div class="item-thumbnails-images voucher-img" style="background-image: url('{$v.image}')">
									{if $v.qty > 1}
									<span class="voucher-count f12 f-500">{$v.qty}</span>
									{/if}
								</div>
								<div class="item-thumbnail-item-context mT5">
									<div class="f-400 f12 h35 lh14 over-hidden"> {$v.title}</div>
									{if $v.expired > 0}
										{assign var='expried_date' value=($v.expired-$smarty.now)/86400}
										{if $expried_date > 1}
											{if $expried_date <5}
												<div class="f12 f-600 color-red">{"HSD còn %s ngày"|t|sprintf:round($expried_date,0)}</div>
											{/if}
										{else}
										<div class="f12 f-600 color-red">{"HSD còn %s ngày"|t|sprintf:round($expried_date,1)}</div>
										{/if}
									{else}
{*											<div class="f12 f-600">{"Vô thời hạn"}</div>*}
									{/if}
								</div>
							</div>
						</div>
					{/foreach}

					{if isset($gift_expired.brand)}
					{foreach from=$gift_expired.brand item=$item}
						<div class="item-thumbnails-item my-voucher expired_voucher">
							<div class="item-inner">
								<div class="item-thumbnails-images voucher-img" style="background-image: url('{$item.avatar}')">
								</div>
								<div class="item-thumbnail-item-context mT5">
									<div class="f-400 f12 h35 lh14 over-hidden"> {$item.gift_name}</div>
									<div class="f12 f-600">{"Hết hạn sử dụng"|t}</div>
								</div>
							</div>
						</div>

					{/foreach}
					{/if}
				</div>
				{if $usedVoucher}
				<div class="title mT30 f-600">{"Đã sử dụng"|t}</div>
				<div class="box-gift flex-shrink-0 mT10 justify-content-flex-start" style="flex-wrap: wrap;">
						{foreach from=$usedVoucher item=$item}
							<div class="item-thumbnails-item my-voucher expired_voucher">
								<div class="item-inner">
									<div class="item-thumbnails-images voucher-img" style="background-image: url('{$item.image}')">
									</div>
									<div class="item-thumbnail-item-context mT5">
										<div class="f-400 f12 h35 lh14 color-gray over-hidden"> {$item.title}</div>
										<div class="f12 f-600  color-gray">{"Đã sử dụng"|t}</div>
									</div>
								</div>
							</div>

						{/foreach}

				</div>
				{/if}
			</div>
			<div class="wl-line mT30 wl-container"></div>
			<div class="pT20 pB20 wl-container mx">
				{if empty($history)}
					<div class="text-align-center">
						<img src="{$smarty.const.WEB_ROOT}mobile/style/loyal/images/img-no-transaction.svg" alt="" class="mT40">
						<div class="mT30">{"Chưa có lịch sử giao dịch"|t}</div>
					</div>
				{else}
					<div class="f30 f-500 mB20">{"Lịch sử đổi quà"|t}</div>
					<table width="100%" border="0" cellpadding="0" cellspacing="0" class="list-history">
						{foreach from=$history item=$his}
							<tr>
								<td class="f10r pT15 pB15 f-400 w100">{$his.created|date_format:'d/m/Y'}
								</td>
								<td class="f10r f-400">{$his.title} x {$his.qty}</td>
								<td class="f10r f-400 nowrap">
									{if $his['delivery_required'] == 2}
										{if empty($his.process)}
											<span class="color-green">{"Đang chờ xử lý"|t}</span>
										{else}
											{if $his.process == 1}
												<span class="color-green">{"Đang chờ xử lý"|t}</span>
											{elseif $his.process==2}
												{"Đã chuyển hàng"|t}
											{elseif $his.process==3}
												{"Thành công"|t}
											{elseif $his.process==4}
												<span class="color-organ">{"Vận chuyển trả hàng"|t}</span>
											{else}
												<span class="color-red">{"Chờ có hàng"|t}</span>
											{/if}
										{/if}
									{else}
										{if $his.delivery == 3}
											<span>{"Đã sử dụng"|t}</span>
										{else}
											<span class="color-green">{"Chưa sử dụng"|t}</span>
										{/if}
									{/if}

								</td>
								<td>{$his.id}</td>
								<td class="w120 text-align-right">
									{include file="./point.tpl" point=$his.point campaign=$campaign imgClass="w16" cssClass='f16 f-500 color-blue' preText='-'}
								</td>
							</tr>
						{/foreach}
					</table>
				{/if}
			</div>
			<div class="footer"></div>
		</div>
		{/if}
	</div>
</div>
