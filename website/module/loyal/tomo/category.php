<?php
class CategoryForm extends Form
{

    function draw() {
        global $display;

        $per_page = Url::getParamInt('per_page', 12);
        $parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);
		$cat_id = Url::getParamInt('id',0);
		$type = Url::getParamInt('type',0);
		$order = Url::getParam('order',"default");
		$range = Url::getParam('range',"");
        $array_range = explode('-', $range);
        if (count($array_range) != 2) {
            $array_range[0] = 0;
            $array_range[1] = 60000;
        }
        $pages = [
                "per_page" => $per_page,
                "page_num" => 1
        ];
//        if($tag_id != 0){
//            $id = $tag_id;
//        }else{
//            $id = $cat_id;
//        }
		$card = System::$data['card'];
		$app = System::$data['apps'];
		$campaign = System::$data['campaign'];
        $category = Loyalty::getCategory($parent_id);
        $filter = [
            'default'=>"Mặc định",
            'popular'=>"Phổ biến nhất",
            'new'=>"Mới cập nhật",
            'priceDesc'=>"Giá cao đến thấp",
            'priceAsc'=>"Giá thấp đến cao"
        ];
        $selected_filter = $filter[$order];
        $display->add('selected_filter',$selected_filter);
       // $tag = Loyalty::getCategory($cat_id);
        if(!in_array($type,[1,2])) $type = 1;
        $display->add('filter',$filter);
        $data = Loyalty::getListGifts($card,$cat_id,$pages,['isPhysical'=>$type,'orderBy'=>$order,'priceRange'=>$range]);
        $message = StringLib::post_db_parse_html($campaign['description']);
        $city = Loyalty::allProvide(false);
        $current_city = CookieLib::get("L_MY_CITY");
        $display->add('id',$cat_id);
        $display->add('type',$type);
        $display->add('current_city',$current_city);
        $display->add('city',$city);
        $display->add('category',$category);
        $display->add('filter',$filter);
        $selected_filter = $filter[$order];
        $display->add('selected_filter',$selected_filter);
        $display->add('order',$order);
        $display->add('message',$message);
        $display->add('data',$data);
        $display->add('card',$card);
        $display->add('app',$app);
        $display->add('range',$array_range);
        $display->add('cat_id',$cat_id);
        $display->add('per_page',$per_page);
        $display->add('campaign',$campaign);
        return $display->output(System::$data['version']."category");
    }
}