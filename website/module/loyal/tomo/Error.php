<?php
class ErrorForm extends Form
{

    function draw() {
        global $display;
        $type = Url::getParam('type',"");
        switch($type) {
			case "user_not_found":
				$msg = "Không lấy được thông tin người dùng. Vui lòng quay lại sau 1 phút nữa.";
				$home = 1;
				break;
			case "session_expired":
				$msg = "Phiên làm việc đã hết hạn. Vui lòng đăng nhập lại từ IBS để tiếp tục";
				$home = 1;
				break;
			default:
				$msg = "Trang bạn truy cập không tồn tại hoặc không chính xác.";
				$home = 1;
				break;
		}
		$display->add("msg",$msg);
		$display->add("home",$home);
		
		$display->add("card",System::$data['card']);
        return $display->output(System::$data['version']."Error");
    }
}