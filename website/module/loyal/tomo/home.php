<?php
	class HomeForm extends Form
	{
		
		function draw() {
			global $display;
			$per_page = Url::getParamInt('per_page', 15);
			$pages = [
				"per_page" => $per_page,
				"page_num" => 1
			];
			$card = System::$data['card'];
			$app = System::$data['apps'];
			$campaign = System::$data['campaign'];
			$parent_id = Url::getParam('parent_id',CGlobal::PARENT_CAT_ID);
			
			$category = Loyalty::getCategory($parent_id);
			$data =[];
			foreach ($category as $k=> $item) {
				$data[$k]['title'] = $item['title'];
				$data[$k]['id'] = $item['id'];
				$data[$k]['data'] = Loyalty::getListGifts($card, $item['id'], $pages,['isPhysical'=>1]);
			}
			
			//Loyalty::setGiftToCache(1699);
			$viewed_gift = Loyalty::getGiftFromCache($card,1,0,$campaign['id']);
			$promoVoucher = Loyalty::hotVoucher($card,6,1,0);
			$message = StringLib::post_db_parse_html($campaign['description']);
			$display->add('card',$card);
			$display->add('message',$message);
			$display->add('data',$data);
			$display->add('promoVoucher',$promoVoucher);
			$display->add('viewed_gift',$viewed_gift);
			$display->add('category',$category);
			$display->add('campaign',$campaign);
			$display->add('app',$app);
			return $display->output(System::$data['version']."home");
		}
	}
