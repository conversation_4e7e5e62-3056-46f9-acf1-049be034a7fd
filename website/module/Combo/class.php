<?php
	
	class Combo extends Module
	{
		function __construct($block)
		{
			Module::initialize( $block );
			$page = Url::getParam( 'page', 'home' );
			Form::addCss( 'website/libs/_f7.4.5.10/css/framework7.bundle.min.css' );
			Form::addCss( 'website/style/combo/style.css' );
			Form::addJs( 'website/libs/_f7.4.5.10/js/framework7.bundle.js' );
			Form::addJs( 'website/javascript/combo/routes.js' );
			Form::addJs( 'website/javascript/combo/app.js' );
			
			#Trang Hiển thị Quà COMBO
			#page.urbox.vn/combo/{token}
			#Get TOKEN URL
			#Kiem tra TOKEN trong bảng cart
			#Vào trang danh sach quà combo lay trong cart_detail có gom theo thương hiệu bộ quà giống các trang phần quà của tôi
			#Xem chi tiết 1 quà voucherchỉ
			#Xem chi tiết 1 thương hiêu và địa
			
			
			#Get Token roi check
			$token = !empty( System::$data['url']['query'][1] ) ? System::$data['url']['query'][1] : '';
			$token = preg_replace( '/[^A-Za-z0-9\-]/', '', $token );
			
			if ($token == '') {
				Url::redirect( 'home' );
			}
			
			CookieLib::set('cb_token',$token,time()+3600*24,true,false);
			$combo = ComboGift::getCombo( $token );
			if (!$combo) {
				Url::redirect( 'home' );
			}
			System::$data['combo'] = $combo;
			System::$data['page']['title'] = $combo['gift']['title'];
			$session = isset($_SESSION['isLoginCombo']) ? $_SESSION['isLoginCombo']: null;
			if($session != $token && $combo['pin'] != ""){
				$page = 'login';
			}
			
			switch ($page) {
				case "detail":
					require_once 'detail.php';
					$form = 'DetailForm';
					break;
				case "waiting":
					require_once 'Waiting.php';
					$form = 'WaitingForm';
					break;
				case "result":
					require_once 'Result.php';
					$form = 'ResultForm';
					break;
				case "expired":
					require_once 'expired.php';
					$form = 'ExpiredForm';
					break;
				case 'login':
					require_once 'LogIn.php';
					$form = 'LogInForm';
					break;
				default:
					require_once 'home.php';
					$form = 'HomeForm';
					break;
			}
			Module::addForm( new $form() );
		}
	}
