<div class="page" data-name="home">
    <div class="page-content">
        <div class="home">
            <header>
                <div class="header-background" style="background: transparent url('{$gift.avatar.640}') no-repeat center center;max-width:700px;height:270px !important;">
                </div>
                <div class="header-logo-grab">
                    <img style="width: 100%;display: inline-block;" src="{$smarty.const.WEB_ROOT}/website/style/hsbc/images/logo-hsbc.jpg" alt="Hsbc Reward">
                </div>
            </header>
            <main class="" style="margin-top: 40px;">
                <div class="container">
                    <div class="gift-block">
                        <div class="gift-name mB10">
                            {$gift.title}
                        </div>
                        <div class="text-center">
                            <div class="mB10"><b>Lưu ý/Notice:</b></div>
                            <div class="mB10" style="">
                                T<PERSON><PERSON> k<PERSON> của bạn phải ở trạng thái hoạt động trước khi thực hiện nạp điểm.
                                <b><a style="color:#3e68f0" href="https://www.grab.com/vn/rewards/" class="external f14" target="_blank"><i>(Xem thông tin về hạn sử dụng điểm GrabRewards)</i></a></b>
                            </div>
                            <div class="mB10" style="">
                                Your Grab account must be active before making a transaction.
                                <b><a style="color:#3e68f0" href="https://www.grab.com/en/rewards/" class="external f14" target="_blank"><i>(See more information on point expiry)</i></a></b>
                            </div>
                        </div>
                        <div class="gift-code-input mT40">
                            <span class="mB15 pL5 f15"><b>Nhập mã quà tặng</b> - <i>Enter the giftcode</i></span>
                            <input  name="code" id="code" class="check-event input-text" type="text" placeholder="Nhập mã tại đây - Giftcode here"/>
                            <input name="appName" id="appName" type="hidden" value="{$app.name}"/>
                            <div id="errPut" class="error-message mT5 pL10 hidden">*Code đã được sử dụng - Code has been used</div>
                        </div>
                        <a onclick="_.mod.hsbc.topup({$id})" id="link-btn" class="btn-topup mT40">Xác nhận - Confirm</a>
                        <div class="hotline mT20 mB20"><a class="external" style="color:#3e68f0" href="tel:1900299232">Hotline: {HSBC_CUSTOMER_PHONE}</a></div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
