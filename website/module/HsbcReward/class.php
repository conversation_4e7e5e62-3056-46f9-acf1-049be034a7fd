<?php
const HSBC_CUSTOMER_PHONE = '1900 299 232';
class HsbcReward extends Module
{
    function __construct($block)
    {
        Module::initialize($block);

        Form::addCss('website/style/f7.3.6.5/css/framework7.min.css');
        Form::addCss('website/style/default.css');
        Form::addJs('website/style/f7.3.6.5/js/framework7.min.js');

        Form::addCss('website/style/hsbc/style.css');
        Form::addJs('website/javascript/hsbc.reward/routes.js');
        Form::addJs('website/javascript/hsbc.reward/bundle.js');
        Form::addJs('website/javascript/hsbc.reward/app.js');

        $action = StringLib::clean_key(StringLib::clean_value(!empty(System::$data['url']['query'][2]) ? System::$data['url']['query'][2] : ''));
        $id = (int)StringLib::clean_key(StringLib::clean_value(!empty(System::$data['url']['query'][3]) ? System::$data['url']['query'][3] : 0));
//        die(System::decrypt("2PCF0cajwiyfo-pA0MDxDQ"));
//        UB118554249
//        die($action);
        if ($action == "redirect") {
            require_once 'redirect.php';
            $form = 'RedirectForm';
        } elseif ($action == "success") {
            require_once 'success.php';
            $form = 'SuccessForm';
        } elseif ($id > 0) {
            switch ($action) {
                case "topup":
                    require_once 'topup.php';
                    $form = 'TopUpForm';
                    break;
                case "receiver":
                    # Kiểm tra Ma
                    $code = CookieLib::get(md5('code'), '');
                    if (empty($code)) {
                        Url::redirect('home');
                    }
                    $gift = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $id);
                    #Co dung thong tin qua da nhap code
                    $gift_code = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE status=1 AND brand_id=" . $gift['brand_id'] . " AND code='" . System::encrypt($code) . "'");
                    if (empty($gift_code) || $gift_code['cart_detail_id'] == 0 || $gift_code['gift_detail_id'] != $id) {
                        Url::redirect('home');
                    }
                    #Xem da nhap thong tin chua
                    $checkFinish = CookieLib::get(md5('ACTIVESUS' . $id . $code), 0);
                    if ($checkFinish) {
                        Url::redirect(System::$data['app']['name'] . '/gift/finish/' . $id);
                    }
                    require_once 'receiver.php';
                    $form = 'ReceiverForm';
                    break;
                case "finish":
                    require_once 'finish.php';
                    $form = 'FinishForm';
                    break;

                default:
                    require_once 'gift.php';
                    $form = 'GiftForm';
                    break;
            }
        } else {
            Url::redirect('home');
        }

        Module::addForm(new $form());
    }
}
