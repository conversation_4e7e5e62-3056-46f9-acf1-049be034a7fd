<div class="page" data-name="home">
	<div class="navbar header">
		<div class="bl-container">
			<div class="top-head header-content">
				<div class="header-left">
					<img src="{$logo}" alt="">
				</div>
				<div class="header-right">
					<img src="/website/style/delivery/images/urbox-logo.png" alt="">
				</div>
			</div>
		</div>
	</div>
	<div class="page-content">

		<div class="title-page width-layout mB15">
			<div class="bl-container">
				<div class="title-page-content">
					<strong>Quà tặng của bạn</strong>
				</div>
			</div>
		</div>

		<div class="product width-layout bg-layout mB20">
			<div class="bl-container">
				<div class="swiper-container thumbnails">
					<div class="swiper-wrapper">
						{foreach $gifts as $gift}
						<div class="swiper-slide product-slide-width">
							<div class="product-content bg-layout">
								<div class="product-image">
									<img src="{$gift.image}" alt="">
								</div>
								<div class="product-description">
									<div class="product-title">
										<span>{$gift.title}</span>
									</div>
{*									<div class="product-price">*}
{*										<span>{$gift.price|number_format:0:'.':","}đ</span>*}
{*									</div>*}
									<div class="product-quantity">
										<span>Số lượng: </span><span class="product-quantity-number">{$countValue[$gift.id]}</span>
									</div>
								</div>
							</div>
						</div>
						{/foreach}
					</div>

					<!-- Add Arrows -->
					<div class="product-arrow product-prev swiper-button-prev"></div>
					<div class="product-arrow product-next swiper-button-next"></div>
				</div>
			</div>
		</div>

		<div class="form-input width-layout bg-layout mB20">
			<div class="bl-container">
				<form action="" class="" id="theForm">
					<div class="form-input-content">
					<div class="header-form-input">
						<div class="main-header-form">
							Nhập thông tin
						</div>
						<p>
							{if count($gifts) ==1}
								Thông tin bạn nhập sẽ được sử dụng để giao hàng. Bạn vui lòng nhập thông tin chính xác
							{else}
								Thông tin bạn nhập sẽ được sử dụng để giao hàng. Bạn vui lòng nhập thông tin chính xác
							{/if}
						</p>
{*						<div>Thời gian giao quà bắt đầu từ 19/2/2024( Sau Tết Nguyên Đán)</div>*}
					</div>
					<div class="body-form-input">
						<div class="form-group">
							<fieldset>
								<legend>Số điện thoại</legend>
								<input type="text" id="phone" class="form-control formRequire" name="phone" data-require="phone" placeholder="Số điện thoại của bạn">
								<div id="E-phone" class="error-message mT5 hidden">*Số điện thoại không hợp lệ</div>
							</fieldset>
						</div>
						<div class="form-group">
							<fieldset>
								<legend>Họ và tên</legend>
								<input type="text" id="name" class="form-control formRequire" name="name" data-require="name" placeholder="Tên của bạn">
								<div id="E-name" class="error-message mT5 hidden">*Họ và tên không được để trống</div>
							</fieldset>
						</div>
						<div class="form-group">
							<fieldset>
								<legend>Email của bạn</legend>
								<input type="text" id="email" class="form-control " name="email"  placeholder="<EMAIL>">
								<div id="E-email" class="error-message mT5 hidden">*Email không hợp lệ</div>
							</fieldset>
						</div>
						<div class="form-group">
							<fieldset>
								<legend>Tỉnh/Thành phố</legend>
{*								<select onchange="_.mod.click.district(this)" class="form-control formRequire"  data-require="int" name="city" id="city">*}
{*									<option value="">{'Chọn'|t}  </option>*}
{*									{foreach from=$city item=opt name=i}*}
{*										<option value="{$opt.id}">{$opt.title|t}</option>*}
{*									{/foreach}*}
{*								</select>*}
								<input type="hidden" class="formRequire" id="city" name="city" data-require="int" required >
								<input type="text" onclick="_.app.popup.open('#popup-city')" readonly id="text-city" placeholder="Chọn" class="form-control has-dropdown">
								<div id="E-city" class="error-message mT5 hidden">*Bạn chưa chọn Tỉnh/Thành phố</div>
							</fieldset>
						</div>
						<div class="form-group">
{*							onchange="_.mod.click.ward(this.value)"*}
							<fieldset>
								<legend>Quận/Huyện</legend>
{*								<select onchange="_.mod.click.ward(this)" id="" class="form-control formRequire" required name="district" data-name="district" data-require="int">*}
{*									<option value="">{'Chọn'|t} </option>*}
{*								</select>*}
								<input type="hidden" class="formRequire" id="district" name="district" data-require="int"  data-name="district" required>
								<input type="text" onclick="_.app.popup.open('#popup-district')" id="text-district" readonly  placeholder="Chọn" class="form-control has-dropdown">

								<div id="E-district" class="error-message mT5 hidden">*Bạn chưa chọn Quận/Huyện</div>
							</fieldset>
						</div>
						<div class="form-group">
							<fieldset>
								<legend>Phường/Xã</legend>
{*								<select id="ward" class="form-control formRequire" required name="ward" data-name="ward" data-require="int">*}
{*									<option value="">{'Chọn'|t} </option>*}
{*								</select>*}
								<input type="hidden" class="formRequire" id="ward" name="ward" data-require="int" data-name="ward" required>
								<input type="text" onclick="_.app.popup.open('#popup-ward')" id="text-ward" readonly  placeholder="Chọn" class="form-control has-dropdown">

								<div id="E-ward" class="error-message mT5 hidden">*Phường/Xã không được để trống</div>
							</fieldset>
						</div>
						<div class="form-group">
							<fieldset>
								<legend>Địa chỉ chi tiết</legend>
								<input type="text" class="form-control formRequire" name="street" id="street" data-require="name" placeholder="Số nhà, Tên đường">
								<div id="E-street" class="error-message mT5 hidden">*Địa chỉ không được đê trống</div>
							</fieldset>
						</div>
						<div class="form-group">
							<fieldset>
								<legend>Ghi chú giao hàng <span>(không bắt buộc)</span></legend>
								<input type="text" class="form-control" name="notes" id="notes" placeholder="Nội dung ghi chú">
							</fieldset>
						</div>
						<div class="form-group hidden">
							<label class="checkbox"><input type="checkbox" name="bill-checkbox" id="bill-checkbox" value="1"><i class="icon-checkbox"></i></label> Xuất hóa đơn cho đơn hàng này
						</div>
						<div id="block-bill" style="background: #f0f0f0" class="p10 hidden">
							<div class="form-group">
								<fieldset>
									<legend>Tên công ty</legend>
									<input type="text" class="form-control" onkeyup="checkBillInput()" data-require="name" maxlength="170" name="company-name" id="company-name" placeholder="Tên công ty">
									<div id="E-company-name" class="error-message mT5 hidden">*Tên công ty không được đê trống</div>
								</fieldset>
							</div>
							<div class="form-group">
								<fieldset>
									<legend>Mã số thuế</legend>
									<input type="text" class="form-control" onkeyup="checkBillInput()" name="company-tax" maxlength="20" data-require="name" id="company-tax" placeholder="Mã số thuế">
									<div id="E-company-tax" class="error-message mT5 hidden">*Mã số thuế không đúng</div>
								</fieldset>
							</div>
							<div class="form-group">
								<fieldset>
									<legend>Địa chỉ</legend>
									<input type="text" class="form-control" onkeyup="checkBillInput()" name="company-address" maxlength="200" data-require="name" id="company-address" placeholder="Địa chỉ">
									<div id="E-company-address" class="error-message mT5 hidden">*Địa chỉ không được để trống</div>
								</fieldset>
							</div>
							<div class="form-group">
								<fieldset>
									<legend>Email nhận</legend>
									<input type="text" class="form-control" name="company-email" maxlength="100" data-require="email" id="company-email" placeholder="Email nhận hóa đơn">
									<div id="E-company-email" class="error-message mT5 hidden">*Email không đúng định dạng</div>
								</fieldset>
							</div>
							<div class="f11">
								<div style="color: #F00;font-weight:bold">*Lưu ý:</div>
								- Hóa đơn được xuất trong khoảng 10 ngày làm việc tính từ thời điểm khách hàng nhận được hàng<br>
								- UrBox sẽ phát hành hóa đơn điện tử và gửi về Email nhận tại thông tin xuất hóa đơn. Trường hợp không điền thông tin trên, hóa đơn sẽ được gửi về Email của người nhận hàng.
							</div>
						</div>
						
						<div style="border: 3px solid #FFD6B0; border-radius: 20px; background: #FFF6ED; padding: 24px 32px; color: #232323; text-align: justify;  font-weight: 400; line-height: 1.3; max-width: 800px; margin: auto; margin-top: 16px;">
							<span>Quý khách vui lòng sử dụng địa chỉ theo <b>đơn vị hành chính cũ</b> trong thời gian này để đảm bảo việc xử lí đơn hàng được chính xác.</span> 
						</div>

					</div>
					<div class="footer-form-input">
						<a class="form-input-submit btn w100p" id="btn-submit" onclick="_.mod.click.submit('{$cart.token}','{$cart.id}')">Hoàn tất</a>
					</div>
				</div>
				</form>
			</div>
		</div>

		<div class="hot-line">
			<div class="bl-container">
				<div class="hot-line-content text-center">
					<span>Hotline: </span><span class="hot-line-phone">{UB_CUSTOMER_PHONE}</span>
				</div>
			</div>
		</div>

		<div class="footer bg-white">
			<div class="bl-container">
				<div class="footer-content display-flex justify-content-center align-items-center">
					<img src="/website/style/delivery/images/footer.svg" alt="">
				</div>
			</div>
		</div>

		<div class="popup" id="popup-city">
			<div class="page">
				<div class="navbar">
					<div class="navbar-inner">
						<div class="text-align-center w100p pL15 pR15">
							<div class="text-align-center f17 f-700">Địa điểm</div>
							<input id="input-city" onkeyup="_.mod.click.searchDis(this.id)" class="input-search" type="text"  placeholder="Tìm tỉnh thành">
						</div>
					</div>
				</div>
				<div class="page-content">
					<div class="list listSort" >
						<ul id="ul-input-city">
							{foreach from=$city item=cat name=i}
								<li class="mB0" onclick="_.mod.click.getDisOfCity('#text-city','{$cat.title}',{$cat.id})">
									<label class="item-radio item-content">
										<input type="radio" name="radio-city" data-name="{$cat.title}" value="{$cat.id}" />
										<div class="item-inner">
											<div class="display-flex pL5">
												<img src="/website/style/delivery/images/pin.svg" alt="">
												<div class="item-title f16 pL15 text-align-left"><span>{$cat.title}</span></div>
											</div>
											<i class="icon icon-radio"></i>
										</div>

									</label>
								</li>
							{/foreach}
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="popup" id="popup-district">
			<div class="page">
				<div class="navbar">
					<div class="navbar-inner">
						<div class="text-align-center w100p pL15 pR15">
							<div class="text-align-center f17 f-700">Địa điểm</div>
							<input id="dis" onkeyup="_.mod.click.searchDis(this.id)" class="input-search" type="text"  placeholder="Tìm Quận/Huyện">
						</div>
					</div>
				</div>
				<div class="page-content">
					<div class="list listSort" >
						<ul id="ul-dis">
							<li class="pT20 pB20 text-align-center">Bạn cần chọn Tỉnh/Thành phố trước</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="popup" id="popup-ward">
			<div class="page">
				<div class="navbar">
					<div class="navbar-inner">
						<div class="text-align-center w100p pL15 pR15">
							<div class="text-align-center f17 f-700">Địa điểm</div>
							<input id="input-ward" onkeyup="_.mod.click.searchDis(this.id)" class="input-search" type="text"  placeholder="Tìm Xã/Phường">
						</div>
					</div>
				</div>
				<div class="page-content">
					<div class="list listSort" >
						<ul id="ul-input-ward">
							<li class="pT20 pB20 text-align-center">Bạn cần chọn Xã/Phường trước</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
