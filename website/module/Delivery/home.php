<?php

class HomeForm extends Form
{
	function __construct() {

	}
	
	function draw() {
		global $display;
		$city = Loyalty::allDistrict();
		$display->add("city",$city);
		$cart =System::$data['cart'];
		$receiver = [
			'fullname' => '',
			'phone' => '',
			'email' => '',
			'indentify' => ''
			
		];
		$app = DeliveryCore::getApp($cart['site_id']);
		if(empty($app)) {
			Url::redirect('/home');
		}
        $app_id = $cart['site_id'];
        $time_expired_campaign_1 = 1646067600; //28/2
        $time_expired_campaign_3 = 1650473999; //20/4
        if($app_id == 500000217 ) {
            if ($time_expired_campaign_1 < time() && $cart['created'] < $time_expired_campaign_1) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=1");
            }
            if ($time_expired_campaign_3 < time() && $cart['created'] < $time_expired_campaign_3) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=3");
            }
        }
        $time_expired_campaign_4 = 1659286799; //31/7

        if($app_id == 615 ) {
            if ($time_expired_campaign_4 < time() && $cart['created'] < $time_expired_campaign_4) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=4");
            }
        }
        $time_expired_campaign_643 = 1655744399; //20/6

        if($app_id == 643 ) {
            if ($time_expired_campaign_643 < time() && $cart['created'] < $time_expired_campaign_643) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=643");
            }
        }

        $time_expired_campaign_663 = 1659200399; //30/7

        if($app_id == 663 ) {
            if ($time_expired_campaign_663 < time() && $cart['created'] < $time_expired_campaign_663) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=663");
            }
        }



        $time_expired_campaign_690 = 1666717199; //25/10

        if($app_id == 690 ) {
            if ($time_expired_campaign_690 < time() && $cart['created'] < $time_expired_campaign_690) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=690");
            }
        }

        $time_expired_campaign_709 = 1668963599; //20/11

        if($app_id == 709 ) {
            if ($time_expired_campaign_709 < time() && $cart['created'] < $time_expired_campaign_709) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=709");
            }
        }

        $time_expired_campaign_710 = 1666285199; //20/10

        if($app_id == 710 ) {
            if ($time_expired_campaign_710 < time() && $cart['created'] < $time_expired_campaign_709) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=710");
            }
        }

        $time_expired_campaign_711 = 1666285199; //20/10
        if($app_id == 711 ) {
            if ($time_expired_campaign_711 < time() && $cart['created'] < $time_expired_campaign_711) {
                Url::redirect('delivery/' . $cart['token'] . "?page=expired&cart_no={$cart['id']}&type=711");
            }
        }



        $gift_detail_ids = array_column($cart['detail'],'gift_detail_id');
		$gifts = Gift::getDetailByID($gift_detail_ids,2);
		$countValue = array_count_values($gift_detail_ids);
		$display->add("gifts",$gifts);
		$display->add("countValue",$countValue);
		$display->add("cart",$cart);
		$display->add("logo",$app['logo']);
		$display->add("receiver",$receiver);
		return $display->output("home");
	}
}