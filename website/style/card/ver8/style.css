@import url('https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,500,500i,700,700i,900,900i&subset=vietnamese');

@font-face {
    font-family: "pincode-input-mask";
    src: url(data:font/woff;charset:utf-8;base64,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) format("woff");
}

:root {
    --app-bg-color: #720D5D;
    --app-border-color: #720D5D;
    --app-color: #720D5D;
    --text-color: #313131;
    --red-color: #FD2020;
    --orange-color: #F46F20;
    --red1-color: #CE1E25;
    --blue-color: #1787FB;
    --green-color: #0E850C;
    --expired-color: #4a4a4a;
    --new-color: #720D5D;
    --pink-color: #F49495;
    --white-color: #FFFFFF;
    --gray-color: #BCBCBC;
    --gray1-color: #BDBDBD;
    --gray2-color: #828282;
    --gray3-color: #F2F2F2;
    --border-input-color: #d9d9d9;
    --border-gift: #DADADA;
    --f7-radio-active-color: #720D5D;
    --f7-navbar-height: 60px;
    --f7-dialog-bg-color: #FFF;
    --f7-toolbar-bottom-shadow-image: transparent;
    --f7-searchbar-input-font-size: 16px;
    --f7-list-item-subtitle-font-size: 13px;
    --f7-list-item-title-font-size: 14px;
    --f7-radio-border-width: 1px;
    --f7-radio-size: 25px;
    --f7-toolbar-height: 70px;
    --f7-searchbar-input-bg-color: #fff;
    --f7-searchbar-bg-color: #FFF;
    --f7-dialog-width: 335px;
    --f7-dialog-text-color: #000;
    --f7-dialog-font-size: 14px;
    --f7-range-knob-color: #0C6885;
    --f7-range-knob-bg-color: #0C6885;
    --f7-range-bar-active-bg-color: #0C6885;
    --f7-range-knob-size: 20px;
    --f7-range-bar-size: 2px;
    --f7-list-margin-vertical: 15px;
    --f7-dialog-border-radius: 5px;
    --f7-tabbar-link-text-transform: unset;
    --f7-searchbar-input-height: 40px;
    --f7-searchbar-height: 40px;
    --f7-stepper-value-text-color: #000;
}

/*.md .icon-radio:after{*/
/*  width: 15px;*/
/*  height: 15px;*/
/*  margin-left: -8px;*/
/*  margin-top: -8px;*/
/*}*/
.color-red{
    color:#ff0000;
}
.color-organ {
    color: #F78F00;
}
header.has-shadow {
    box-shadow: 0 3px 6px #163C8429;
    opacity: 1;
    background: #FFFFFF;
}

.navbar:before {
    height: 0 !important;
}

header .navbar-top {
    padding: 0 100px;
    height: 100%;
}

header .navbar-top .lgxx a {
    display: flex;
    align-items: center;
}

header .navbar-top .lgxx img {
    height: 40px;
    object-fit: cover;
}

header .nav-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header .nav-right .bt {
    display: flex;
    align-items: center;
}

header .nav-right .btl {
    text-align: right;
}

header .nav-right .btl strong {
    font-size: 20px;
    width: 100%;
    float: right;
    color: #720D5D;
}

header .nav-right .btl span {
    font-size: 14px;
    color: #969EB2;
}

header .nav-right .btl span b {
    color: #ED1B2E;
}

header .nav-right .bt .history {
    background: #720D5D 0 0 no-repeat padding-box;
    border-radius: 20px;
    width: 108px;
    height: 40px;
    margin: 0 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header .nav-right .bt .history a {
    color: #ffffff;
    width: 100%;
    text-align: center;
    font-size: 14px
}

header .region {
    height: 18px;
    margin: 0 28px;
    background: #F0F3F5;
    border: 1px solid #E6ECF0;
    border-radius: 24px;
    padding: 3px;
}

header .region a {
    border-radius: 24px;
    font-size: 13px;
    padding: 1px 7px;
    color: #969EB2;
}

header .region a.active {
    background: #FFF;
    box-shadow: 0 3px 6px #163C8429;
    color: #720D5D;
}

header.hsearch .delivery {
    height: 24px;
    width: 1px;
    background: #E2E6E8
}

.gift_me_box .container {
    border-radius: 32px;
    box-shadow: 0 10px 30px #163C8429;
    overflow: hidden;
    max-width: 1160px;
    width: 100%;
    box-sizing: border-box;
    margin: 60px auto;
    padding: 20px;
    background-color: #FFFFFF;
    display: flex;
    justify-content: space-between;
}

.gift_me_box .container .box-recharge {
    width: 700px;
    height: 100%;
    background: #F0F3F5;
    border-radius: 24px;
}

.gift_me_box .container .box-recharge .box-recharge-inner {
    margin: 24px;
    height:100%;
}

.gift_me_box .container .box-recharge .box-recharge-inner .title {
    color: #720D5D;
    font-size: 24px;
}

.gift_me_box .container .box-recharge .box-recharge-inner .title-step {
    font-size: 16px;
    margin: 5px 0;
}

.price_voucher {
    height: 100px;
    background: #FFFFFF 0 0 no-repeat padding-box;
    border-radius: 8px;
    text-align: center;
    position: relative;
    box-shadow: 0 3px 10px #163C8429;
    margin-bottom: 24px;
    cursor: pointer;
}

.price_voucher:before {
    content: " ";
    position: absolute;
    z-index: 10;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border: 2px solid #E6ECF0;
    border-radius: 8px;
}

.price_voucher img {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    margin: auto;
    margin-top: 10px;
}

.price_voucher .img-money {
    width: 24px !important;
    height: 24px !important;
    object-fit: cover;
    border-radius: 0;
    margin-top: 20px;
}

.price_voucher div {
    color: #969EB2;
    font-size: 14px;
}

.carrier-active:before {
    border-color: #720D5D;
}
.choose_price_active:before{
    border-color: #720D5D;
}
.form-recharge {
    padding: 13px 0 28px 0;
}

.form-recharge form {
    position: relative;
}

.form-recharge input {
    height: 56px;
    background: #ffffff;
    border-radius: 12px;
    width: 100%;
    padding: 16px;
    font-size: 24px;
    border: 1px solid #DCE1E5;
}

.form-recharge input::placeholder {
    color: #969EB2;
}

.submit-form {
    width: 50px;
    height: 56px;
    background: #969EB2;
    border-radius: 0 12px 12px 0;
    text-align: center;
    position: absolute;
    top: 0;
    right: 0;
    pointer-events: none;
    cursor: pointer;
}

.submit-form.active-btn {
    background: #720D5D;
    pointer-events: unset;
}

.submit-form img {
    margin-top: 20px;
}

.button-submit {
    width: 100%;
    margin-top: 30px;
    background: #720D5D;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    height: 52px;
    align-items: center;
}

.button-submit a {
    color: #FFFFFF;
    font-size: 20px;
    font-weight: bold;
}

.card-box {
    width: 36%;
}

.card-box img {
    max-width: 100%;
    object-fit: cover;
    height:240px;
    border-radius:12px;
}

@media screen and (max-width: 1100px) {
    .card-box {
        display: none;
    }

    .gift_me_box {
        margin: 0 16px;
    }

    .gift_me_box .container {
        justify-content: center;
        width: 100%;
    }
}
.fft{
    background: url("images/background.svg") repeat-x top;
    margin: -320px 0 0;
    position: relative;
    z-index: -1;
    padding: 300px 0 15px;
}

.fft .ft{
    color: #fff;
    padding: 0 0 5px;
    font-size:16px;
    margin-top:120px;
}
.fft .ft a{
    color:#FFFFFF;
    font-size:20px;
}

.popup-recharge{
    width:350px !important;
    margin-left:-175px !important;
    top:20% !important;
    height:auto !important;
    margin-top:auto !important;
    background:#FFFFFF;
    border-radius:24px !important;
}

.popup-recharge .title{
    padding-top:10px;
    font-size:28px;
    height:56px;
    color:#632466;
    border-bottom:1px solid #cccccc;
    font-weight:bold;
    text-align:center;
}

.popup-recharge .content {
    padding:15px;
}

.popup-recharge .content .content-item{
    color:#969EB2;
    font-size:18px;
    border-bottom:1px solid #cccccc;
    justify-content:space-between;
    padding: 5px;
}

.popup-recharge .content .content-item span{
    color:#720D5D;
    font-weight:bold;
}
.popup-recharge .button-recharge{
    height:56px;
    cursor:pointer;
    display:flex;
    justify-content:center;
    align-items:center;
    font-size:18px;
    text-transform:uppercase;
    background:#720D5D;
    border-radius:12px;
    margin: 15px 16px;
    color:#fff;
}
.popup-recharge .button-close{
    color:#969EB2;
    cursor:pointer;
    font-size:16px;
    padding-bottom:20px;
    text-align:center;
}

/*success*/

.page-content-ver8{
    height: 100% !important;
    background: url(images/background.png) repeat-x center top 450px;
    background-size: 100% 100%;
    text-align: center;
}
.title-success {
    letter-spacing: 0;
    color: #720d5d;
    opacity: 1;
    font-size: 32px;
    font-weight: 700;
    margin-top: 100px;
}
.box-success {
    margin: 40px auto 0 auto;
    background: #fff 0 0 no-repeat padding-box;
    box-shadow: 0 3px 6px #163C8429;
    border-radius: 24px;
    opacity: 1;
    position: relative;
    width: 382px;
    padding: 12px;
    color: #474747;
}

.color-green{
    color:#37B34A;
}
.color-gray {
    color: #969EB2;
}

.f-400 {
    font-weight: 400;
}

.f-600 {
    font-weight: 600;
}

.color-blue {
    color: #1074FF;
}

.color-red {
    color: #FF0000;
}

.button-back{
    margin-top:20px;
    height: 48px;
    background: #720D5D;
    border: 1px solid #8E1675;
    border-radius: 28px;
    opacity: 1;
    cursor:pointer;
    color:#fff;
    font-size:16px;
    display:flex;
    align-items:center;
    justify-content:center;
}

.max-width-700{
    max-width:700px !important;
    min-height:588px;
}
.container.max-width-700 .tabs{
    width:100%;
}
.list-transaction{

}
.color-pink{
    color:#720D5D;
}
.justify-content-between{
    justify-content:space-between;
}
.item-history{
    padding:5px 5px 5px 10px;
    position:relative;
}
.item-history:before{
   content:"";
    width:6px;
    height:6px;
    border-radius:3px;
    background:#720D5D;
    position:absolute;
    left:0;
    top:12px;
}
.border-bottom{
    width:100%;
    height:4px;
    background:#F0F3F5;
}
.card_image{
      position:relative;
}
.card_image .card_content{
    position:absolute;
    left:24px;
    bottom:16px;
    color:#FFFFFF;
}
.gift-name{
    height:20px;
    overflow:hidden;
}

.total-price{
    border: 1px solid #D7E0E5;
    border-radius: 12px;
    padding: 10px 0;
}
.total-price .display-flex{
    padding: 10px 0;
    border-bottom: 1px solid #D7E0E5;
}
.total-price .display-flex:last-child{
    border-bottom: 0px;
}
@media (min-width: 630px) and (min-height: 450px) {
    .popup:not(.popup-tablet-fullscreen) {
        left: 50%;
    }
}