# Hướng dẫn chạy dự án với Docker

## Y<PERSON><PERSON> cầu
- Docker
- Docker Compose

## C<PERSON><PERSON> tr<PERSON><PERSON> Docker
- **PHP-FPM 5.6**: Xử lý code PHP
- **Nginx**: Web server proxy đến PHP-FPM
- **Port**: 8880 (có thể thay đổi trong docker-compose.yml)

## Cách chạy

### 1. Build và khởi động containers
```bash
docker-compose up -d --build
```

### 2. Kiểm tra trạng thái containers
```bash
docker-compose ps
```

### 3. Xem logs
```bash
# Xem tất cả logs
docker-compose logs

# Xem logs của PHP
docker-compose logs app

# Xem logs của Nginx
docker-compose logs nginx

# Theo dõi logs real-time
docker-compose logs -f
```

### 4. Truy cập ứng dụng
Mở trình duyệt và truy cập: http://localhost:8880

### 5. Dừng containers
```bash
docker-compose down
```

### 6. Dừng và xóa volumes
```bash
docker-compose down -v
```

## C<PERSON><PERSON> l<PERSON>nh hữu ích

### Truy cập vào container PHP để debug
```bash
docker-compose exec app bash
```

### Chạy Composer commands
```bash
# Install dependencies
docker-compose exec app composer install

# Update dependencies
docker-compose exec app composer update
```

### Xem PHP info
```bash
docker-compose exec app php -v
docker-compose exec app php -m
```

## Cấu hình

### Thay đổi port
Sửa file `docker-compose.yml`, thay đổi:
```yaml
ports:
  - "8880:80"  # Thay 8880 thành port mong muốn
```

### Cấu hình Nginx
Sửa file `docker/nginx/nginx.conf` để tùy chỉnh cấu hình web server.

### Cấu hình PHP
Nếu cần thêm extensions PHP hoặc thay đổi cấu hình, sửa file `Dockerfile`.

## Troubleshooting

### Container không khởi động được
```bash
# Xem logs chi tiết
docker-compose logs

# Rebuild containers
docker-compose down
docker-compose up --build
```

### Lỗi permissions
```bash
# Fix permissions
docker-compose exec app chown -R www-data:www-data /var/www/html
docker-compose exec app chmod -R 755 /var/www/html
```

### Lỗi Composer
```bash
# Clear composer cache
docker-compose exec app composer clear-cache
docker-compose exec app composer install
```
