<?php

class GiftCode
{

    static function vaildText($key)
    {
        $text = '';
        switch ($key) {
            case 'success':
                $text = 'Thành công';
                break;
            case 'isUsed':
                $text = 'Mã đã sử dụng';
                break;
            case 'notExistCoupon':
                $text = 'Không tìm thấy mã';
                break;
            case 'expried':
                $text = 'Mã đã hết hạn sử dụng';
                break;
            case 'notExistBrand':
                $text = 'Không tìm thấy nhà cung cấp';
                break;
            case 'notParam':
                $text = 'Không tìm thấy tham số sao';
                break;

        }
        return $text;
    }
    
    static function isValid($code, $brand_id)
    {
        $code = System::encrypt($code);
        $sql = "SELECT * FROM " . T_GIFT_CODE . " WHERE code = '" . $code . "' AND active = 1 AND status = 1 AND brand_id =" . $brand_id . " AND ( expired = 0 OR expired>=" . TIME_NOW . ")";
        return DB::fetch($sql);
    }
    
    static function useCode($code, $brand_id, $noCheck = false)
    {
        if($noCheck == true){
            $result = $code;
        }else {
            $result = GiftCode::isValid( $code, $brand_id );
        }
        if (!empty($result)) {
            $newRow = array('active' => 2, 'used' => TIME_NOW,'apiCall'=> 10);
            DB::update(T_GIFT_CODE, $newRow, " id =" . $result['id']);
            $cart_detail_id = $result['cart_detail_id'];
            if ($cart_detail_id) {
                $newCartRow = array(
                    'using_time' => TIME_NOW,
                    'delivery' => 3,
                );
                $cart = DB::fetch("SELECT * FROM " . T_CART . " WHERE id = {$result['cart_id']} LIMIT 1");
                $arrLog = array('cart_id' => $result['cart_id'], 'customer_id' => $cart['customer_id'], 'receiver_id' => $cart['receiver_id'], 'action_from' => 3, 'action_to' => 2, 'action_time' => TIME_NOW, 'action_type' => 5);
                self::insertLogCart($arrLog);
                DB::update(T_CART_DETAIL, $newCartRow, " id =" . $cart_detail_id);
            }
            return true;
        }
        return false;
    }
    static function insertLogCart($param)
    {
        $arrInsert = array();
        if (!empty($param)) {
            if (isset($param['cart_id']) && $param['cart_id'] > 0) {
                $arrInsert['cart_id'] = $param['cart_id'];
            }
            if (isset($param['user_id']) && $param['user_id'] >= 0) {
                $arrInsert['user_id'] = $param['user_id'];
            }
            if (isset($param['customer_id']) && $param['customer_id'] >= 0) {
                $arrInsert['customer_id'] = $param['customer_id'];
            }
            if (isset($param['receiver_id']) && $param['receiver_id'] >= 0) {
                $arrInsert['receiver_id'] = $param['receiver_id'];
            }
            if (isset($param['office_id']) && $param['office_id'] >= 0) {
                $arrInsert['office_id'] = $param['office_id'];
            }
            if (isset($param['brand_id']) && $param['brand_id'] >= 0) {
                $arrInsert['brand_id'] = $param['brand_id'];
            }
            if (isset($param['brand_uid']) && $param['brand_uid'] >= 0) {
                $arrInsert['brand_uid'] = $param['brand_uid'];
            }
            if (isset($param['action_type']) && $param['action_type'] >= 0) {
                $arrInsert['action_type'] = $param['action_type'];
            }
            if (isset($param['action_from']) && $param['action_from'] >= 0) {
                $arrInsert['action_from'] = $param['action_from'];
            }
            if (isset($param['action_to']) && $param['action_to'] >= 0) {
                $arrInsert['action_to'] = $param['action_to'];
            }
            if (isset($param['action_time']) && $param['action_time'] >= 0) {
                $arrInsert['action_time'] = $param['action_time'];
            }
            if (isset($param['change_to']) && $param['change_to'] != '') {
                $arrInsert['change_to'] = $param['change_to'];
            }
            if (isset($param['change_from']) && $param['change_from'] != '') {
                $arrInsert['change_from'] = $param['change_from'];
            }
            if (isset($param['note']) && $param['note'] != '') {
                $arrInsert['note'] = $param['note'];
            }
        }
        if (!empty($arrInsert)) {
            return DB::insert(T_CART_LOG, $arrInsert);
        }
        return false;
    }
    static function assignCode($cart_detail_id,&$code =''){
        if($cart_detail_id>0){
            $dataPost['idCartDetail'] = $cart_detail_id;
            $linkApi = LINK_URBOX_INTERNAL.'ajax.php?act=api&code=receivingGift';
            $curl_handle = curl_init($linkApi);
            curl_setopt($curl_handle, CURLOPT_HEADER, 0);
            curl_setopt($curl_handle, CURLOPT_VERBOSE, 0);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl_handle, CURLOPT_USERAGENT, "Mozilla/6.1 (compatible;)");
            curl_setopt($curl_handle, CURLOPT_POST, true);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $dataPost);
            curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, false);
            //curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, false);
            if(DEVMODE){
                curl_setopt($curl_handle, CURLOPT_USERPWD, "urbox:urbox@170**");
            }
            $returned_data = curl_exec($curl_handle);
            $newsRow = array(
                'linkapi' => $linkApi,
                'param' => json_encode($dataPost),
                'response' => $returned_data,
                'created' => TIME_NOW,
                'site' => 'page.urbox.vn'
            );
            DB::insert(T_LOGS_API,$newsRow);
            curl_close($curl_handle);
            $data = json_decode($returned_data,true);
            if(!empty($data)&&isset($data['done'])&&$data['done']==1){
                if(isset($data['code'])&&$data['code']!=''){
                    $code = $data['code'];
                }
                if(isset($data['receive_code'])&&$data['receive_code']!=''){
                    return $data['receive_code'];
                }
            }
        }
        return false;
    }

    static function removeCodeOutCart($arrCode){
        if(!empty($arrCode)){
            foreach ($arrCode as $info){
                DB::update(T_GIFT_CODE,array('cart_id'=>0,'cart_detail_id'=>0),"brand_id=".$info['brand_id']." AND code = '".$info['code']."'");
            }
        }

    }
    static function getCodeByCart($cart_id = []){
        if (empty($cart_id) || !is_array($cart_id)) return [];
        $id = implode(',',$cart_id);
        return DB::fetch_all("SELECT id,code from ". T_GIFT_CODE . " where cart_detail_id in($id)");
        
    }

}

?>
