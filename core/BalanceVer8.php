<?php
	
	class BalanceVer8 extends Balance
	{
		static function getApp($card)
		{
			if(empty($card) || !is_array($card)) return [];
			$app = DB::fetch("SELECT * FROM " . T_APP . " WHERE id=" . $card['app_id']);
			$issue = DB::fetch("SELECT image FROM " . T_CARD_ISSUE . " WHERE id=" . $card['issue_id']);
            $banner_welcome = "";
            if (!empty($issue) && !empty($issue['image'])) {
                $c_image = \MediaUrl::fromImageTitle($issue['image'], []);
                $banner_welcome = $image = isset($c_image[0]) ? $c_image[0] : '';
            }
            if (!empty($app)) {
                $logo = \MediaUrl::fromImageTitle($app['logo'], [320]);
                $app['logo'] = isset($logo[320]) ? $logo[320] : '';
                if ($image == "") {
                    $banner = \MediaUrl::fromImageTitle($app['banner'], []);
                    $image = isset($banner[0]) ? $banner[0] : '';
                    $_banner_welcome = \MediaUrl::fromImageTitle($app['banner_welcome'], []);
                    $banner_welcome = isset($_banner_welcome[0]) ? $_banner_welcome[0] : '';
                }
                $app['image'] = $image;
                $app['image_welcome'] = $banner_welcome;
				return $app;
			}
			
			return [
				'logo' => '',
				'image' => $image,
                'image_welcome' => $image,
			];
		}
		
		static function history($card)
		{
			if(empty($card) || !is_array($card)) return [];
			$sql = "SELECT a.id as transaction_id,a.note,a.type,bc.id as cart_number,a.money,c.gift_id,a.created,bc.pay_status,c.title,c.id,c.isPhysical,count(b.id) as qty,b.id as detail_id, gr.phone
                FROM " . T_CARD_TRANSACTION . " a
                LEFT JOIN " . T_CART . " bc on bc.id=a.cart_id
                LEFT JOIN " . T_CART_DETAIL . " b on b.cart_id=bc.id
                LEFT JOIN " . T_GIFT_DETAIL . " c ON c.id = b.gift_detail_id
                LEFT JOIN " . T_GIFT_RECEIVER . " gr on b.receiver_id=gr.id
                WHERE a.card_id ='{$card['id']}' AND a.status = ".IS_ON."
                GROUP BY a.id
                ORDER BY a.id DESC limit 500";
			
			$data = DB::query($sql);
			
			if (Language::$activeLang != "vi") {
				$result = [];
				while ($r = $data->fetch(PDO::FETCH_ASSOC)) {
					$content = Gift::getContent(Language::$activeLang, $r['gift_id']);
					if (isset($content['title']) && $content['title'] != '') {
						$r['title'] = $content['title'];
					}
					$result[] = $r;
				}
			} else {
				$result = $data->fetchAll(PDO::FETCH_ASSOC);
			}
            $cartDetailIds = [];
            foreach ($result as &$item) {
                if($item['type'] == 3){
                    $cart_detail_id = $item['cart_number'];
                    if($cart_detail_id > 0) {
                        $cartDetailIds[] = $cart_detail_id;
                        $item['cart_detail_id'] = $cart_detail_id;
                    }
                }
            }
            if(!empty($cartDetailIds)) {
                $cartDetailIds = implode(",", $cartDetailIds);
                $carts = DB::fetch_all("SELECT cart_id as id,phone from " . T_CART_DETAIL . " a
                
                 LEFT JOIN ".T_GIFT_RECEIVER." b on a.receiver_id=b.id
                 WHERE a.cart_id in($cartDetailIds)");
                foreach ($result as &$item) {
                    if($item['type'] == 3 && isset($carts[$item['cart_detail_id']])){
                        $item['cart_number'] = $carts[$item['cart_detail_id']]['id'];
                        $item['phone'] = $carts[$item['cart_detail_id']]['phone'];
                    }
                }
            }
			return $result;
		}

//    survey
		static function getAnswer($card_id)
		{
			$card_id = (int)$card_id;
			if($card_id <=0) return [];
			$stmt_cus_survey = DB::query("SELECT survey_id  FROM " . T_SURVEY_RESULT . " WHERE card_id = '{$card_id}' GROUP BY survey_id ");
			return $stmt_cus_survey->fetchAll(PDO::FETCH_COLUMN);
		}
		
		static function checkSurvey($card)
		{
			if(empty($card) || !is_array($card)) return [];
			if ($card != false && $card['survey_id'] > 0) {
				$survey = self::viewSurvey($card['survey_id']);
				if (!empty($survey)) {
					$cus_survey = self::getAnswer($card['id']);
					if (count($cus_survey) <= 0) {
						return $card['survey_id'];
					}
					return 0;
				}
			}
			return 0;
		}
		
		static function getQuestion($question_id)
		{
			$question_id = (int)$question_id;
			if($question_id <=0) return [];
			return DB::fetch("SELECT * FROM " . T_SURVEY_QUESTION . " WHERE id={$question_id}");
		}
		
		static function issetAnswer($question_id = 0, $card_id = 0)
		{
			$card_id = (int)$card_id;
			$question_id = (int)$question_id;
			if($question_id <=0) return [];
			if ($question_id == 0 || $card_id == 0) {
				return true;
			}
			return DB::fetch("SELECT id FROM " . T_SURVEY_RESULT . " WHERE question_id={$question_id} AND card_id={$card_id}");
			
		}
		
		static function saveSurvey($survey, $card)
		{
			if(empty($card) || !is_array($card) || !is_array($survey)) return [];
			// trong trường hợp thằng nào vui tính insert hàng loạt vào db
			if (is_numeric(implode('', array_keys($survey)))) {
				foreach ($survey as $item) {
					self::saveSurvey($item, $card);
				}
			} else {
				if (!empty($card['phone'])) {
					$_phone = System::decrypt($card['phone'], CRYPT_KEY);
					$cus = self::getCus($_phone);
					if (isset($cus['id']) && $cus['id'] > 0) {
						$customer_id = $cus['id'];
					} else {
						$cus = self::saveCus($_phone);
						if (!empty($cus)) {
							$customer_id = $cus;
						} else {
							$customer_id = 0;
						}
					}
				} else {
					$customer_id = 0;
				}
				$card_id = $survey['card_id'];
				$survey_id = $survey['survey_id'];
				$question_id = $survey['question_id'];
				$answer_id = $survey['answer_id'];
				$content = $survey['content'];
				if ($card_id > 0 && $survey_id > 0 && $question_id > 0) {
					$isset = self::issetAnswer($question_id, $card_id);
					if ($isset == true || !empty($isset)) {
						return false;
					}
					return DB::insert(T_SURVEY_RESULT, ['card_id' => $card_id, 'survey_id' => $survey_id, 'customer_id' => $customer_id, 'question_id' => $question_id, 'answer_id' => $answer_id, 'content' => $content, 'created' => TIME_NOW]);
				}
				return false;
			}
		}
		
		static function countRequire($id)
		{
			$id = (int)$id;
			if($id <=0) return [];
			return DB::fetch("SELECT COUNT(id) as qty FROM " . T_SURVEY_QUESTION . " WHERE survey_id ={$id} AND isRequired=" . IS_ON);
		}
		
		static function viewSurvey($id)
		{
			$id = (int)$id;
			if($id <=0) return [];
			return DB::fetch("SELECT id,title,description from " . T_SURVEY . " WHERE id={$id} AND status=" . IS_ON);
		}
		
		static function getSurvey($survey_id = 0)
		{
			$survey_id = (int)$survey_id;
			if($survey_id <=0) return [];
			$survey_name = DB::fetch("SELECT id,title,description,image from " . T_SURVEY . " WHERE id={$survey_id}");
			$stmt_survey = DB::query("SELECT * FROM " . T_SURVEY_QUESTION . " WHERE survey_id={$survey_id}");
			$question = $stmt_survey->fetchAll(PDO::FETCH_ASSOC);
			foreach ($question as $k => $v) {
				$ans = DB::query("SELECT id, title FROM " . T_SURVEY_ANSWER . " WHERE question_id= {$v['id']} and survey_id = {$survey_id}")->fetchAll(PDO::FETCH_ASSOC);
				$question[$k]['answer'] = $ans;
			}
			$banner = MediaUrl::fromImageTitle($survey_name['image'], [640]);
			$survey_name['image'] = isset($banner[640]) ? $banner[640] : '';
			
			return [
				'survey' => $survey_name,
				'question' => $question
			];
		}
		
		static function getCus($phone)
		{
			$cus = "SELECT id FROM " . T_CUSTOMER . " WHERE phone='{$phone}'";
			return DB::fetch($cus);
		}
		
		static function saveCus($phone)
		{
			$is_phone = FunctionLib::is_phone($phone);
			if($is_phone === false) return 0;
			$cus = self::getCus($phone);
			if ($cus === 0 || (isset($cus['id']) && $cus['id'] > 0)) {
				return false;
			}
			$data = [
				'phone' => $phone,
				'status' => 1,
				'active' => 1,
				'created' => TIME_NOW
			];
			return DB::insert(T_CUSTOMER, $data);
		}
		
		/*Topup*/
		public static function giftTopup($type)
		{
			$type =(int)$type;
			if($type == GIFT_TYPE_PHONE){
				$sql = "SELECT a.id,a.name,b.logo FROM " . T_GIFT . " a LEFT JOIN ".T_BRAND." b on a.brand_id=b.id WHERE a.id in(".GIFT_PHONE_ID.") AND a.status=2 AND a.id <> 1321";
			}else {
				$sql = "SELECT a.id,a.name,b.logo FROM " . T_GIFT . " a LEFT JOIN ".T_BRAND." b on a.brand_id=b.id WHERE a.type=$type AND a.status=2 AND a.id <> 1321";
			}
			$gift = DB::query($sql);
			$data = [];
			while ($row = $gift->fetch(PDO::FETCH_ASSOC)){
			    $logo = MediaUrl::fromImageTitle($row['logo'],[160]);
			    $row['logo'] = !empty($logo) && isset($logo[160])? $logo['160']: '';
				$child = DB::query("SELECT id,title,price from ". T_GIFT_DETAIL . " WHERE gift_id={$row['id']} AND code_quantity > 0 and status=1 ORDER BY price asc")->fetchAll(PDO::FETCH_ASSOC);
				$row['detail'] = $child;
				$data[] = $row;
			}
			return $data;
			
		}
	}