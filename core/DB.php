<?php
global $db;
$db = new DB();
$db->connect();

Class DB
{
	static function existColumn($tableName = '', $columnName = '')
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}

		$sql = "SHOW COLUMNS FROM `$tableName` LIKE '$columnName'";
		$start_time = System::getTime();
		$re = self::fetch($sql);
		$end_time = System::getTime();
		$executionTime = $end_time - $start_time;
		self::log($sql, $executionTime);

		return $re;
	}

	static function connect()
	{
		global $db;
		$db = new PDO('mysql:host=' . DB_MASTER_SERVER . ';dbname=' . DB_MASTER_NAME,
			DB_MASTER_USER,
			DB_MASTER_PASSWORD,
			array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES 'utf8'", PDO::ATTR_EMULATE_PREPARES => false, PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
		);

		return $db;
	}

	static function fetch($sql = false, $field = false, $default = false)
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}

		if ($sql) {
			$re = self::query($sql);
			if ($re) {
				$r = $re->fetch(PDO::FETCH_ASSOC);
				if ($field && isset($r[$field])) {
					return $r[$field];
				} elseif ($default !== false) {
					return $default;
				}
				return $r;
			}
		}
	}

	static function query($sql)
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}
		$start_time = System::getTime();
		$end_time = System::getTime();
		$excuteTime = $end_time - $start_time;
		self::log($sql, $excuteTime);
		try {
			$re = $db->query($sql);
		}catch (Exception $e){
			//gui email khi co loi truy van
			if(!preg_match('#localhost#', WEB_ROOT) && DOMAIN_NAME == 'page.urbox.vn') {
				$email_bug = EMAIL_NOTIFY;
				$server = FunctionLib::ip();
				$content = 'Time: '.date('d-M-Y H:i:s').'<br />';
				$content.= 'Server: '. $server .'<br />';
				$content.= '<b>mysql_error: '.$db->errorInfo()[2].'  in <br />['.$sql .']</b><br />';
				$content.= '<pre>'.print_r(debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT,5), true).'</pre><br /> -- end error --';
				System::send_mail('System',	$email_bug, 'URPAGE SQL Error - '.WEB_ROOT.' - '.date('d-M-Y H:i'),	$content);
				throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
			}
			if (DEBUG) {
				echo '<p><font face="Courier New,Courier" size=3><b>' .json_encode($db->errorInfo()) . '  in ' . $sql . '</b></font><br><b>Run at:</b><br />';
				System::debug(debug_backtrace());
				exit ();
			} else {
				throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
			}
		}
		return $re;
	}

	static function insert($table, $values, $replace = false)
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}

		if ($replace) {
			$sql = 'REPLACE';
		} else {
			$sql = 'INSERT INTO';
		}

		$sql .= ' `' . $table . '`(';
		$i = 0;
		if (is_array($values)) {
			foreach ($values as $key => $value) {
				if (($key === 0) or is_numeric($key)) {
					$key = $value;
				}
				if ($key) {
					if ($i != 0) {
						$sql .= ',';
					}
					$sql .= '`' . $key . '`';
					$i++;
				}
			}
			$sql .= ') VALUES(';
			$i = 0;

			foreach ($values as $key => $value) {
				if (is_numeric($key) or $key === 0) {
					$value = Url::getParam($value);
				}

				if ($i != 0) {
					$sql .= ',';
				}

				if ($value === 'NULL') {
					$sql .= 'NULL';
				} else {
					$sql .= '\'' . self::escape($value) . '\'';
				}
				$i++;
			}
			$sql .= ')';


			$start_time = System::getTime();
			
			$end_time = System::getTime();
			$excuteTime = $end_time - $start_time;
			self::log($sql, $excuteTime);
			try {
				$db->exec($sql);
			}catch (Exception $e){
				//gui email khi co loi truy van
				if(!preg_match('#localhost#', WEB_ROOT)) {
					$email_bug = EMAIL_NOTIFY;
					$server = FunctionLib::ip();
					$content = 'Time: '.date('d-M-Y H:i:s').'<br />';
					$content.= 'Server: '. $server .'<br />';
					$content.= '<b>mysql_error: '.$db->errorInfo()[2].'  in <br />['.$sql .']</b><br />';
					$content.= '<pre>'.print_r(debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT,5), true).'</pre><br /> -- end error --';
					System::send_mail('System',	$email_bug, 'URPAGE SQL Error - '.WEB_ROOT.' - '.date('d-M-Y H:i'),	$content);
					throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
				}
				if (DEBUG) {
					echo '<p><font face="Courier New,Courier" size=3><b>' .json_encode($db->errorInfo()) . '  in ' . $sql . '</b></font><br><b>Run at:</b><br />';
					System::debug(debug_backtrace());
					exit ();
				} else {
					throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
				}
			}
			$id = $db->lastInsertId();
			return $id;
		}
	}

	static function update($table, $values, $condition,$self = 0)
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}

		$sql = 'UPDATE `' . $table . '` SET ';
		$i = 0;
		if ($values) {
			foreach ($values as $key => $value) {
				if ($key === 0 or is_numeric($key)) {
					$key = $value;
					$value = Url::getParam($value);
				}

				if ($i != 0) {
					$sql .= ',';
				}

				if ($key) {
					if ($value === 'NULL') {
						$sql .= '`' . $key . '`=NULL';
					} else {
						$sql .= '`' . $key . '`=\'' . self::escape($value) . '\'';
					}
					$i++;
				}
			}
			$sql .= ' WHERE ' . $condition;
			$start_time = System::getTime();
			$end_time = System::getTime();
			$executionTime = $end_time - $start_time;
			self::log($sql, $executionTime);
			try {
				$affected_rows = $db->exec($sql);
				//hash card khi update
				if($table == T_CARD && $self == 0){
					$card = DB::fetch("SELECT id,phone,needActive,pin,number,token,money,status from " . T_CARD . " where $condition");
					if(!empty($card)) {
						DB::update(T_CARD,['hash' => Card::cardHash( $card )],$condition,1);
					}
				}
			}catch (Exception $e){
				//gui email khi co loi truy van
				if(!preg_match('#localhost#', WEB_ROOT)) {
					$email_bug = EMAIL_NOTIFY;
					$server = FunctionLib::ip();
					$content = 'Time: '.date('d-M-Y H:i:s').'<br />';
					$content.= 'Server: '. $server .'<br />';
					$content.= '<b>mysql_error: '.$db->errorInfo()[2].'  in <br />['.$sql .']</b><br />';
					$content.= '<pre>'.print_r(debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT,5), true).'</pre><br /> -- end error --';
					System::send_mail('System',	$email_bug, 'URPAGE SQL Error - '.WEB_ROOT.' - '.date('d-M-Y H:i'),	$content);
					throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
				}
				if (DEBUG) {
					echo '<p><font face="Courier New,Courier" size=3><b>' .json_encode($db->errorInfo()) . '  in ' . $sql . '</b></font><br><b>Run at:</b><br />';
					System::debug(debug_backtrace());
					exit ();
				} else {
					throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
				}
			}
			

			return $affected_rows;
		}
		return false;
	}

	static function delete($table, $condition)
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}

		$sql = 'DELETE FROM `' . $table . '` WHERE ' . $condition;

		$start_time = System::getTime();
		$end_time = System::getTime();
		$excuteTime = $end_time - $start_time;
		self::log($sql, $excuteTime);
		try {
			$db->exec($sql);
		}catch (Exception $e){
			//gui email khi co loi truy van
			if(!preg_match('#localhost#', WEB_ROOT)) {
				$email_bug = EMAIL_NOTIFY;
				$server = FunctionLib::ip();
				$content = 'Time: '.date('d-M-Y H:i:s').'<br />';
				$content.= 'Server: '. $server .'<br />';
				$content.= '<b>mysql_error: '.$db->errorInfo()[2].'  in <br />['.$sql .']</b><br />';
				$content.= '<pre>'.print_r(debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT,5), true).'</pre><br /> -- end error --';
				System::send_mail('System',	$email_bug, 'URPAGE SQL Error - '.WEB_ROOT.' - '.date('d-M-Y H:i'),	$content);
				throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
			}
			if (DEBUG) {
				echo '<p><font face="Courier New,Courier" size=3><b>' .json_encode($db->errorInfo()) . '  in ' . $sql . '</b></font><br><b>Run at:</b><br />';
				System::debug(debug_backtrace());
				exit ();
			} else {
				throw new Exception('Có lỗi truy vấn cơ sở dữ liệu');
			}
		}

		return true;
	}

	static function escape($sql)
	{
		return addslashes($sql);
	}

	static function parseBackTrace()
	{
		$out = [];
		foreach (debug_backtrace() as $k => $v) {
			if ($v['function'] == 'run' && strpos($v['file'], 'index.php')) {
				break;
			} else $out[] = $v;
		}
		return $out;
	}

	static function log($sql, $excuteTime)
	{
		if (DEBUG) {
			System::$data['db']['time'] += $excuteTime;
			System::$data['db']['quantity']++;
			System::$data['db']['query'][] = array(
				'content' => $sql,
				'time' => $excuteTime,
				'backTrace' => self::parseBackTrace(),
			);
		}
	}

	static function addColumn($tableName, $columnName, $columnDefinition)
	{
		global $db;

		if (empty($db)) {
			$db = self::connect();
		}

		$sql = "ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition";
		$start_time = System::getTime();
		$affected_columns = $db->exec($sql);
		$end_time = System::getTime();
		$executionTime = $end_time - $start_time;
		self::log($sql, $executionTime);
		return $affected_columns;
	}

    static function fetch_all($sql = false) {
        global $db;

        if (empty($db)) {
            $db = self::connect();
        }

        if ($sql) {
            $result = array ();

            $re = self::query($sql);
            if ($re) {
                while ($row = $re->fetch(PDO::FETCH_ASSOC)){
                    if (isset ( $row ['id'] ))
                        $result [$row ['id']] = $row;
                    else
                        $result [] = $row;
                }

                return $result;
            }
        }
        return false;
    }

    static function beginTransaction()
    {
        global $db;
        if (empty($db))
            $db = self::connect();

        return $db->beginTransaction();

    }

    static function rollback()
    {
        global $db;
        if (empty($db))
            $db = self::connect();

        $result = $db->rollback();
    }

    static function commit()
    {
        global $db;
        if (empty($db))
            $db = self::connect();

        $db->commit();
    }

}