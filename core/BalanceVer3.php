<?php

class BalanceVer3 extends Balance
{
    static function getApp($card)
    {
        if (empty($card) || !is_array($card)) return [];
        $app = DB::fetch("SELECT * FROM " . T_APP . " WHERE id=" . $card['app_id']);
        $issue = DB::fetch("SELECT image FROM " . T_CARD_ISSUE . " WHERE id=" . $card['issue_id']);
        $image = "";
        if (!empty($issue) && !empty($issue['image'])) {
            $c_image = MediaUrl::fromImageTitle($issue['image'], []);
            $image = isset($c_image[0]) ? $c_image[0] : '';
        }
        if (!empty($app)) {
            $logo = MediaUrl::fromImageTitle($app['logo'], [320]);
            $app['logo'] = isset($logo[320]) ? $logo[320] : '';
            if ($image == "") {
                $banner = MediaUrl::fromImageTitle($app['banner'], []);
                $image = isset($banner[0]) ? $banner[0] : '';
            }
            $_banner_welcome = \MediaUrl::fromImageTitle($app['banner_welcome'], []);
            $banner_welcome = isset($_banner_welcome[0]) ? $_banner_welcome[0] : '';

            $app['image'] = $image;
            $app['image_welcome'] = $banner_welcome;
            return $app;
        }

        return [
            'logo' => '',
            'image' => $image,
            'image_welcome' => $image,
        ];
    }

    static function getGiftByBrand($brand_id = null, $city = null)
    {
        $brand_id = (int)$brand_id;
        if ($brand_id <= 0) {
            return [];
        }
        $order = " price asc";
        $param = [];
        $param[] = 'code_quantity > 0';
        $param[] = 'status=1';
        $param[] = 'type<>3';
        $param[] = 'type<>8';
        $param[] = 'isUnfixPrice=' . IS_OFF;
        //nếu lọc theo brand
        if ($brand_id > 0) {
            $param[] = " brand_id={$brand_id}";
        }

        $brand = self::getBrand($brand_id);
        if (empty($brand)) {
            return [];
        }
        $giftCity = array(0);
        $access_loc = self::getAccessLoc();
        if ($city != null) {
            $city_id = $city;
        } else {
            $city_id = CookieLib::get("C_MY_CITY");
        }
        $city_id = (int)$city_id;
        //city
        if ($brand['online'] == IS_OFF && $access_loc != 1) {
            //nếu ko cho phep truy cập vị trí
            if ($city_id != 0) {
                $where_c = ["brand_id=$brand_id"];
                if ($city_id == -1) {
                    // tinhr thanh khac
                    $provide = self::allProvide(true);
                    $provide_id = array_column($provide, 'id');
                    if (count($provide_id) > 0) {
                        $where_c[] = "  city_id NOT IN(" . implode(',', $provide_id) . ")";
                    }
                } elseif ($city_id != 0) {
                    $where_c[] = " (city_id={$city_id} OR city_id=0)";
                }
                $sql_city = "SELECT gift_detail_id FROM " . T_GIFT_CITY;
                $where_condition = FunctionLib::addCondition($where_c, true);
                $sql_city .= $where_condition;
                $stmt = DB::query($sql_city);
                if ($stmt) {
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        $giftCity[] = $row['gift_detail_id'];
                    }
                }
            }
        }


        //giftset
        $id_gift_set = isset(System::$data['card']['gift_id']) && System::$data['card']['gift_id'] > 0 ? System::$data['card']['gift_id'] : 0;
        if ($id_gift_set <= 0) {
            $s = "SELECT DISTINCT (gift_detail_id) FROM " . T_GIFT_LIMIT . " WHERE type=2 and status=" . IS_ON . " AND app_id =29";
            $stmt = DB::query($s);
            $giftset_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
            if (empty($giftset_ids)) $giftset_ids = [0];
        } else {
            $s1 = "SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_id={$id_gift_set}";
            $stmt = DB::query($s1);
            $giftset_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
            if (empty($giftset_ids)) $giftset_ids = [0];
        }

        if ($city_id != 0) {
            if ($brand['online'] == IS_OFF && $access_loc != 1) { // nếu tìm kiếm theo thành phố
                if ($id_gift_set <= 0) {
                    $id_gift = array_diff($giftCity, $giftset_ids);
                } else {
                    $id_gift = array_intersect($giftCity, $giftset_ids);
                }
                if (empty($id_gift)) $id_gift = [0];
                $param[] = " id IN (" . implode($id_gift, ',') . ")";
            } else { //nếu là bán hàng online hoặc lấy toàn bộ tỉnh thành
                if ($id_gift_set <= 0) {
                    $param[] = " id NOT IN (" . implode($giftset_ids, ',') . ")";
                } else {
                    $param[] = " id IN (" . implode($giftset_ids, ',') . ")";
                }
            }
        } else {
            if ($id_gift_set <= 0) {
                $param[] = " id NOT IN (" . implode($giftset_ids, ',') . ")";
            } else {
                $param[] = " id IN (" . implode($giftset_ids, ',') . ")";
            }
        }


        $condition = FunctionLib::addCondition($param, true);
        $sql = "SELECT id,gift_id,title,price,avatar,isPhysical FROM " . T_GIFT_DETAIL . $condition . " ORDER BY $order";
        $re = DB::query($sql);
        $data = array();
        if ($re) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['avatar'], [320]);
                $temp = array(
                    'id' => $row['id'],
                    'gift_id' => $row['gift_id'],
                    'title' => $row['title'],
                    'price' => $row['price'],
                    'isPhysical' => $row['isPhysical'],
                    'image' => isset($images[320]) ? $images[320] : ''
                );
                $data[$row['id']] = $temp;
            }
        }
        return $data;
    }

    static function physicalCategory($parent_id = 121)
    {
        $parent_id = (int)$parent_id;
        if ($parent_id <= 0) {
            return array();
        }
        $category = [];
        $re = DB::query("SELECT * FROM " . T_CATEGORY . " WHERE status=1 AND parent_id=" . $parent_id . " ORDER BY weight ASC");
        if ($re) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['avatar'], [320]);
                $temp = array(
                    'id' => $row['id'],
                    'title' => $row['title'],
                    'image' => isset($images[320]) ? $images[320] : '',
                    'link' => '/card/' . System::$data['card']['token'] . '?cat_id=' . $row['id'],
                );
                $category[] = $temp;
            }
        }
        return $category;
    }

    static function viewCate($id)
    {
        $id = (int)$id;
        if ($id <= 0) {
            return array();
        }
        return DB::fetch("SELECT id,title FROM " . T_CATEGORY . " WHERE id={$id} limit 1");
    }

    /**
     * @param $card array
     * @param $cat_id int
     * @param $pages array
     * @param $params array
     * @return  array|null
     * */
    static function getListGifts($card, $cat_id = 0, $pages = [], $params = [])
    {
        if (empty($card) || !is_array($card)) return false;
        $cat_id = (int)$cat_id;
        if (empty($pages)) {
            $pages['per_page'] = 10;
            $pages['page_num'] = 1;
        }
        if (empty($pages['per_page'])) {
            $pages['per_page'] = 10;
        }
        if (empty($pages['page_num'])) {
            $pages['page_num'] = 1;
        }
        $param = [];
        $param[] = 'a.code_quantity > 0';
        $param[] = 'a.status=1';
        $param[] = 'a.type<>3';
        $param[] = 'a.type<>8';
        $param[] = 'a.justGetOrder <> 2';
        $param[] = 'a.isUnfixPrice=' . IS_OFF;
        $orderBy = "";
        if (isset($params['orderBy']) && in_array($params['orderBy'], ['new', 'popular', 'priceDesc', 'priceAsc', 'default', 'hot'])) {
            switch ($params['orderBy']) {
                case "new":
                    $orderBy = " a.id desc";
                    break;
                case "popular":
                    $orderBy = " a.view desc";
                    break;
                case "priceDesc":
                    $orderBy = " a.price desc";
                    break;
                case "priceAsc":
                    $orderBy = " a.price Asc";
                    break;
                case "hot":
                    $orderBy = " a.is_hot DESC";
                    break;
                default:
                    break;
            }
        }


        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $param[] = "a.title like '%{$params['keyword']}%'";
        }

        if (isset($params['isPhysical']) && in_array($params['isPhysical'], [1, 2])) {
            $param[] = "a.isPhysical={$params['isPhysical']}";
        }

        $giftID = [];
        $giftSubID = [];
        $brandSubID = [];
        $id_gift_set = $card['gift_id'];
        if ($id_gift_set <= 0) {
            $s = "SELECT gift_detail_id,brand_id,type FROM " . T_GIFT_LIMIT . " WHERE status=" . IS_ON . " AND app_id =" . $card['app_id'];
            $stmt = DB::query($s);
            while ($data_limit = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if ($data_limit['type'] == 2) {
                    $giftSubID[] = $data_limit['gift_detail_id'];
                } else {
                    $brandSubID[] = $data_limit['brand_id'];
                }
            }
        } else {
            $s1 = "SELECT distinct (gift_detail_id) as gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_id={$id_gift_set}";
            $stmt = DB::query($s1);
            $data_gift_id = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $giftID = [0];
            if (!empty($data_gift_id)) {
                $giftID = array_column($data_gift_id, 'gift_detail_id');
            }
        }


        //&& $params['isPhysical'] == 1
        if ($cat_id > 0) {
            $cat_arr[$cat_id] = $cat_id;
            $catData = DB::query("SELECT * FROM " . T_CATEGORY . " WHERE status=1 AND parent_id=" . $cat_id);
            if ($catData) {
                while ($rw = $catData->fetch(PDO::FETCH_ASSOC)) {
                    $cat_arr[$rw['id']] = $rw['id'];
                }
            }
            if (!empty($cat_arr)) {
                $param[] = 'a.cat_id IN(' . implode(',', $cat_arr) . ')';
            }
        }

        //neếu quà voucher thì tìm location, quà vật lý không cần tìm
        if (!isset($params['isPhysical']) || $params['isPhysical'] != 2) {
            #loc theo LOCATION
            $allow = self::getAccessLoc();
            if ($allow == 1) {
                $brand_id = self::getNearBrand($cat_id);
                if (empty($brand_id)) {
                    $brand_id = [0];
                }

                // loại bỏ các brand bị chặn nếu có
                if (!empty($brandSubID)) {
                    $brand_id = array_diff($brand_id, $brandSubID);
                }
                $param[] = "b.id in (" . implode(",", $brand_id) . ")";
            } else {
                $city_id = CookieLib::get("C_MY_CITY");
                $city_id = (int)$city_id;
                if ($city_id != 0) {
                    $where_c = [];
                    if ($city_id == -1) {
                        // tinhr thanh khac
                        $provide = self::allProvide(true);
                        $provide_id = array_column($provide, 'id');
                        if (count($provide_id) > 0) {
                            $where_c[] = "  city_id NOT IN(" . implode(',', $provide_id) . ")";
                        }
                    } elseif ($city_id != 0) {
                        $where_c[] = " (city_id={$city_id} OR city_id=0)";
                    }

                    $sql_city = "SELECT gift_detail_id FROM " . T_GIFT_CITY;
                    $where_condition = FunctionLib::addCondition($where_c, true);
                    $sql_city .= $where_condition;
                    $stmt = DB::query($sql_city);

                    $giftId_city = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    if (!empty($giftId_city)) {
                        if (!empty($giftID)) {
                            // nếu là giftset, lọc những id có trỏng gift set và có ở thành phố X
                            $a = array_intersect($giftID, $giftId_city);
                        } else {
                            // nếu all quà, lọc những gift có ở thành phố X và không bị chặn
                            $a = array_diff($giftId_city, $giftSubID);
                        }
                    } else {
                        $a = [0];
                    }
                    if (empty($a)) $a = [0];
                    $param[] = "a.id IN (" . implode(",", $a) . ")";

                } else {
                    //nếu tìm toàn bộ tỉnh thành
                    if (!empty($giftID)) {
                        // nếu là giftset
                        $param[] = "a.id IN (" . implode(",", $giftID) . ")";
                    } else {
                        // nếu all quà, lọc những gift bị chặn
                        if (!empty($giftSubID))
                            $param[] = "a.id NOT IN (" . implode(",", $giftSubID) . ")";
                    }

                }
            }
        } else {
            if (!empty($giftID)) {
                // nếu là giftset
                $param[] = "a.id IN (" . implode(",", $giftID) . ")";
            } else {
                // nếu all quà, lọc những gift bị chặn
                if (!empty($giftSubID))
                    $param[] = "a.id NOT IN (" . implode(",", $giftSubID) . ")";
            }
        }


        $condition = FunctionLib::addCondition($param, true);

        $sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,openTime,a.title,a.price,a.view,a.avatar,a.is_hot,b.logo,b.title as brand_title,c.title as catagory,a.isPhysical, a.avatar as image
        FROM " . T_GIFT_DETAIL . " a
        LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
        LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
        $condition ORDER BY " . ($orderBy != '' ? $orderBy : "a.id DESC");

        $re = PagingGB::query($sql, $pages['per_page'], $pages['page_num'], 'a.id');
        $data = array();
        if ($re && PagingGB::$totalPage >= $pages['page_num']) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['logo'], [160]);
                $banner = MediaUrl::fromImageTitle($row['image'], [640]);
                $temp = array(
                    'title' => (isset($params['length']) && $params['length'] > 0) ? StringLib::truncateHtml($row['title'], (int)$params['length']) : $row['title'],
                    'image' => isset($images[160]) ? $images[160] : '',
                    'banner' => isset($banner[640]) ? $banner[640] : '',
                );
                $content = Gift::getContent(Language::$activeLang, $row['gift_id']);
                if ($content['title'] != "") {
                    $temp['title'] = (isset($params['length']) && $params['length'] > 0) ? StringLib::truncateHtml($content['title'], (int)$params['length']) : $content['title'];
                }
                $data[$row['id']] = $temp + $row;
            }
        }

        return $data;
    }

    static function getGiftById($card, $id = 0)
    {
        $id = (int)$id;
        if ($id == "" || $id <= 0) return [];
        if (empty($card) || !is_array($card)) return [];
        if ($card['gift_id'] > 0) {
            $getSiteGift = DB::fetch("SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_detail_id=$id AND gift_id=" . $card['gift_id']);
            if (empty($getSiteGift)) {
                return [];
            }
        }

        $gift = GiftDetail::get($id);
        if (!empty($gift)) {
            DB::update(T_GIFT_DETAIL, array('view' => $gift['view'] + 1), 'id=' . $id);

            $content = Gift::getContent(Language::$activeLang, $gift['gift_id']);
            if ($content['title'] != "") {
                $gift['title'] = $content['title'];
            }
            $gift['brand'] = DB::fetch("SELECT a.title,a.logo,a.created,a.openTime,a.priceRange,a.online,b.title as cat_title FROM " . T_BRAND . " a LEFT JOIN " . T_CATEGORY . " b on a.cat_id=b.id WHERE a.id=" . $gift['brand_id']);
            $gift['content'] = StringLib::post_db_parse_html($content['content']);
            $gift['note'] = StringLib::post_db_parse_html($content['note']);

            $s_image = "SELECT image from " . T_BRAND_IMAGE . " WHERE brand_id={$gift['brand_id']} AND status=" . IS_ON . " ORDER BY type desc,id asc";
            $stmt_image = DB::query($s_image);
            $brand_image = [];
            while ($ri = $stmt_image->fetch(PDO::FETCH_ASSOC)) {
                $banner = MediaUrl::fromImageTitle($ri['image'], [640]);
                $brand_image[] = isset($banner[640]) ? $banner[640] : '';
            }
            $images = MediaUrl::fromImageTitle($gift['brand']['logo'], [80]);
            $gift['brand']['logo'] = isset($images[80]) ? $images[80] : '';
            $gift['brand']['banner'] = $brand_image;
        }

        return $gift;
    }

    static function getBrandGiftSet($card = null)
    {
        if ($card == null) {
            return false;
        }
        $param = [];
        $param[] = 'a.code_quantity > 0';
        $param[] = 'a.status=1';
        $param[] = 'a.justGetOrder <> 2';
        $param[] = 'isUnfixPrice=' . IS_OFF;
        // gift
        $param[] = 'b.status in (1,2)';
        $id_gift_set = isset($card['gift_id']) && $card['gift_id'] > 0 ? $card['gift_id'] : 0;
        if ($id_gift_set > 0) {
            $getSiteGift = DB::query("SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE status=" . IS_ON . " and gift_id=" . $id_gift_set);
            if ($getSiteGift) {
                $id_site_gift = $getSiteGift->fetchAll(PDO::FETCH_ASSOC);
                $id_gift_detail = array_column($id_site_gift, 'gift_detail_id');
            }
            if (!empty($id_gift_detail)) {
                $param[] = " a.id IN (" . implode(',', $id_gift_detail) . ")";
            } else {
                $param[] = " a.id=0";
            }
        } else {

            $s = "SELECT * FROM " . T_GIFT_LIMIT . " WHERE status=" . IS_ON . " AND app_id =" . $card['app_id'];
            $stmt = DB::query($s);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $gift_gift = [];
            $gift_brand = [];
            foreach ($data as $item) {
                if ($item['type'] == 2) {
                    array_push($gift_gift, $item['gift_detail_id']);
                } else {
                    array_push($gift_brand, $item['brand_id']);
                }
            }
            if (count($gift_gift) > 0) {
                $param[] = "a.id NOT IN (" . implode(",", $gift_gift) . ")";
            }
            if (count($gift_brand) > 0) {
                $param[] = " a.brand_id NOT IN (" . implode(",", $gift_brand) . ")";
            }

        }


        $condition = FunctionLib::addCondition($param, true);
        $sql = "SELECT a.brand_id,a.id FROM " . T_GIFT_DETAIL . " a LEFT JOIN " . T_GIFT . " b on a.gift_id=b.id " . $condition;
        $re = DB::query($sql);
        return $re->fetchAll(PDO::FETCH_ASSOC);
    }

    static function allProvide($is_city = false)
    {
        $where = "";
        if ($is_city == true) {
            $where = " WHERE is_city=1 ";
        }
        $sql = "SELECT id,title from " . T_PROVINCE . " $where ORDER BY title asc";
        $stmt = DB::query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @throws Exception
     */
    static function myVoucher($card_id)
    {
        $day_limit = TIME_NOW + (0 * 24 * 60 * 60);

        $list_cart_details = self::_listCartDetailByCard($card_id, $day_limit);

        $brand_ids = array_column($list_cart_details, 'brand_id');
        $gift_ids = array_column($list_cart_details, 'gift_id');
        $list_gifts = self::listVoucherGift($gift_ids);
        $vouchers = self::_extractVoucherType($list_cart_details, $list_gifts);

        $active_vouchers = $vouchers['active_voucher'];
        $listBrand = self::_listVoucherBrand($brand_ids);
        foreach ($listBrand as &$brand) {
            $images = MediaUrl::fromImageTitle($brand['logo'], [160]);
            $brand['image'] = isset($images['160']) ? $images['160'] : "";
            $brand['voucher'] = self::_mapVoucherBrand($active_vouchers, $brand['id']);
        }
        return [
            'active_voucher' => $listBrand,
            'expired_voucher' => $vouchers['expired_voucher'],
            'sent_voucher' => $vouchers['sent_voucher']
        ];
    }

    private static function _extractVoucherType($vouchers, $gift)
    {
        $active_voucher = [];
        $sent_voucher = [];
        $expired_voucher = [];
        $listAvatar = array_column($vouchers, 'avatar');
        $genImage = [];
        $day_limit = TIME_NOW + (5 * 24 * 60 * 60);
        $listAvatar = array_unique($listAvatar);
        foreach ($listAvatar as $item) {
            $images = MediaUrl::fromImageTitle($item, [160]);
            $genImage[$item] = isset($images['160']) ? $images['160'] : "";
        }
        foreach ($vouchers as $voucher) {
            $voucher['gift'] = isset($gift[$voucher['gift_id']]) ? $gift[$voucher['gift_id']] : [
                "id" => 0,
                "name" => "",
                "gift_id" => "",
                "brand_id" => ",",
                "url" => ''
            ];
            $voucher['image'] = $genImage[$voucher['avatar']];
            $voucher['receive_code'] = System::decrypt($voucher['receive_code']);
            $expired = $voucher['expired'];
            if ($expired > 0 && $expired <= $day_limit && $voucher['receiver_id'] == 0) {
                $expired_voucher[] = $voucher;
            } elseif ($voucher['receiver_id'] < 0) {
                $sent_voucher[] = $voucher;
            } else {
                $active_voucher[] = $voucher;
            }
        }
        return [
            'active_voucher' => $active_voucher,
            'expired_voucher' => $expired_voucher,
            'sent_voucher' => $sent_voucher,
        ];
    }

    private static function _listCartDetailByCard($card_id, $day_limit, $limit = 0)
    {
        $cart_details = self::_getCartDetailMyVoucher($card_id, $limit);
        if (empty($cart_details)) return [];
        $cart_detail_ids = array_column($cart_details, 'id');
        $gift_codes = self::_getCodeMyVoucher($cart_detail_ids, $day_limit);
        if (empty($gift_codes)) return [];

        $gift_detail_ids = array_column($gift_codes, 'gift_detail_id');
        $gift_details = self::_getGiftDetailMyVoucher($gift_detail_ids);

        return self::_mapCartDetailWithCode($gift_codes, $cart_details, $gift_details);
    }

    private static function _getCartDetailMyVoucher($card_id, $limit = 0)
    {
        $cond_cart = [];
        $cond_cart[] = "card_id =$card_id";
        $cond_cart[] = "delivery in(1,2,9)";
        $condition_cart = FunctionLib::addCondition($cond_cart);
        if ($limit == 0 || $limit > 500) $cart_limit = 500;
        else $cart_limit = $limit;
        $sql_cart_detail = vsprintf(
            "SELECT id,receiver_id,receive_code,created,price,status,pay_status FROM %s WHERE %s ORDER BY id desc LIMIT %s",
            [
                T_CART_DETAIL,
                $condition_cart,
                $cart_limit
            ]);
        $cart_details = DB::query($sql_cart_detail)->fetchAll(PDO::FETCH_ASSOC);

        $valid_cart_details = [];
        foreach ($cart_details as $cart_detail) {
            if ($cart_detail['status'] == IS_ON && $cart_detail['pay_status'] == IS_ON) {
                $valid_cart_details[$cart_detail['id']] = $cart_detail;
            }
        }
        return $valid_cart_details;
    }

    private static function _getCodeMyVoucher($cart_detail_ids, $day_limit)
    {
        $str_cart_detail_id = implode(',', $cart_detail_ids);
        $cond_code = [];
        $cond_code[] = "cart_detail_id in($str_cart_detail_id)";
        $cond_code[] = "status = 1";
        $cond_code[] = "(expired = 0 OR expired >= $day_limit)";
        $condition_code = FunctionLib::addCondition($cond_code);
        $sql_gift_code = vsprintf("SELECT expired,cart_detail_id,gift_detail_id,gift_id,brand_id FROM %s WHERE  %s ORDER BY cart_detail_id DESC",
            [
                T_GIFT_CODE,
                $condition_code
            ]
        );
        return DB::query($sql_gift_code)->fetchAll(PDO::FETCH_ASSOC);
    }

    private static function _getGiftDetailMyVoucher($gift_detail_ids)
    {
        $gift_detail_ids = array_unique($gift_detail_ids);
        $str_gift_detail_id = implode(',', $gift_detail_ids);
        $cond_gift = [];
        $cond_gift[] = "id in($str_gift_detail_id)";
        $condition_gift = FunctionLib::addCondition($cond_gift);
        $sql_gift_details = vsprintf("SELECT id,title,type,avatar,promotion_code as code FROM %s WHERE %s", [
            T_GIFT_DETAIL,
            $condition_gift
        ]);
        return DB::query($sql_gift_details)->fetchAll(PDO::FETCH_ASSOC);
    }

    private static function _mapCartDetailWithCode($gift_codes, $cart_details, $gift_details)
    {
        $carts = [];
        foreach ($gift_codes as $gift_code) {
            $cart_detail = $cart_details[$gift_code['cart_detail_id']];
            $gift_detail_id = $gift_code['gift_detail_id'];
            $gift_detail_key = array_search($gift_detail_id, array_column($gift_details, 'id'));
            $gift_detail = $gift_details[$gift_detail_key];
            if($gift_detail['code'] == '') unset($gift_detail['code']);
            $carts[$gift_code['cart_detail_id']] = $gift_code + $cart_detail + $gift_detail;
        }
        return $carts;
    }

    private static function _listVoucherBrand($brand_ids)
    {
        if (empty($brand_ids)) return [];
        $brand_id = implode(',', $brand_ids);
        return DB::query("SELECT id,title,logo FROM " . T_BRAND . " WHERE id in($brand_id) order by FIELD(id,$brand_id)")->fetchAll(PDO::FETCH_ASSOC);
    }

    private static function listVoucherGift($gift_ids)
    {
        if (empty($gift_ids)) return [];
        $gift_id = implode(',', $gift_ids);
        return DB::query("SELECT a.id,a.name,gift_id,brand_id,b.url FROM " . T_GIFT . " a 
                                LEFT JOIN " . T_POINT_UNIT . " b on a.pointu_id=b.id
        WHERE a.id in($gift_id)")->fetchAll(PDO::FETCH_ASSOC);
    }

    private static function _mapVoucherBrand($vouchers, $brand_id)
    {
        $voucher = array_filter($vouchers, function ($voucher) use ($brand_id) {
            return $voucher['brand_id'] == $brand_id;
        });
        return self::_mapVoucherGift($voucher);
    }

    private static function _mapVoucherGift($vouchers)
    {
        $mapped = [];
        foreach ($vouchers as $voucher) {
            $key = $voucher['type'] . "_" . $voucher['price'];
            $mapped[$key][] = $voucher;
        }
        return $mapped;
    }

    //SỬ dụng ở trang home
    static function allVoucher($card_id = null)
    {
        $card_id = (int)$card_id;
        if ($card_id == "" || $card_id <= 0) return [];
        $day_limit = TIME_NOW;
        $list_cart_details = self::_listCartDetailByCard($card_id, $day_limit, 5);

        if (empty($list_cart_details)) return [];
        $new_carts = [];
        foreach ($list_cart_details as $list_cart_detail) {
            if (isset($list_cart_detail['avatar'])) {
                $avt = MediaUrl::fromImageTitle($list_cart_detail['avatar'], [320]);
                $list_cart_detail['banner'] = isset($avt['320']) ? $avt['320'] : "";
            }
            $list_cart_detail['receive_code'] = System::decrypt($list_cart_detail['receive_code']);
            $new_carts[] = $list_cart_detail;
        }

        return $new_carts;
    }

    static function expiredVoucher($card_id)
    {
        $card_id = (int)$card_id;
        if ($card_id == "" || $card_id <= 0) return [];
        $array = [];
        $day_limit = time() - (5 * 24 * 60 * 60);
        $rec = DB::query("SELECT id FROM " . T_CART . " WHERE status=" . IS_ON . " AND pay_status=" . IS_YES . " AND card_id=" . $card_id);
        $carts_id = [];
        if ($rec) {
            $carts_id = $rec->fetchAll(PDO::FETCH_COLUMN);
        }
        if (!empty($carts_id)) {
            $carts_id = array_unique($carts_id);
            $arr_id_gift_detail = [];
            $recd = DB::query("SELECT distinct (gift_detail_id) as gift_detail_id FROM " . T_CART_DETAIL . " WHERE cart_id IN(" . implode(',', $carts_id) . ") AND  pay_status=" . IS_YES . " AND status=" . IS_ON . " ORDER BY id DESC");
            if ($recd) {
                $arr_id_gift_detail = $recd->fetchAll(PDO::FETCH_COLUMN);
            }
            $arr_id_gift_detail = array_unique($arr_id_gift_detail);
            if (empty($arr_id_gift_detail)) {
                $arr_id_gift_detail = [0];
            }
            $today = time();
            $sql = "SELECT a.id,a.title,b.title as gift_name,b.gift_id,b.avatar ,a.logo, c.id as cart_detail_id,expired,d.id as code_id,b.promotion_code
                        FROM " . T_BRAND . " a
                        LEFT JOIN " . T_GIFT_DETAIL . " b on a.id=b.brand_id
                        LEFT JOIN " . T_CART_DETAIL . " c on b.id=c.gift_detail_id
                        LEFT JOIN " . T_GIFT_CODE . " d on c.id=d.cart_detail_id
                        WHERE b.id IN (" . implode(',', $arr_id_gift_detail) . ")
                            AND c.cart_id IN (" . implode(',', $carts_id) . ") AND c.status = 2
                            AND (c.delivery = 1 OR c.delivery=2)
                            AND (d.expired > 0 AND d.expired between $day_limit AND $today)
                            and d.status = 1
                            order by expired desc";
            $stmt = DB::query($sql);
            $brands = [];
            while ($item = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($item['logo'], [160]);
                $item['image'] = isset($images['160']) ? $images['160'] : "";
                if (isset($item['avatar'])) {
                    $avt = MediaUrl::fromImageTitle($item['avatar'], [320]);
                    $item['avatar'] = isset($avt['320']) ? $avt['320'] : "";
                }
                if (Language::$activeLang != "vi") {
                    $content = Gift::getContent(Language::$activeLang, $item['gift_id']);
                    if (isset($content['title']) && $content['title'] != '') {
                        $item['gift_name'] = $content['title'];
                    }
                }
                $brands[] = $item;
            }
            $array['brand'] = $brands;
        }
        return $array;
    }

    //chi tiêt voucher
    static function detailVoucher($card, $options = [])
    {
        $brand_id = $options['brand_id'];
        $cart_detail_id = $options['id'];
        $price = $options['price'];
        $type = $options['type'];

        if (empty($card)) return [];
        $array = [];
        $rec = DB::query("SELECT id FROM " . T_CART . " WHERE status=" . IS_ON . " AND pay_status=" . IS_YES . " AND card_id=" . $card['id']);
        $carts_id = [];
        if ($rec) {
            $carts_id = $rec->fetchAll(PDO::FETCH_COLUMN);
        }
        if (empty($carts_id)) {
            $array['brand'] = [];
            return $array;
        }
//        $condition[] = "c.receiver_id = 0";
        $condition[] = "(b.expired>=" . TIME_NOW . " OR b.expired=0 OR b.expired is null)";
        $condition[] = "c.status=" . IS_ON;
        $condition[] = "c.cart_id in (" . implode(',', $carts_id) . ")";
        $condition[] = "b.status = 1";
        $condition[] = "c.delivery <> 3";
        if ($brand_id > 0) {
            $condition[] = "c.brand_id=$brand_id";
        }
        if ($cart_detail_id > 0) {
            $condition[] = "c.id=$cart_detail_id";
        }
        if ($price > 0) {
            $condition[] = "c.price='$price'";
        }
        if ($type > 0) {
            $condition[] = "a.type=$type";
        }
        $condition = FunctionLib::addCondition($condition);
        $sql = "SELECT a.id,a.title,a.avatar,a.type,b.expired,c.created,b.code,a.price,d.code_type,d.type,d.showbarcode,c.delivery,c.receive_code,d.article_id,receive_code,b.status as code_status,c.id as cart_detail_id,b.id as code_id,c.gift_id,e.identity,d.needIdentity,d.needPhone,b.used,b.valid_time,b.pin,b.serial,a.brand_id,a.promotion_code
                FROM " . T_GIFT_DETAIL . " a
                LEFT JOIN " . T_CART_DETAIL . " c on c.gift_detail_id=a.id
                LEFT JOIN " . T_GIFT_CODE . " b on c.id=b.cart_detail_id
                LEFT JOIN " . T_GIFT . " d on d.id=a.gift_id
                LEFT JOIN " . T_CUSTOMER . " e on e.id=c.customer_id
                WHERE $condition order by c.id desc";
        $stmt = DB::query($sql);
        if ($stmt) {
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (($row['code'] == null || $row['code'] == "") && $row['delivery'] == 1) {
                    self::assignCode($row['cart_detail_id']);
                }
                if ($row['used'] == 0 && $row['code_id'] > 0) {
                    DB::update(T_GIFT_CODE, ['last_time' => TIME_NOW], "id='{$row['code_id']}'");
                }
                if($row['promotion_code'] == '') {
                    $row['code'] = System::decrypt($row['code'], CRYPT_KEY);
                }else{
                    $row['code'] = $row['promotion_code'];
                }
                if (Language::$activeLang != "vi") {
                    $content = Gift::getContent(Language::$activeLang, $row['gift_id']);
                    $row['title'] = $content['title'];
                }
                $images = MediaUrl::fromImageTitle($row['avatar'], [640]);
                $row['image'] = isset($images[640]) ? $images[640] : '';

                $row['link'] = LINK_URBOX . 'nhan-qua/' . System::decrypt($row['receive_code']) . '.html';
                $row['receive_code'] = System::decrypt($row['receive_code']);
                $row['qrCode'] = $row['code'];
                if ($row['showbarcode'] == 5) {
                    $extractSerial = explode(' PIN:', $row['serial']);
                    $row['serial'] = $extractSerial[0];
                    $row['pin'] = isset($extractSerial[1]) ? $extractSerial[1] : "";
                }


                $array['gifts'][$row['cart_detail_id']] = $row;
            }
            $item = [];
            if (!empty($array['gifts'])) {
                $item = current($array['gifts']);
            }
            if (!empty($item)) {
                $content = Gift::getContent(Language::$activeLang, $item['gift_id']);
                if ($content['title'] != "") {
                    $gift['title'] = $content['title'];
                }
                $array['note'] = html_entity_decode($content['note']);
                $brand = DB::fetch("SELECT id,title,online from " . T_BRAND . " WHERE id={$item['brand_id']}");
                $array['brand'] = $brand;
            } else {
                $array['brand'] = [];
                $array['note'] = "";
            }

        }
        return $array;
    }

    static function usedVoucher($card_id)
    {
        $card_id = (int)$card_id;
        $time_expired_num = time() - (3 * 24 * 60 * 60);
        $cart_details = DB::query("SELECT id,gift_id,gift_detail_id,receive_code,status,delivery FROM " . T_CART_DETAIL . " WHERE card_id='$card_id' and using_time > $time_expired_num")->fetchAll(PDO::FETCH_ASSOC);
        if (empty($cart_details)) return [];
        $gifts_detail_ids = array_column($cart_details, 'gift_detail_id');
        $gifts_ids = array_column($cart_details, 'gift_id');
        $gifts_details = self::_getGiftDetailHistory($gifts_detail_ids, $gifts_ids);

        foreach ($cart_details as &$cart_detail) {
            if ($cart_detail['status'] == IS_ON && $cart_detail['delivery'] == 3) {
                $cart_detail['link'] = LINK_URBOX . 'nhan-qua/' . System::decrypt($cart_detail['receive_code']) . '.html';
                if (isset($gifts_details[$cart_detail['gift_detail_id']])) {
                    $gift = $gifts_details[$cart_detail['gift_detail_id']];
                    $images = MediaUrl::fromImageTitle($gift['avatar'], [160]);
                    $cart_detail['image'] = isset($images[160]) ? $images[160] : '';
                    $cart_detail['title'] = $gift['title'];
                }
            }
        }
        return $cart_details;
    }

    static function updateCart($token, $id)
    {
        $id = (int)$id;
        if ($id == "" || $id <= 0) return [];
        $card = [];
        if ($token != null) {
            $card = Card::getByToken($token);
        }
        if (empty($card)) {
            return false;
        }
        $s = "SELECT a.id,b.id as cart_id,b.card_id from " . T_CART_DETAIL . " a LEFT JOIN " . T_CART . " b on a.cart_id = b.id where a.id={$id} LIMIT 1";
        $stmt = DB::fetch($s);
        if (!empty($stmt) && $stmt['card_id'] > 0 && $stmt['card_id'] == $card['id']) {
            $gift_code = DB::fetch("SELECT id from " . T_GIFT_CODE . " where cart_detail_id={$stmt['id']} and status=1 LIMIT 1");
            if (!empty($gift_code) && $gift_code['id'] > 0) {
                $newRow = array('active' => 2, 'used' => TIME_NOW, 'apiCall' => 7);
                $code = DB::update(T_GIFT_CODE, $newRow, "id={$gift_code['id']}");
                $newCartRow = array(
                    'using_time' => TIME_NOW,
                    'delivery' => 3,
                );
                $cart = DB::update(T_CART_DETAIL, $newCartRow, " id =" . $id);
                if ($code && $cart) {
                    return true;
                }
            }
        }
        return false;
    }

    static function assignCode($cart_detail_id)
    {
        $cart_detail_id = (int)$cart_detail_id;
        if ($cart_detail_id > 0) {
            $dataPost['idCartDetail'] = $cart_detail_id;
            $linkApi = LINK_URBOX_INTERNAL . 'ajax.php?act=api&code=receivingGift';
            $curl = new CURL();
            $curl->post($linkApi, $dataPost);
        }
    }

    // trả về id các brand gần đây => lấy quà thuộc các thương hiệu này. => Tìm dc quà gần đây
    static function getNearBrand($cat = 0)
    {
        $my_loc = self::getCurrentLoc();
        if (count($my_loc) == 2) {
            $mylat = (float)$my_loc[0];
            $mylon = (float)$my_loc[1];
        } else {
            $mylat = 21.022736;
            $mylon = 105.8019441;
        }
        $lon1 = $mylon - USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
        $lon2 = $mylon + USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
        $lat1 = $mylat - (USER_DISTANCE / 69);
        $lat2 = $mylat + (USER_DISTANCE / 69);
        $condition = [
            "a.status=" . IS_ON,
            "b.status= " . IS_ON,
            "(
					(a.longitude BETWEEN $lon1 AND $lon2 AND a.latitude BETWEEN $lat1 AND $lat2)
					OR online=" . IS_YES . "
				 )"
        ];
        $cat = (int)$cat;
        if ($cat != null && $cat > 0) {
            $cat_ids = Balance::getCatId($cat);
            if (count($cat_ids) > 0) {
                $condition[] = "  b.cat_id IN (" . implode(",", $cat_ids) . ") ";
            }
        }
        $where = FunctionLib::addCondition($condition);
        $sql = "SELECT  b.id,address,address_en,isApply
            FROM " . T_BRAND_OFFICE . " a
            LEFT JOIN " . T_BRAND . " b ON a.brand_id = b.id
            WHERE $where
            GROUP BY b.id HAVING b.id >0";

        $stmt = DB::query($sql);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($data)) {
            $data = array_column($data, 'id');
        }
        return $data;
    }

    function deg2rad($deg)
    {
        return $deg * (M_PI / 180);
    }

    static function getAccessLoc()
    {
        $allow = CookieLib::get('C_ALLOW_LOCATION');
        return (int)$allow;
    }


    static function listBrands($card, $params = [])
    {
        if (empty($card) || !is_array($card)) return [];
        $access_loc = self::getAccessLoc();
        $my_loc = self::getCurrentLoc();
        $brand = self::getBrandGiftSet($card);
        $brands = array_column($brand, 'brand_id');
        $brands = array_unique($brands);
        // nếu cho phép truy cập vi trí
        if ($access_loc == 1 && count($my_loc) == 2) {
            $dataOnline = [];
            if (isset($params['getOnline'])) {
                $dataOnline = self::getBrandOnline($brands);
            }
            $dataOffline = self::allBrandByLoc($brands, $params);
            $data = array_merge($dataOnline, $dataOffline);
        } else {
            $data = self::allBrand($brands, $params);
        }
        // remake image brand
        foreach ($data as $k => &$item) {
            if (isset($data[$k - 1]) && $data[$k - 1]['id'] == $item['id']) {
                $item['logo'] = $data[$k - 1]['logo'];
            } else {
                $images = MediaUrl::fromImageTitle($item['logo'], [160]);
                $item['logo'] = isset($images[160]) ? $images[160] : '';
                //$banner = MediaUrl::fromImageTitle($item['image'], [640]);
                //$item['banner'] = isset($banner[640]) ? $banner[640] : '';
                $item['banner'] = '';
            }
            if (Language::$activeLang != "vi") {
                $item['address'] = $item['address_en'];
            }
        }

        return $data;
    }

    private static function getBrandFromOffice($brands)
    {
        if (empty($brands)) return [];
        $where = [];
        $where[] = "status=" . IS_ON;
        $brand_id = implode(",", $brands);
        $where[] = " brand_id IN($brand_id) ";
        $city_id = CookieLib::get("C_MY_CITY");
        $city_id = (int)$city_id;
        if ($city_id == -1) {
            // tinhr thanh khac
            $provide = self::allProvide(true);
            $provide_id = array_column($provide, 'id');
            if (count($provide_id) > 0) {
                $where[] = " city_id NOT IN(" . implode(",", $provide_id) . ")";
            }
        } elseif ($city_id != 0) {
            $where[] = "city_id={$city_id}";
        }
        $where = FunctionLib::addCondition($where);
        return DB::query("SELECT distinct(brand_id) FROM " . T_BRAND_OFFICE . " WHERE $where")->fetchAll(PDO::FETCH_COLUMN);

    }

    /**
     * @function trả về danh sách brand khi người dùng ko cho phép truy cập vị trí
     * @param $brands array Lấy brand id
     * @param $params
     * @return array
     * */

    //
    static function allBrand($brands = [], $params = [])
    {
        $paging = $params['paging'];
        $cat = $params['cat'];
        $row_per_page = $params['row_per_page'];
        $page_no = $params['page_no'];
        $keyword = $params['keyword'];
        $order = $params['order'];
        $brand_id = self::getBrandFromOffice($brands);
        $where = [];
        $where[] = "status=" . IS_ON;
        if ($cat != null && $cat > 0) {
            $cat_ids = Balance::getCatId($cat);
            if (count($cat_ids) > 0) {
                $where[] = " cat_id IN (" . implode(",", $cat_ids) . ") ";
            }
        }
        if ($keyword != "") {
            $where[] = " title LIKE '%{$keyword}%'  ";
        }
        if (!empty($brand_id)) {
            $brand_ids = implode(',', $brand_id);
            $brand_diff = array_diff($brands, $brand_id);
            $brand_id_diff = implode(',', $brands);
            if (empty($brand_id_diff)) {
                $where[] = "id in($brand_ids)";
            } else {
                $where[] = "((online =1 and id in($brand_ids)) or (online =2 and id in($brand_id_diff)))";
            }
        } else {
            return [];
        }
        $where = FunctionLib::addCondition($where);
        if ($order == 'new') {
            $orderBy = "id DESC";
        } else {
            $orderBy = "is_hot DESC";
        }
        $sql = "SELECT id,title,logo,'' as address,'' as address_en,id as brand_id,2 as isApply,is_hot 
                FROM " . T_BRAND . " b
                WHERE $where ORDER BY sort=0,sort asc, $orderBy , title ASC";
        if ($paging == true) {
            $data = array();
            $re = Pagging::query($sql, $row_per_page, $page_no);
            if ($re && Pagging::$totalPage >= $page_no) {
                while ($brand = $re->fetch(PDO::FETCH_ASSOC)) {
                    $data[] = $brand;
                }
            }
        } else {
            $stmt = DB::query($sql);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        return $data;
    }

    static function getBrandOnline($brand_ids)
    {
        if (empty($brand_ids)) {
            return [];
        }
        $brand_id = implode(",", $brand_ids);
        $where[] = "  id IN($brand_id)";
        $where[] = "online=2 AND STATUS = 2";
        $where = FunctionLib::addCondition($where);
        return DB::query("SELECT id,title,online,logo FROM " . T_BRAND . " WHERE $where ORDER BY is_hot desc,id desc")->fetchAll(PDO::FETCH_ASSOC);
    }

    /*Lấy danh sách brand theo vị trí người dùng, nếu bật định vị*/
    static function allBrandByLoc($brands = [], $params = [])
    {
        $paging = $params['paging'];
        $cat = $params['cat'];
        $row_per_page = $params['row_per_page'];
        $page_no = $params['page_no'];
        $group_by = $params['group_by'];
        $keyword = $params['keyword'];


        $my_loc = self::getCurrentLoc();
        $mylat = (float)$my_loc[0];
        $mylon = (float)$my_loc[1];
        $lon1 = $mylon - USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
        $lon2 = $mylon + USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
        $lat1 = $mylat - (USER_DISTANCE / 69);
        $lat2 = $mylat + (USER_DISTANCE / 69);
        $where = [];
        $where[] = "b.online=1";
        if (!empty($brands)) {
            $brand_id = implode(",", $brands);
            $where[] = "  a.brand_id IN($brand_id)";
        }
        if ($keyword != "") {
            $where[] = "  b.title LIKE '%{$keyword}%'";
        }
        if ($cat != null && $cat > 0) {
            $cat_ids = Balance::getCatId($cat);
            if (count($cat_ids) > 0) {
                $where[] = "  b.cat_id IN (" . implode(",", $cat_ids) . ") ";
            }
        }
        $where = FunctionLib::addCondition($where);
        $expression = "(6371 * acos( cos( radians($mylat) ) * cos( radians( a.latitude ) ) * cos( radians( a.longitude ) - radians($mylon) ) + sin( radians($mylat) ) * sin( radians( a.latitude ) ) ) )";
        if ($group_by == true) {
            $gr = " GROUP BY a.brand_id ";
        } else {
            $gr = "";
        }
        $sql = "SELECT  b.online,b.id,a.address,address_en,b.title,b.logo,isApply,is_hot,round($expression,1) as distance
            FROM " . T_BRAND_OFFICE . " a
            LEFT JOIN " . T_BRAND . " b ON a.brand_id = b.id
            WHERE
             	a.status=" . IS_ON . " AND b.status= " . IS_ON . " AND
				$where
				AND (
					(a.longitude BETWEEN $lon1 AND $lon2 AND a.latitude BETWEEN $lat1 AND $lat2)
				 	OR online=" . IS_YES . "
				 )
				$gr HAVING b.id >0
            ORDER BY $expression asc";
        if ($paging == true) {
            $re = PagingGB::query($sql, $row_per_page, $page_no, 'a.brand_id,b.id');
            if ($re && PagingGB::$totalPage >= $page_no) {
                $data = $re->fetchAll(PDO::FETCH_ASSOC);
            } else {
                $data = [];
            }
        } else {
            $stmt = DB::query($sql);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        return $data;
    }


    static function setGiftToCache($id)
    {
        $gift_json = CookieLib::get('viewed_gifts');
        $gifts = [];
        if (!empty($gift_json) && $gift_json != "null") {
            $gifts = json_decode($gift_json, true);
        }
        if (!in_array($id, $gifts)) {
            array_push($gifts, $id);
            if (count($gifts) > 10) array_shift($gifts);
        }
        $expire = time() + (86400 * 7);
        CookieLib::set('viewed_gifts', json_encode($gifts), $expire);
    }

    static function removeGiftCache($id)
    {
        $gift_json = CookieLib::get('viewed_gifts');
        $gift_ids = [];
        if (!empty($gift_json)) {
            $gift_ids = json_decode($gift_json, true);
        }
        $index = array_search($id, $gift_ids);
        unset($gift_ids[$index]);
        $expire = time() + (86400 * 7);
        CookieLib::set('viewed_gifts', json_encode($gift_ids), $expire);
    }

    static function getGiftFromCache($isPhysic = 0, $length = 0)
    {
        $gift_json = CookieLib::get('viewed_gifts');
        if (empty($gift_json)) {
            return null;
        }
        $gift_id = json_decode($gift_json, true);
        $gift_id = array_unique($gift_id);
        if (empty($gift_id)) {
            return null;
        }
        foreach ($gift_id as &$item) {
            $item = (int)$item;
        }
        $gift_id = implode(",", $gift_id);
        $param = array();
        $param[] = 'code_quantity>0';
        $param[] = 'a.status=1';
        $param[] = 'a.justGetOrder <> 2';
        $param[] = 'a.isUnfixPrice=' . IS_OFF;
        $param[] = "a.id in ($gift_id)";

        if ($isPhysic == 2) {
            $param[] = 'a.isPhysical=2';
        }
        if ($isPhysic == 1) {
            $param[] = 'a.isPhysical=1';
        }

        $condition = FunctionLib::addCondition($param, true);

        $sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as catagory,a.isPhysical,a.avatar as image
        FROM " . T_GIFT_DETAIL . " a
        LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
        LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
        $condition ORDER BY FIELD(a.id," . $gift_id . ")";

        $re = DB::query($sql);
        $data = array();
        if ($re) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['logo'], [160]);
                $banner = MediaUrl::fromImageTitle($row['image'], [640]);
                $temp = array(
                    'title' => $length > 0 ? StringLib::truncateHtml($row['title'], $length) : $row['title'],
                    'image' => isset($images[160]) ? $images[160] : '',
                    'banner' => isset($banner[640]) ? $banner[640] : ''
                );
                $content = Gift::getContent(Language::$activeLang, $row['gift_id']);
                if ($content['title'] != "") {
                    $temp['title'] = $content['title'];
                }
                $temp['content'] = $content;
                $data[$row['id']] = $temp + $row;
            }
            return $data;
        }
    }

    static function setBrandToCache($id)
    {
        $brand_json = CookieLib::get('viewed_brand');
        $brands = [];
        if (!empty($brand_json) && $brand_json != "null") {
            $brands = json_decode($brand_json, true);
        }
        if (!in_array($id, $brands)) {
            array_push($brands, $id);
            if (count($brands) > 10) array_shift($brands);
        }
        $expire = time() + (86400 * 7);
        CookieLib::set('viewed_brand', json_encode($brands), $expire);
    }

    static function removeBrandCache($id)
    {
        $brand_json = CookieLib::get('viewed_brand');
        $brand_ids = [];
        if (!empty($brand_json)) {
            $brand_ids = json_decode($brand_json, true);
        }
        $index = array_search($id, $brand_ids);
        unset($brand_ids[$index]);
        $expire = time() + (86400 * 7);
        CookieLib::set('viewed_brand', json_encode($brand_ids), $expire);
    }

    static function getBrandFromCache($length = 0)
    {
        $brand_json = CookieLib::get('viewed_brand');
        if (empty($brand_json)) {
            return null;
        }
        $brand_id = json_decode($brand_json, true);
        $brand_id = array_unique($brand_id);
        if (empty($brand_id)) {
            return null;
        }
        foreach ($brand_id as &$item) {
            $item = (int)$item;
        }
        $brand_id = implode(",", $brand_id);
        $param = [];
        $param[] = 'a.status=' . IS_ON;
        $param[] = "a.id in ($brand_id)";


        $condition = FunctionLib::addCondition($param);

        $sql = "SELECT a.id,a.title,a.logo,a.is_hot,b.title as category FROM " . T_BRAND . " a LEFT JOIN " . T_CATEGORY . " b on a.cat_id = b.id
					WHERE $condition LIMIT 9
			";

        $re = DB::query($sql);
        $data = array();
        if ($re) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['logo'], [320]);
                $temp = array(
                    'title' => $length > 0 ? StringLib::truncateHtml($row['title'], $length) : $row['title'],
                    'logo' => isset($images[320]) ? $images[320] : '',
                );
                $data[$row['id']] = $temp + $row;
            }
            return $data;
        }
    }

    static function changePrice($array, $option)
    {
        $id = isset($option['id']) ? $option['id'] : 0;
        $price = isset($option['price']) ? $option['price'] : 0;
        $rate = isset($option['rate']) && $option['rate'] != 0 ? $option['rate'] : 1;
        $app_id = $option['app_id'];
        $cond = [];
        $cond[] = "app_id={$app_id}";
        $cond[] = "status=" . IS_ON;


        $conditions = FunctionLib::addCondition($cond);
        if (empty($array)) {
            $conditions .= " AND gift_detail_id ={$id}";
            $newPoint = DB::fetch("SELECT point,price FROM " . T_GIFT_PRICE . " WHERE  $conditions");
            $data['price_point'] = $price / $rate;
            $data['price'] = $price;
            if (!empty($newPoint)) {
                $data['price_point'] = $newPoint['point'];
                $data['price'] = $newPoint['price'];
            }
        } else {
            $gift_id = array_keys($array);
            $gift_id[] = 0;
            $string_id = implode(',', $gift_id);
            $conditions .= " AND gift_detail_id IN ({$string_id})";
            $sql = DB::query("SELECT point,price,gift_detail_id FROM " . T_GIFT_PRICE . " WHERE  $conditions");
            while ($r = $sql->fetch(PDO::FETCH_ASSOC)) {
                if (isset($array[$r['gift_detail_id']])) {
                    $array[$r['gift_detail_id']]['price_point'] = $r['point'];
                    $array[$r['gift_detail_id']]['price'] = $r['price'];
                }
            }
            $data = $array;
        }
        return $data;

    }

    /**
     * @throws Exception
     */
    static function history($card)
    {
        if (empty($card) || !is_array($card)) return [];
        $card_id = $card['id'];
        $card_transactions = DB::query("SELECT id as transaction_id,note,type,cart_id,money FROM  " . T_CARD_TRANSACTION . " WHERE card_id='$card_id' and status=2 order by id DESC limit 500")->fetchAll(PDO::FETCH_ASSOC);
        if (empty($card_transactions)) return [];
        $cart_ids = array_column($card_transactions, 'cart_id');
        $cart_ids = array_filter($cart_ids);
        $carts = [];
        $cart_details = [];
        $delivery_details = [];
        $gift_details = [];
        if (!empty($cart_ids)) {
            $list_cart_ids = implode(',', $cart_ids);
            $carts = DB::query("SELECT id as cart_number,pay_status,created FROM " . T_CART . " WHERE id in($list_cart_ids) and status=2 and pay_status=2")->fetchAll(PDO::FETCH_ASSOC);
            if (!empty($carts)) {
                $carts = self::_mapKeyForList($carts, 'cart_number');
                $cart_details = DB::query("SELECT cart_id,gift_detail_id,gift_id,count(cart_id) as qty FROM " . T_CART_DETAIL . " WHERE cart_id in($list_cart_ids) and status=2 and pay_status=2 group by cart_id")->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($cart_details)) {
                    $cart_details = self::_mapKeyForList($cart_details, "cart_id");

                    $gift_detail_ids = array_column($cart_details, 'gift_detail_id');
                    $gift_ids = array_column($cart_details, 'gift_id');

                    $gift_details = self::_getGiftDetailHistory($gift_detail_ids, $gift_ids);
                    $delivery_details = DB::query("SELECT * FROM (SELECT cart_id,process FROM " . T_DELIVERY_DETAIL . " WHERE cart_id in($list_cart_ids) order by id desc) as temp_tbl group by cart_id")->fetchAll(PDO::FETCH_ASSOC);
                    $delivery_details = self::_mapKeyForList($delivery_details, "cart_id");
                }
            }
        }
        $empty_cart = [
            "cart_number" => 0,
            "pay_status" => 1,
            "created" => 0
        ];
        $empty_cart_detail = [
            "cart_id" => 0,
            "gift_detail_id" => 1,
            "gift_id" => 0,
            "qty" => 0
        ];
        $empty_gift_detail = [
            "id" => 0,
            "title" => "",
            "isPhysical" => 1
        ];

        foreach ($card_transactions as &$card_transaction) {
            if (isset($carts[$card_transaction['cart_id']])) {
                $card_transaction += $carts[$card_transaction['cart_id']];
                unset($carts[$card_transaction['cart_id']]);
            } else {
                $card_transaction += $empty_cart;
            }

            if (isset($cart_details[$card_transaction['cart_id']])) {
                $card_transaction += $cart_details[$card_transaction['cart_id']];
                unset($cart_details[$card_transaction['cart_id']]);
            } else {
                $card_transaction += $empty_cart_detail;
            }

            if (isset($gift_details[$card_transaction['gift_detail_id']])) {
                $card_transaction += $gift_details[$card_transaction['gift_detail_id']];
            } else {
                $card_transaction += $empty_gift_detail;
            }
            if (isset($delivery_details[$card_transaction['cart_id']])) {
                $card_transaction += $delivery_details[$card_transaction['cart_id']];
                unset($delivery_details[$card_transaction['cart_id']]);
            } else {
                $card_transaction += ['process' => 1];
            }
        }
        return $card_transactions;
    }

    private static function _getGiftDetailHistory($gift_detail_ids, $gift_ids)
    {
        $gift_detail_ids = array_unique($gift_detail_ids);
        $gift_ids = array_unique($gift_ids);
        $list_gift_detail_ids = implode(',', $gift_detail_ids);
        $gift_details = DB::query("SELECT title,id,isPhysical,gift_id,avatar FROM " . T_GIFT_DETAIL . " WHERE id in($list_gift_detail_ids)")->fetchAll(PDO::FETCH_ASSOC);
        $gift_contents = [];
        if (Language::$activeLang != "vi") {
            $list_gift_ids = implode(',', $gift_ids);
            $gift_contents = DB::query("SELECT gift_id,title FROM " . T_GIFT_CONTENT . " WHERE gift_id in($list_gift_ids) and status = 2 and lang='" . Language::$activeLang . "'")->fetchAll(PDO::FETCH_ASSOC);
            $gift_contents = self::_mapKeyForList($gift_contents, 'gift_id');

        }

        $new_gift_details = [];
        foreach ($gift_details as $gift_detail) {
            if (isset($gift_contents[$gift_detail['gift_id']])) {
                $gift_content = $gift_contents[$gift_detail['gift_id']];
                $gift_detail['title'] = isset($gift_content['title']) && !empty($gift_content['title']) ? $gift_content['title'] : $gift_detail['title'];
            }
            unset($gift_detail['gift_id']);
            $new_gift_details[$gift_detail['id']] = $gift_detail;
        }
        return $new_gift_details;
    }

    private static function _mapKeyForList($arrays, $keyField = 'id')
    {
        $newArray = [];
        foreach ($arrays as $array) {
            $newArray[$array[$keyField]] = $array;
        }
        return $newArray;
    }


//    survey
    static function getAnswer($card_id)
    {
        $card_id = (int)$card_id;
        if ($card_id <= 0) return [];
        $stmt_cus_survey = DB::query("SELECT survey_id  FROM " . T_SURVEY_RESULT . " WHERE card_id = '{$card_id}' GROUP BY survey_id ");
        return $stmt_cus_survey->fetchAll(PDO::FETCH_COLUMN);
    }

    static function checkSurvey($card)
    {
        if (empty($card) || !is_array($card)) return [];
        if ($card != false && $card['survey_id'] > 0) {
            $survey = self::viewSurvey($card['survey_id']);
            if (!empty($survey)) {
                $cus_survey = self::getAnswer($card['id']);
                if (count($cus_survey) <= 0) {
                    return $card['survey_id'];
                }
                return 0;
            }
        }
        return 0;
    }

    static function saveSurvey($survey, $card)
    {
        if (empty($card) || !is_array($card) || !is_array($survey)) return [];
        // trong trường hợp thằng nào vui tính insert hàng loạt vào db
        if (is_numeric(implode('', array_keys($survey)))) {
            foreach ($survey as $item) {
                self::saveSurvey($item, $card);
            }
        } else {
            if (!empty($card['phone'])) {
                $_phone = System::decrypt($card['phone'], CRYPT_KEY);
                $cus = self::getCus($_phone);
                if (isset($cus['id']) && $cus['id'] > 0) {
                    $customer_id = $cus['id'];
                } else {
                    $cus = self::saveCus($_phone);
                    if (!empty($cus)) {
                        $customer_id = $cus;
                    } else {
                        $customer_id = 0;
                    }
                }
            } else {
                $customer_id = 0;
            }
            $card_id = $survey['card_id'];
            $survey_id = $survey['survey_id'];
            $question_id = $survey['question_id'];
            $answer_id = $survey['answer_id'];
            $content = $survey['content'];
            if ($card_id > 0 && $survey_id > 0 && $question_id > 0) {
                $isset = self::issetAnswer($question_id, $card_id);
                if ($isset == true || !empty($isset)) {
                    return false;
                }
                return DB::insert(T_SURVEY_RESULT, ['card_id' => $card_id, 'survey_id' => $survey_id, 'customer_id' => $customer_id, 'question_id' => $question_id, 'answer_id' => $answer_id, 'content' => $content, 'created' => TIME_NOW]);
            }
            return false;
        }
    }

    static function checkPhysicalGift($card)
    {
        if (empty($card) || !is_array($card)) return [];
        if ($card['gift_id'] <= 0) return true;

        $gift = DB::fetch("SELECT count(id) as qty FROM " . T_GIFT_DETAIL . " WHERE id in (SELECT DISTINCT(gift_detail_id) as gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_id={$card['gift_id']}) AND isPhysical=" . IS_ON);
        if (count($gift['qty']) > 0) return true;
        return false;
    }

    static function giftPhysical($card)
    {
        if (empty($card) || !is_array($card)) return [];
        $gift_set = $card['gift_id'];
        if ($gift_set == 0) {
            $outGift = DB::query("SELECT DISTINCT (gift_detail_id) as gift_detail_id FROM " . T_GIFT_LIMIT . " WHERE type=2 and status=" . IS_ON . " AND app_id ={$card['app_id']}")->fetchAll(PDO::FETCH_COLUMN);
            $strId = implode(",", $outGift);
            if (empty($strId)) $strId = 0;
            $gift = DB::query("SELECT id,title,price,avatar FROM " . T_GIFT_DETAIL . " WHERE id not in ($strId) AND status = 1 AND code_quantity > 0 AND isPhysical=" . IS_ON . "  ORDER BY is_hot DESC limit 10")->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $inGift = DB::query("SELECT DISTINCT(gift_detail_id) as gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_id={$card['gift_id']}")->fetchAll(PDO::FETCH_COLUMN);
            $strId = implode(",", $inGift);
            if (empty($strId)) $strId = 0;
            $gift = DB::query("SELECT id,title,price,avatar FROM " . T_GIFT_DETAIL . " WHERE id in ($strId)  AND status = 1 AND code_quantity > 0 AND isPhysical=" . IS_ON . " ORDER BY is_hot DESC limit 10")->fetchAll(PDO::FETCH_ASSOC);
        }
        foreach ($gift as &$item) {
            $images = MediaUrl::fromImageTitle($item['avatar'], [640]);
            $item['image'] = isset($images['640']) ? $images['640'] : "";
        }
        return $gift;
    }

    /*Topup*/
    public static function giftTopup($type, $gift_set = 0)
    {
        $cond = ["a.status=1", "a.id<>1321"];
        $detail_cond = ["code_quantity > 0", "status=1"];
        if ($gift_set > 0) {
            $gift_detail_ids = DB::query("SELECT gift_detail_id from " . T_GIFT_SET . " WHERE gift_id={$gift_set}")->fetchAll(PDO::FETCH_COLUMN);
            if (empty($gift_detail_ids)) return [];
            $str_id = implode(",", $gift_detail_ids);
            $detail_cond[] = "id in($str_id)";
        }

        if ($type == GIFT_TYPE_PHONE) {
            $cond [] = "a.id in(" . GIFT_PHONE_ID . ")";
        } else {
            $cond [] = "a.type in(" . LIST_GIFT_TYPE_TOPUP . ")";
        }
        $str_condition = FunctionLib::addCondition($cond);
        $sql = "SELECT a.id,a.name,b.logo,b.title FROM " . T_GIFT . " a left JOIN " . T_BRAND . " b on a.brand_id=b.id WHERE  $str_condition";
        $gift = DB::query($sql)->fetchAll(PDO::FETCH_ASSOC);
        if (empty($gift)) return [];
        $gift_id = array_column($gift, "id");
        $str_gift_id = implode(",", $gift_id);
        $detail_cond[] = "gift_id in ($str_gift_id)";
        $detail_condition = FunctionLib::addCondition($detail_cond);
        $giftDetail = DB::query("SELECT id,title,price,gift_id,avatar from " . T_GIFT_DETAIL . " WHERE $detail_condition  ORDER BY price asc")->fetchAll(PDO::FETCH_ASSOC);
        $array = [];
        foreach ($gift as $item) {
            $logoBanner = MediaUrl::fromImageTitle($item['logo']);
            foreach ($giftDetail as $gd) {
                if ($gd['gift_id'] == $item['id']) {
                    $array[$item['id']]['id'] = $item['id'];
                    $array[$item['id']]['name'] = $item['name'];
                    $array[$item['id']]['title'] = $item['title'];
                    $array[$item['id']]['image'] = isset($logoBanner['160']) ? $logoBanner['160'] : "";
                    $array[$item['id']]['detail'][] = $gd;
                }
            }
        }
        return $array;
    }

    /*Topup*/
    public static function giftTopupVihat($gift_set = 0)
    {
        $type = [GIFT_TYPE_TOPUP_VIHAT];
        return self::giftToupByType($gift_set, $type);
    }

    public static function giftToupByType($gift_set,$type = []){
        if(empty($type) || $gift_set <= 0) return [];
        $gift_detail_ids = DB::query("SELECT gift_detail_id from " . T_GIFT_SET . " WHERE gift_id={$gift_set}")->fetchAll(PDO::FETCH_COLUMN);
        if (empty($gift_detail_ids)) return [];
        $str_id = implode(",", $gift_detail_ids);
        $typeTopup = implode(',',$type);
        $cond[] = "id in($str_id)";
        $cond[] = "status=1";
        $cond[] = "code_quantity > 0";
        $cond [] = "type in($typeTopup)";
        $detail_cond = FunctionLib::addCondition($cond);
        return DB::query("SELECT id,title,price,gift_id,avatar from " . T_GIFT_DETAIL . " WHERE $detail_cond  ORDER BY price asc")->fetchAll(PDO::FETCH_ASSOC);

    }

    static function checkSendGift($voucher_id)
    {
        if (is_string($voucher_id) || is_numeric($voucher_id)) {
            return DB::query("SELECT count(*) as total from " . T_SMS_SENT . " WHERE cart_detail_id='{$voucher_id}' LIMIT 1")->fetchColumn();
        }
        if (is_array($voucher_id)) {
            $voucher_ids = implode(',', $voucher_id);
            return DB::query('SELECT cart_detail_id from ' . T_SMS_SENT . " WHERE cart_detail_id in ({$voucher_ids})")->fetchAll(PDO::FETCH_ASSOC);

        }
        return 0;
    }
}