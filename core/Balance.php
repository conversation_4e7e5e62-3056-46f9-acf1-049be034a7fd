<?php


class Balance
{
    static function getCatId($id = 0)
    {
        if ($id == 0 || $id == null) {
            return [];
        }
        $sql = "SELECT id FROM " . T_CATEGORY . " WHERE id NOT IN (" . implode(CGlobal::$catNotShow, ',') . ") AND status=1 AND parent_id=" . $id;
        $stmt = DB::query($sql);
        $data = [];
        array_push($data, $id);
        $cats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($cats as $cat) {
            array_push($data, $cat['id']);
        }
        return $data;
    }

    static function getCategory($parent_id)
    {
        if ($parent_id <= 0) return array();
        $category = [];
        $re = \DB::query("SELECT * FROM " . T_CATEGORY . " WHERE id NOT IN (" . implode(CGlobal::$catNotShow, ',') . ") AND status=1 AND parent_id=" . $parent_id . " ORDER BY weight ASC");
        if ($re) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['icon'], [80]);
                $temp = array(
                    'id' => $row['id'],
                    'title' => $row['title'],
                    'image' => isset($images[80]) ? $images[80] : '',
                    'link' => '/card/' . System::$data['card']['token'] . '?cat_id=' . $row['id'],
                );
                $category[] = $temp;
            }
        }
        return $category;
    }

    static function getApp($card)
    {
        if (empty($card)) return [];
        $app = \DB::fetch("SELECT * FROM " . T_APP . " WHERE id=" . $card['app_id']);
        $issue = \DB::fetch("SELECT image,banner_welcome FROM " . T_CARD_ISSUE . " WHERE id=" . $card['issue_id']);
        $image = "";
        $banner_welcome = "";
        if (!empty($issue) && !empty($issue['image'])) {
            $c_image = \MediaUrl::fromImageTitle($issue['image'], []);
            $image = isset($c_image[0]) ? $c_image[0] : '';
        }
        if (!empty($issue) && !empty($issue['banner_welcome'])) {
            $c_banner_welcome = \MediaUrl::fromImageTitle($issue['banner_welcome'], []);
            $banner_welcome = isset($c_banner_welcome[0]) ? $c_banner_welcome[0] : '';
        }
        if (!empty($app)) {
            $logo = \MediaUrl::fromImageTitle($app['logo'], [320]);
            $app['logo'] = isset($logo[320]) ? $logo[320] : '';
            if ($image == "") {
                $banner = \MediaUrl::fromImageTitle($app['banner'], []);
                $image = isset($banner[0]) ? $banner[0] : '';
            }
            if ($banner_welcome == "") {
                $_banner_welcome = \MediaUrl::fromImageTitle($app['banner_welcome'], []);
                $banner_welcome = isset($_banner_welcome[0]) ? $_banner_welcome[0] : '';
            }
            $app['image'] = $image;
            $app['image_welcome'] = $banner_welcome;
            return $app;
        }
        return false;
        return [
            'logo' => '',
            'image' => $image,
            'image_welcome' => $image,
        ];
    }

    //    survey
    static function getAnswer($card_id)
    {
        if ($card_id <= 0) return array();
        $stmt_cus_survey = \DB::query("SELECT survey_id  FROM " . T_SURVEY_RESULT . " WHERE card_id = '{$card_id}' GROUP BY survey_id ");
        $cus_survey = $stmt_cus_survey->fetchAll(PDO::FETCH_COLUMN);
        return $cus_survey;
    }

    static function checkSurvey($token)
    {
        $card = Card::getByToken($token);
        if ($card != false && $card['survey_id'] > 0) {
            $survey = self::viewSurvey($card['survey_id']);
            if (!empty($survey)) {
                $cus_survey = self::getAnswer($card['id']);
                if (count($cus_survey) <= 0)
                    return $card['survey_id'];
                return 0;
            }
            return 0;
        }
        return 0;
    }

    static function getQuestion($question_id)
    {
        $question_id = (int)$question_id;
        if ($question_id <= 0) return array();
        return \DB::fetch("SELECT * FROM " . T_SURVEY_QUESTION . " WHERE id={$question_id}");
    }

    static function issetAnswer($question_id = 0, $card_id = 0)
    {
        $card_id = (int)$card_id;
        $question_id = (int)$question_id;
        if ($question_id < 0) return [];
        if ($question_id == 0 || $card_id == 0) {
            return true;
        }
        return \DB::fetch("SELECT id FROM " . T_SURVEY_RESULT . " WHERE question_id={$question_id} AND card_id={$card_id}");

    }

    static function saveSurvey($survey, $card)
    {
        if (empty($survey) || empty($card)) return array();
        // trong trường hợp thằng nào vui tính insert hàng loạt vào db
        if (is_numeric(implode('', array_keys($survey)))) {
            foreach ($survey as $item) {
                self::saveSurvey($item, $card);
            }
        } else {
            if (!empty($card['phone'])) {
                $_phone = System::decrypt($card['phone'], CRYPT_KEY);
                $cus = self::getCus($_phone);
                if (isset($cus['id']) && $cus['id'] > 0) {
                    $customer_id = $cus['id'];
                } else {
                    $cus = self::saveCus($_phone);
                    if (!empty($cus))
                        $customer_id = $cus;
                    else $customer_id = 0;
                }
            } else {
                $customer_id = 0;
            }
            $card_id = $survey['card_id'];
            $survey_id = $survey['survey_id'];
            $question_id = $survey['question_id'];
            $answer_id = $survey['answer_id'];
            $content = $survey['content'];
            if ($card_id > 0 && $survey_id > 0 && $question_id > 0) {
                $isset = self::issetAnswer($question_id, $card_id);
                if ($isset == true || !empty($isset)) return false;
                return \DB::insert(T_SURVEY_RESULT, ['card_id' => $card_id, 'survey_id' => $survey_id, 'customer_id' => $customer_id, 'question_id' => $question_id, 'answer_id' => $answer_id, 'content' => $content, 'created' => TIME_NOW]);
            }
            return false;
        }
    }

    static function countRequire($id)
    {
        $id = (int)$id;
        if ($id <= 0) return false;
        return \DB::fetch("SELECT COUNT(id) as qty FROM " . T_SURVEY_QUESTION . " WHERE survey_id ={$id} AND isRequired=" . IS_ON);
    }

    static function viewSurvey($id)
    {
        $id = (int)$id;
        if ($id <= 0) return false;
        $survey = \DB::fetch("SELECT id,title,description from " . T_SURVEY . " WHERE id={$id} AND status=" . IS_ON);
        return $survey;
    }

    static function getSurvey($survey_id = 0)
    {
        $survey_id = (int)$survey_id;
        if ($survey_id <= 0) return [];
        $survey_name = DB::fetch("SELECT id,title,description,image from " . T_SURVEY . " WHERE id={$survey_id}");
        $stmt_survey = DB::query("SELECT * FROM " . T_SURVEY_QUESTION . " WHERE survey_id={$survey_id}");
        $question = $stmt_survey->fetchAll(PDO::FETCH_ASSOC);
        foreach ($question as $k => $v) {
            $ans = DB::query("SELECT id, title FROM " . T_SURVEY_ANSWER . " WHERE question_id= {$v['id']} and survey_id = {$survey_id}")->fetchAll(PDO::FETCH_ASSOC);
            $question[$k]['answer'] = $ans;
        }
        $banner = \MediaUrl::fromImageTitle($survey_name['image'], [640]);
        $survey_name['image'] = isset($banner[640]) ? $banner[640] : '';

        return [
            'survey' => $survey_name,
            'question' => $question
        ];
    }

    static function getCus($phone)
    {
        $is_phone = FunctionLib::is_phone($phone);
        if ($is_phone === false) return 0;
        $cus = "SELECT id FROM " . T_CUSTOMER . " WHERE phone={$phone}";
        return \DB::fetch($cus);
    }

    static function saveCus($phone)
    {
        $cus = self::getCus($phone);
        if ($cus === 0 || (isset($cus['id']) && $cus['id'] > 0)) return false;
        $data = [
            'phone' => $phone,
            'status' => 1,
            'active' => 1,
            'created' => TIME_NOW
        ];
        return DB::insert(T_CUSTOMER, $data);
    }

    static function getCartDetail($id)
    {
        if ($id < 0 || $id == "") {
            return false;
        }
        $re = DB::query("SELECT id,cart_id,gift_detail_id FROM " . T_CART_DETAIL . " WHERE id='$id'");
        return $re->fetch(PDO::FETCH_ASSOC);

    }

    static function getGiftById($card, $id = 0)
    {
        $id = (int)$id;
        if ($id == "" || $id <= 0) return [];
        if (empty($card) || !is_array($card)) return [];
        if ($card['gift_id'] > 0) {
            $getSiteGift = DB::fetch("SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_detail_id=$id AND gift_id=" . $card['gift_id']);
            if (empty($getSiteGift)) {
                return [];
            }
        }

        $gift = GiftDetail::get($id);
        if (!empty($gift)) {
            DB::update(T_GIFT_DETAIL, array('view' => $gift['view'] + 1), 'id=' . $id);

            $content = Gift::getContent(Language::$activeLang, $gift['gift_id']);
            if ($content['title'] != "") {
                $gift['title'] = $content['title'];
            }
            $gift['brand'] = DB::fetch("SELECT a.title,a.logo,a.created,a.openTime,a.priceRange,a.online,b.title as cat_title FROM " . T_BRAND . " a LEFT JOIN " . T_CATEGORY . " b on a.cat_id=b.id WHERE a.id=" . $gift['brand_id']);
            $gift['content'] = StringLib::post_db_parse_html($content['content']);
            $gift['note'] = StringLib::post_db_parse_html($content['note']);

            $s_image = "SELECT image from " . T_GIFT_IMAGE . " WHERE gift_detail_id={$gift['id']} AND status=" . IS_ON . " ORDER BY id asc";
            $stmt_image = DB::query($s_image);
            $gift_image = [];
            while ($ri = $stmt_image->fetch(PDO::FETCH_ASSOC)) {
                $banner = MediaUrl::fromImageTitle($ri['image'], [640]);
                $gift_image[] = isset($banner[640]) ? $banner[640] : '';
            }
            $images = MediaUrl::fromImageTitle($gift['brand']['logo'], [80]);
            $gift['brand']['logo'] = isset($images[80]) ? $images[80] : '';
            $gift['gift_images'] = $gift_image;
        }

        return $gift;
    }

    //đang dùng cho trang receiver, không cần lấy nhiều thông tin
    static function getGift($id = 0)
    {
        if ($id < 0 || $id == "") {
            return false;
        }
        $s = "SELECT a.id,a.title,a.gift_id,a.avatar,a.price,a.isPhysical,b.weight,b.needPhone,b.needIdentity,a.brand_id FROM " . T_GIFT_DETAIL . " a LEFT JOIN " . T_GIFT . " b on a.gift_id=b.id WHERE a.id=" . $id;

        $data = DB::fetch($s);
        $content = Gift::getContent(Language::$activeLang, $data['gift_id']);
        if (isset($content['note']) && $content['note'] != '') {
            $data['gift_content'] = $content['note'];
        }
        $images = MediaUrl::fromImageTitle($data['avatar'], [640]);
        $data['banner'] = isset($images['640']) ? $images['640'] : "";
        return $data;

    }

    static function brandInGiftSet($brand_id = 0, $gift_id = 0)
    {
        if ($brand_id <= 0) return false;
        if ($gift_id <= 0) {
            $giftLimit = DB::query("SELECT count(*) as  total from " . T_GIFT_LIMIT . " WHERE brand_id={$brand_id}")->fetch(PDO::FETCH_COLUMN);
            if ($giftLimit > 0) return false;
            return true;
        } else {
            $sql = "SELECT gift_detail_id from " . T_GIFT_SET . " WHERE gift_id=$gift_id and status= 2";
            $giftIds = DB::query($sql)->fetchAll(PDO::FETCH_COLUMN);
            if (!empty($giftIds)) {
                $string_gift_id = implode(",", $giftIds);
                $isBrand = DB::query("SELECT count(*) as total from " . T_GIFT_DETAIL . " WHERE brand_id={$brand_id} AND ID in($string_gift_id)")->fetch(PDO::FETCH_COLUMN);
                if ($isBrand > 0) return true;
                return false;
            } else {
                return false;
            }
        }
    }

    static function checkCardPrefix($card_number)
    {
        $mai_linh = "120793";
        if (strpos($card_number, $mai_linh) === 0) {
            return false;
        }
        return true;
    }

    static function selectLinkVersion($version)
    {
        $selector = [
            "default" => "Thẻ balance đổi quà nhiều lần giao diện page.urbox.vn (1)",
            "ver3" => "Thẻ balance đổi quà nhiều lần giao diện page.urbox.vn",
            "ver5" => "Thẻ balance chọn 1 quà trong giftset dùng 1 lần",
            "ver6" => "Thẻ balance có 1 quà",
            "ver8" => "Thẻ balance dùng nhiều lần để topup điện thoại",
        ];
        return isset($selector[$version]) ? $selector[$version] : $selector['default'];
    }

    static function selectCardStatusName($cardStatus)
    {
        $selector = [
            1 => "Chưa hoạt động",
            2 => "Đang hoạt động",
            3 => "Đã khóa",
            4 => "Đã ban",
            5 => "Đã sử dụng trong App"
        ];
        return isset($selector[$cardStatus]) ? $selector[$cardStatus] : "";
    }

    static function isUseCard($id)
    {
        $card_trans = DB::fetch("SELECT count(*) as total from " . T_CARD_TRANSACTION . " WHERE card_id=$id and type = 2 and status =2");
        return $card_trans['total'];
    }

    static function calcTimeDelivery($city_id, $time = TIME_NOW)
    {
        $bigCity = $city_id == 29 || $city_id == 22;
        $deliveryDaysPlus = $bigCity ? ['min' => 3, 'max' => 4] : ['min' => 5, 'max' => 6];
        $dayOfWeek = date('w', strtotime($time));
        $dayPlus = 0;
        if ($dayOfWeek + $deliveryDaysPlus['min'] >= 7) $dayPlus = 2;
        elseif ($dayOfWeek + $deliveryDaysPlus['min'] >= 6) $dayPlus = 1;
        $dateFrom = $time + ($deliveryDaysPlus['min'] + $dayPlus) * 24 * 60 * 60;
        $dateTo = $time + ($deliveryDaysPlus['max'] + $dayPlus) * 24 * 60 * 60;

        $balance = new Balance();

        $displayDateFrom = $balance->convertDayToVN(date('D', $dateFrom)) . ', ' . date('d/m/Y', $dateFrom);
        $displayDateTo = $balance->convertDayToVN(date('D', $dateTo)) . ', ' . date('d/m/Y', $dateTo);

        return [
            'from' => $displayDateFrom,
            'to' => $displayDateTo,
        ];

    }

    private function convertDayToVN($day)
    {
        $selector = [
            "Mon" => "Thứ Hai",
            "Tue" => "Thứ Ba",
            "Wed" => "Thứ Tư",
            "Thu" => "Thứ Năm",
            "Fri" => "Thứ Sáu",
            "Sat" => "Thứ Bảy",
            "Sun" => "Chủ Nhật"
        ];
        return $selector[$day] ?: $selector["Mon"];
    }

    static function getBrandOfficeApply($gift_id)
    {
        // $gift = DB::fetch("SELECT id,approved_store FROM " . T_GIFT. " WHERE id='$gift_id'");
        //if(empty($gift) || $gift['approved_store'] == 1) return false;
        $gift_brand_office = DB::query("SELECT distinct(brand_office_id) as id FROM " . T_GIFT_BRAND_OFFICE . " WHERE gift_id='$gift_id'")->fetchAll(PDO::FETCH_COLUMN);
        return empty($gift_brand_office) ? false : implode(',', $gift_brand_office);
    }

    static function getBrand($id)
    {
        $id = (int)$id;
        if ($id <= 0) {
            return [];
        }
        $sql = "SELECT a.id,a.title,a.logo,a.online,a.cat_id,a.openTime,a.priceRange,a.mota FROM " . T_BRAND . " a WHERE a.id={$id}";
        $stmt = DB::query($sql);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    static function getBrandById($id)
    {
        $id = (int)$id;
        if ($id <= 0) return [];
        $brand = self::getBrand($id);
        if (empty($brand)) return [];
        $category = DB::fetch("SELECT title FROM " . T_CATEGORY . " WHERE id={$brand['cat_id']} and status=1");
        $brand['category'] = $category['title'];
        $logo = MediaUrl::fromImageTitle($brand['logo'], [160]);
        $brand['logo'] = isset($logo[160]) ? $logo[160] : '';
        $banner = DB::fetch("SELECT image FROM " . T_BRAND_IMAGE . " WHERE brand_id={$id} and type=2");
        $image = MediaUrl::fromImageTitle($banner['image'], [640]);
        $brand['banner'] = isset($image[640]) ? $image[640] : '';
        return $brand;
    }

    static function getOffice($brand_id, $gift_id = 0, $lm = 0, $filter = false)
    {
        $limit = "";
        $lm = (int)$lm;
        $brand_id = (int)$brand_id;
        if ($lm > 0) {
            $limit = " LIMIT $lm";
        }
        $brand_office_id = false;
        if ($gift_id > 0) {
            $brand_office_id = self::getBrandOfficeApply($gift_id);
        }
        if ($filter == false) {
            $cond[] = "brand_id={$brand_id} AND status=" . IS_ON;
            if ($brand_office_id !== false) {
                $cond[] = "id in($brand_office_id)";
            }
            $where = FunctionLib::addCondition($cond);
            $s_office = "SELECT id,address,address_en,latitude,longitude,city_id,isApply,brand_id,''as 'distance' from " . T_BRAND_OFFICE . " WHERE $where " . $limit;
            $stmt_office = DB::query($s_office);
            $data = $stmt_office->fetchAll(PDO::FETCH_ASSOC);

        } else {
            $allow = self::getAccessLoc();
            $my_loc = self::getCurrentLoc();
            if ($allow == 1 && count($my_loc) == 2) {
                // nếu định vị quanh đây

                $mylat = (float)$my_loc[0];
                $mylon = (float)$my_loc[1];
                $lon1 = $mylon - USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
                $lon2 = $mylon + USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
                $lat1 = $mylat - (USER_DISTANCE / 69);
                $lat2 = $mylat + (USER_DISTANCE / 69);


                $expression = "(6371 * acos( cos( radians($mylat) ) * cos( radians( a.latitude ) ) * cos( radians( a.longitude ) - radians($mylon) ) + sin( radians($mylat) ) * sin( radians( a.latitude ) ) ) )";
                $sql = "SELECT  a.id ,a.address,a.address_en,b.title,isApply,a.brand_id,ROUND($expression,1) as distance
                    FROM " . T_BRAND_OFFICE . " a
                    LEFT JOIN " . T_BRAND . " b ON a.brand_id = b.id
                    WHERE
                        a.status=" . IS_ON . " AND b.status= " . IS_ON . " AND
                        a.brand_id =$brand_id
						AND (
							(a.longitude BETWEEN $lon1 AND $lon2 AND a.latitude BETWEEN $lat1 AND $lat2)
							OR online=" . IS_YES . "
				 		)
                    ORDER BY $expression $limit";
                $data = DB::query($sql)->fetchAll(PDO::FETCH_ASSOC);
            } else {
                $city_id = CookieLib::get("C_MY_CITY");
                $city_id = (int)$city_id;
                $cond = [];
                $cond[] = "a.status=" . IS_ON;
                $cond[] = "a.status=" . IS_ON;
                $cond[] = "a.brand_id =$brand_id";
                if ($brand_office_id != false) {
                    $cond[] = "a.id in($brand_office_id)";
                }
                if ($city_id > 0 && $city_id != 9999) {
                    $cond[] = "a.city_id = $city_id";
                }
                $cond = FunctionLib::addCondition($cond);
                $sql = "SELECT  a.id,a.address,a.address_en,b.title,a.brand_id,isApply,'' as 'distance'

                    FROM " . T_BRAND_OFFICE . " a
                    LEFT JOIN " . T_BRAND . " b ON a.brand_id = b.id
                    WHERE
                     $cond
                    ORDER BY a.address $limit";

                $data = DB::query($sql)->fetchAll(PDO::FETCH_ASSOC);
            }
        }
        if (Language::$activeLang != "vi") {
            foreach ($data as &$item) {
                $item['address'] = $item['address_en'];
            }
        }
        return $data;


    }
    static function viewOffice($id)
    {
        $id = (int)$id;
        if ($id <= 0) {
            return [];
        }
        $sql = "SELECT a.address,a.address_en,latitude,longitude,city_id,brand_id,b.title,b.logo,isApply FROM " . T_BRAND_OFFICE . " a
        		LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id WHERE a.id=$id";
        $office = DB::query($sql)->fetch(PDO::FETCH_ASSOC);
        $logo = MediaUrl::fromImageTitle($office['logo'], [160]);
        $office['logo'] = isset($logo[160]) ? $logo[160] : '';
        if (Language::$activeLang != "vi") {
            $office['address'] = $office['address_en'];
        }
        return $office;
    }

    static function getCityName()
    {
        // nếu tắt định vị
        $my_loc = self::getCurrentLoc();
        if (self::getAccessLoc() == 0 || count($my_loc) != 2) {
            $my_city = CookieLib::get("C_MY_CITY");
            $my_city = (int)$my_city;
            switch ($my_city) {
                case -1:
                    $city_name = "Tỉnh thành khác";
                    $data = [
                        'id' => -1,
                        'title' => $city_name
                    ];
                    break;
                case 0:
                    $city_name = "Tất cả";
                    $data = [
                        'id' => 0,
                        'title' => $city_name
                    ];
                    break;
                default:
                    $my_city = (int)$my_city;
                    $sql = "SELECT id, title FROM " . T_PROVINCE . " WHERE id={$my_city}";
                    $data = DB::fetch($sql);
            }
            return $data;
        } else {
            $geocode = file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?latlng={$my_loc[0]},{$my_loc[1]}&sensor=false&key=" . GOOGLE_API_KEY);
            $data = json_decode($geocode);
            $add_array = $data->results;
            if (!empty($add_array)) {
                $add_array = $add_array[0];
                $add_array = $add_array->address_components;
            }
            $state = "";
            $city = "Tỉnh thành khác";
            foreach ($add_array as $key) {
                if ($key->types[0] == 'administrative_area_level_2') {
                    $city = $key->long_name;
                }
                if ($key->types[0] == 'administrative_area_level_1') {
                    $state = $key->long_name;
                }
            }
            // nếu bật định vị
            return [
                'id' => 9999,
                'title' => implode(", ", [$city, $state])
            ];
        }
    }
    static function getAccessLoc()
    {
        $allow = CookieLib::get('C_ALLOW_LOCATION');
        return (int)$allow;
    }
    static function getCurrentLoc()
    {
        $loc = CookieLib::get('C_MY_LOCATION');
        $loc = explode(",", $loc);
        if (isset($loc[0])) $loc[0] = (float)$loc[0];
        if (isset($loc[1])) $loc[1] = (float)$loc[1];
        return $loc;
    }

}
