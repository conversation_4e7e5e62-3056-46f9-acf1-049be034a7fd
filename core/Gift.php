<?php
class Gift{
	static $subDir = 'gift/';
	static $cacheTime = 86400;

	static function get($id){
		$item = DB::fetch("SELECT * FROM ".T_GIFT." WHERE id = $id LIMIT 1");
		if($item){
			$re = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE status=1 AND gift_id = ".$item['id']." ORDER BY position ASC");
			if($re){
				while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
					$item['detail'][$r['id']] = $r;
				}
			}
            if(isset($item['detail'])&&!empty($item['detail'])){
                $first_key = key($item['detail']);
                $item['main'] = $item['detail'][$first_key];
            }


			$re = DB::query("SELECT * FROM ".T_GIFT_TAG." WHERE gift_id = ".$item['id']." ORDER BY id DESC");
			if($re){
				while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
					$item['tag'][$r['id']] = $r;
				}
			}

			$re = DB::query("SELECT * FROM ".T_GIFT_TAG_RELATIVE." WHERE gift_id = ".$item['id']." ORDER BY id DESC");
			if($re){
				while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
					$item['tag_relative'][$r['id']] = $r;
				}
			}
		}
		return self::parse($item);
	}


    static function getWhere($params = array()){
        $where = array();
        if(isset($params['idNotIn'])){
        	$where[] = "id NOT IN(".implode(',', $params['idNotIn']).")";
        }
        if(isset($params['statusIn'])){
        	$where[] = "status IN(".implode(',', $params['statusIn']).")";
        }
        $arrKeys = array('id', 'name', 'brand_id', 'display', 'status','code_type');
        foreach ($arrKeys as $key => $value) {
            if (isset($params[$value])) {
                if(gettype($params[$value]) == 'string') {
                    if($params[$value] != ''){
                        $where[] = $value. " LIKE '%".$params[$value]."%'";
                    }
                }
                elseif($params[$value] != -1) $where[] = $value. " = ".$params[$value];
            }
        }
        if(isset($params['sex'])){
        	$where[] = "sex >= ".$params['sex'];
        }
        if(isset($params['city_id'])){
        	$where[] = "(city_id=0 OR city_id=".$params['city_id'].")";
        }
        if(isset($params['age_from'])){
            $where[] = "age_from >= ".$params['age_from'];
        }
        if(isset($params['age_to'])){
            $where[] = "age_to <= ".$params['age_to'];
        }

        if(isset($params['price_from'])){
        	$where[] = "price_from >= ".$params['price_from'];
        }
        if(isset($params['price_to'])){
            $where[] = "price_to <= ".$params['price_to'];
        }

        if(isset($params['id_arr'])&&!empty($params['id_arr'])){
            $where[] = "id IN(".implode(',', $params['id_arr']).")";
        }
        if(isset($params['sameBrand'])){
            $where[] = "id != ".$params['sameBrand'];
        }
        if(isset($params['keyword'])){
            $arrGiftID = array();
        	$re = DB::query("SELECT gift_id FROM ".T_GIFT_TAG." WHERE title LIKE '%".$params['keyword']."%' OR title_md5='".StringLib::md5_title($params['keyword'])."' OR title_md5_nounicode='".StringLib::md5_title_unsign($params['keyword'])."'");
            if($re){
                while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
                    $arrGiftID[$r['gift_id']] = $r['gift_id'];
                }
            }



            if(!empty($arrGiftID)){
                $where[] = "id IN(".implode(',', $arrGiftID).")";
            }
            else{
                $where[] = "id = 0";
            }

            // $re = DB::query("SELECT id FROM ".T_ARTICLE." WHERE title_page LIKE '%".$params['keyword']."%'";
            // if($re){
            //     while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
            //         $arrGiftID[] =
            //     }
            // }
        }

        if(isset($params['cat_id'])){
            $arrCatID = array($params['cat_id'] => $params['cat_id']);
            $re = DB::query("SELECT id FROM ".T_CATEGORY." WHERE parent_id = ".$params['cat_id']." AND status = 1");
            if($re){
                while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
                    $arrCatID[$r['id']] = $r['id'];
                }
            }

            if(!empty($arrCatID)){
                $where[] = "cat_id IN(".implode(',', $arrCatID).")";
            }
        }

        return $where;
    }

    static function parse($r){
        if(!empty($r)){
            // $r['expired'] = TimeUtils::convertFromNumberToDate($r['expired']);
            #$r['del'] = Url::build_current(array('cmd' => 'gift', 'action' => 'delete', 'id' => $r['id']));
            #$r['edit'] = Url::build_current(array('cmd' => 'gift', 'action' => 'edit', 'id' => $r['id']));
            #$r['content'] = Url::build_current(array('cmd' => 'gift', 'action' => 'content', 'id' => $r['id']));
            #$r['link'] = Url::build('detail',array('title'=>StringLib::safe_title($r['name'].'-'.$r['id'])));
        }

		return $r;
	}

    static function getByID($arrGiftId = array(), $field = array(),$order = 'id ASC'){
        $arrGiftID = array();
        $arrBrandID = array();
		$strField = '*';
		if(!empty($field)){
			$strField = implode(', ', $field);
		}

		$items = array();
		$sql = "SELECT $strField FROM ".T_GIFT." WHERE status=1 AND id IN(".implode(', ', $arrGiftId).") ORDER BY ".$order;
		$re = DB::query($sql);
		if($re){
			while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
                $arrGiftID[] = $r['id'];
                $arrBrandID[$r['brand_id']] = $r['brand_id'];

				$items[$r['id']] = self::parse($r);
			}

            if(!empty($arrGiftID)){
                $arrDetail = self::getDetailByGiftID($arrGiftID);
                if(!empty($arrDetail)){
                    foreach ($arrDetail as $k => $v) {
                        $items[$k]['detail'] = $v;
                    }
                }
            }

            if(!empty($arrBrandID)){
                $arrBrand = Brand::getByID($arrBrandID);
            }

            foreach ($items as $k => $v) {
                if(!empty($arrBrand[$v['brand_id']])){
                    $items[$k]['brand'] = $arrBrand[$v['brand_id']];
                }

                if(!isset($items[$k]['main']) && !empty($items[$k]['detail'])){
                    $first_key = key($items[$k]['detail']);
                    $items[$k]['main'] = $items[$k]['detail'][$first_key];
                }
            }
		}
		return $items;
	}

	static function delete($id){
		$item = self::get($id);
		if($item){
			$status = abs($item['status'] - 1);
			DB::update(T_GIFT, array('status' => $status), "id = $id");
		}

		return true;
	}

	static function getByTag($tag){
		//
	}

	static function getByTagRelative($tag){

	}

	static function getDetail($gift_detail_id){
        $item = DB::fetch("SELECT * FROM ".T_GIFT_DETAIL." WHERE id = $gift_detail_id LIMIT 1");
        return $item;
    }

    static function getDetailByID($arrDetailID,$showImg = 1){
    	$items = array();
        $re = DB::query("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id IN(".implode(',', $arrDetailID).") ORDER BY position ASC");
        if ($re) {
            while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
            	if($showImg == 2){
					$c_image = MediaUrl::fromImageTitle($r['avatar'], [640]);
					$image = isset($c_image[0]) ? $c_image[0] : '';
					$r['image'] = $image;
				}
                $items[$r['id']] = $r;
            }
        }

        return $items;
    }

    static function getGiftDetail($gift_id){
        $item = DB::fetch("SELECT * FROM ".T_GIFT." WHERE id = $gift_id LIMIT 1");
        if($item) {
            $re = DB::query("SELECT * FROM " . T_GIFT_DETAIL . " WHERE gift_id = " . $item['id'] . " ORDER BY position ASC");
            if ($re) {
                while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
                    $item['detail'][$r['id']] = $r;
                }
            }
        }
        return self::parse($item);

    }

    static function getDetailByGiftID($arrID = array()){
    	$items = array();
        if(!empty($arrID)) {
            $re = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE gift_id IN(".implode(',', $arrID).") ORDER BY position ASC");
            if($re) {
                while ($r = $re->fetch(PDO::FETCH_ASSOC)) {
                    $items[$r['gift_id']][$r['id']] = $r;
                }
            }
        }
        return $items;
    }

    static function getItemPerPageByName($key){
    	$key = CGlobal::$current_page.'-'.$key;
    	switch ($key) {
    		case 'home-hots': return 8; break;
    		case 'gift-hots': return 3; break;
    		default: return 8; break;
    	}
    }

    static function getItemSameTagbyId($id){
        $arrItemTag = array();
        $tagrelative = GiftTagRelative::getList(array('gift_id'=>$id));
        $arrTag = array();
        foreach ($tagrelative as $k => $val){
            $arrTag[$val['title_md5']] = $val['title_md5'];
        }
        $arrIdItemRelative = array();
        if(!empty($arrTag)){
            $retag = DB::query("SELECT * FROM ".T_GIFT_TAG." WHERE status=1 AND title_md5 IN('".implode("','",$arrTag)."')");
            if($retag){
                while ($row = $retag->fetch(PDO::FETCH_ASSOC)){
                    if($row['gift_id']!=$id){
                        $arrIdItemRelative[$row['gift_id']] = $row['gift_id'];
                    }
                }
            }
        }
        if(!empty($arrIdItemRelative)){
            $arrItemTag = Gift::getByID($arrIdItemRelative,array(),' buyed DESC');
        }
        return $arrItemTag;
    }

    static function getGiftByPrice($price,$per_page){
        $arrIdgift = array();
        $arrGift = array();
        $arrBrand = array();
        $arrDetailPrice = array();
        $items = array('gift'=>array(),'article'=>array(),'brand'=>array(),'price'=>array());
        if($price>0){
            $re = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE status=1 AND price<= ".$price." GROUP BY gift_id ORDER BY position DESC LIMIT 50");
            if($re){
                while ($row = $re->fetch(PDO::FETCH_ASSOC)){
                    $arrDetailPrice[$row['gift_id']] = $row;
                    $arrIdgift[$row['gift_id']] = $row['gift_id'];
                }
                $arrArticle = array();
                if(!empty($arrIdgift)){
                    $sql = "SELECT * FROM ".T_GIFT." WHERE status=1 AND id IN(".implode(',',$arrIdgift).")";
                    $reGift = Pagging::query($sql, $per_page);
                    if($reGift){
                        $arrIdBrand = array();
                        $arrIdArticle = array();
                        while ($row = $reGift->fetch(PDO::FETCH_ASSOC) ){
                            $arrGift[$row['id']] = self::parse($row);
                            $arrIdArticle[$row['article_id']] = $row['article_id'];
                            $arrIdBrand[$row['brand_id']] = $row['brand_id'];
                        }
                        if(!empty($arrIdBrand)){
                            $arrBrand = Brand::getByID($arrIdBrand);
                        }
                        if(!empty($arrIdArticle)){
                            $arrArticle = Article::getByID($arrIdArticle);
                        }
                        $items['price'] = $arrDetailPrice;
                        $items['gift'] = $arrGift;
                        $items['article'] = $arrArticle;
                        $items['brand'] = $arrBrand;
                    }
                }
            }
        }
        return $items;
    }

    static function getMinMaxPrice($arrDetail){
        $arrMinMax = array('min' => 10000000, 'max' => 0);
        foreach ($arrDetail as $key => $value) {
            if($value['price'] < $arrMinMax['min']) $arrMinMax['min'] = $value['price'];
            if($value['price'] > $arrMinMax['max']) $arrMinMax['max'] = $value['price'];
        }

        return $arrMinMax;
    }

    /*
     * Ngày 25-7-2018 API viết lại cách lấy GIFT để nhẹ nhàng query
     * */

    static function lists($params = array(), $field = array(), $per_page = 0){
        $data = array();
        $strInnerJoin = '';
        $strWhere = FunctionLib::addCondition(self::getWhere($params), true);
        $strField = 'c.*';
        if(!empty($field)){
            $strField = 'c.'.implode(', c.', $field);
        }
        $page_no = Url::getParamInt('page_no',1);
        $sql = "SELECT $strField FROM ".T_GIFT. " AS c $strInnerJoin $strWhere ORDER BY buyed DESC, c.id DESC";
        $re = $per_page? Pagging::pager_query($sql, $per_page) : DB::query($sql);
        if ($re) {
            $arr_brand_id = array();
            $arr_detail_id = array();
            while ($row = $re->fetch(PDO::FETCH_ASSOC)){
                $arrImage = (array)json_decode($row['images'], true);
                $temp_imgages = '';
                if(!empty($arrImage)){
                    foreach ($arrImage as $k => $v) {
                        if($v!=''&&$temp_imgages == ''){
                            $temp_imgages = ImageUrl::getImageURL($v, $row['created'], 320, 'gift', 'gift/');
                        }
                    }
                }
                $temp = array(
                    'id'=>$row['id'],
                    'brand_id'=>$row['brand_id'],
                    'name'=>$row['name'],
                    'images_src'=>$temp_imgages,
                );
                $arr_brand_id[$row['brand_id']] = $row['brand_id'];
                $arr_detail_id[$row['id']] = $row['id'];
                $data[] = $temp;
            }
            if($arr_detail_id){
                $red = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE gift_id IN(".implode(',',$arr_detail_id).")");
                if($red){
                    while ($rowd = $red->fetch(PDO::FETCH_ASSOC)){
                        foreach ($data as &$value){
                            if($value['id']==$rowd['gift_id']){
                                $value['detail'][] = array('id'=>$rowd['id'],'title'=>$rowd['title'],'price'=>$rowd['price'],'point'=>self::exchangePoint($rowd['price']));
                            }
                        }
                    }
                }
            }
            if(!empty($arr_brand_id)){
                $re2 = DB::query("SELECT * FROM ".T_BRAND." WHERE id IN(".implode(',',$arr_brand_id).")");
                if($re2){
                    while ($row2 = $re2->fetch(PDO::FETCH_ASSOC)){
                        foreach ($data as &$value){
                            if($value['brand_id']==$row2['id']){
                                $value['brand'] = array('id'=>$row2['id'],'title'=>$row2['title']);
                            }
                        }
                    }
                }
            }
        }
        return $data;
    }

    static function exchangePoint($price = 0){
        return $price/CGlobal::$point_exchange;
    }
    static function getContent($lang,$gift_id){
        $content = DB::fetch("SELECT title,note,content FROM ".T_GIFT_CONTENT." WHERE lang='$lang' AND gift_id = '$gift_id' AND status = ".IS_ON . " ORDER BY id desc");
        return $content;
    }
    static function checkTopupGift($ids){
    	if(empty($ids)) return 0;
    	$id = implode(',',$ids);
		$content = DB::fetch("SELECT COUNT(a.id) as qty FROM ".T_GIFT_DETAIL." a LEFT JOIN ". T_GIFT." b on a.gift_id=b.id WHERE a.id in ($id) AND b.type in (5,6,8) AND a.status = 1");
		return $content['qty'];
	}
	static function isTopupGift($ids){
		if(empty($ids)) return 0;
		$id = implode(',',$ids);
		$content = DB::fetch("SELECT COUNT(id) as qty FROM ".T_GIFT."  WHERE id in ($id) AND type in (6) AND status in(0,1,2)");
		return $content['qty'];
	}
	static function getGiftParent($arrGiftId = array(), $field = []){
		$strField = '*';
		if(!empty($field)){
			$strField = implode(', ', $field);
		}
		
		$items = array();
		$sql = "SELECT $strField FROM ".T_GIFT." WHERE status >0 and status != 3 AND id IN(".implode(', ', $arrGiftId).")";
		$re = DB::query($sql);
		if($re) {
			while ($r = $re->fetch( PDO::FETCH_ASSOC )) {
				$items[$r['id']] = self::parse( $r );
			}
		}
		return $items;
	}
}
?>