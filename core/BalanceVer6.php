<?php

class BalanceVer6 extends Balance
{

    static function detailVoucher($card)
    {
        if (empty($card) || !is_array($card)) return [];
        $array = [];
        $rec = DB::query("SELECT id FROM " . T_CART . " WHERE status=" . IS_ON . " AND pay_status=" . IS_YES . " AND card_id=" . $card['id']);
        $carts_id = [0];
        if ($rec) {
            $carts_id = $rec->fetchAll(PDO::FETCH_COLUMN);
        }
        if (empty($carts_id)) return false;
        $condition[] = "b.status > 0";
        $condition[] = "c.status=" . IS_ON;
        $condition[] = "c.cart_id in (" . implode(',', $carts_id) . ")";
        //$condition[] = "c.delivery <> 3";

        $condition = FunctionLib::addCondition($condition);
        $sql = "SELECT a.id,a.title,a.avatar,a.brand_id,b.expired,c.created,b.code,a.price,d.code_type,d.showbarcode,d.type,c.delivery,d.article_id,receive_code,b.status as code_status,c.id as cart_detail_id,b.id as code_id,c.gift_id,b.id as code_id,b.used,b.valid_time,e.identity,d.needIdentity,d.needPhone,b.pin,b.serial,b.active, a.promotion_code
                FROM " . T_GIFT_DETAIL . " a
                LEFT JOIN " . T_CART_DETAIL . " c on c.gift_detail_id=a.id
                LEFT JOIN " . T_GIFT_CODE . " b on c.id=b.cart_detail_id
                LEFT JOIN " . T_GIFT . " d on d.id=a.gift_id
                LEFT JOIN " . T_CUSTOMER . " e on e.id=c.customer_id
    
                WHERE $condition order by c.id desc limit 1";
        $stmt = DB::query($sql);
        if ($stmt) {
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if ($row['code'] == null || $row['code'] == "") {
                    self::assignCode($row['cart_detail_id']);
                }
                if ($row['used'] == 0) {
                    DB::update(T_GIFT_CODE, ['last_time' => TIME_NOW], "id={$row['code_id']}");
                }
                if($row['promotion_code'] == '') {
                    $row['code'] = System::decrypt($row['code'], CRYPT_KEY);
                }else{
                    $row['code'] = $row['promotion_code'];
                }
                if (Language::$activeLang != "vi") {
                    $content = Gift::getContent(Language::$activeLang, $row['gift_id']);
                    $row['title'] = $content['title'];
                }
                $images = MediaUrl::fromImageTitle($row['avatar'], [640]);
                $row['image'] = isset($images[640]) ? $images[640] : '';

                $row['link'] = LINK_URBOX . 'nhan-qua/' . System::decrypt($row['receive_code']) . '.html';
                if ($row['showbarcode'] == 2) {
                    $qrCode = $row['code'];
                } else {
                    $qrCode = $row['code'];
                }
                $row['qrCode'] = $qrCode;
                if ($row['showbarcode'] == 5) {
                    $extractSerial = explode(' PIN:', $row['serial']);
                    $row['serial'] = $extractSerial[0];
                    $row['pin'] = isset($extractSerial[1]) ? $extractSerial[1] : "";
                }
                $array['gifts'][$row['cart_detail_id']] = $row;
            }
            $item = [];
            if (!empty($array['gifts'])) {
                $item = current($array['gifts']);
            }
            $brand_id = $item['brand_id'];
            if (!empty($item)) {
                $content = Gift::getContent(Language::$activeLang, $item['gift_id']);
                if ($content['title'] != "") {
                    $gift['title'] = $content['title'];
                }
                $array['note'] = html_entity_decode($content['note']);
            } else {
                $array['note'] = "";
            }

            if ($brand_id > 0) {
                $brand = DB::fetch("SELECT id,title,online from " . T_BRAND . " WHERE id={$brand_id}");
                $array['brand'] = $brand;
            }
        }
        return $array;
    }

    static function getGiftByBrand($tokens, $params = [])
    {
        if (empty($tokens)) return false;
        $param = [];
        $param[] = 'code_quantity > 0';
        $param[] = 'a.status=1';
        $param[] = 'a.type<>3';
        $param[] = 'a.type<>8';
        $param[] = 'a.isUnfixPrice=' . IS_OFF;
        //giftset
        $id_gift_set = isset(System::$data['card']['gift_id']) && System::$data['card']['gift_id'] > 0 ? System::$data['card']['gift_id'] : $tokens['gift_id'];
        if ($id_gift_set <= 0) {
            $s = "SELECT DISTINCT (gift_detail_id) FROM " . T_GIFT_LIMIT . " WHERE type=2 and status=" . IS_ON . " AND app_id ={$tokens['app_id']}";
            $stmt = DB::query($s);
            $giftset_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
            if (empty($giftset_ids)) $giftset_ids = [0];
        } else {
            $s1 = "SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE gift_id={$id_gift_set}";
            $stmt = DB::query($s1);
            $giftset_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
            if (empty($giftset_ids)) $giftset_ids = [0];
        }

        if ($id_gift_set <= 0) {
            $param[] = " a.id NOT IN (" . implode($giftset_ids, ',') . ")";
        } else {
            $param[] = " a.id IN (" . implode($giftset_ids, ',') . ")";
        }
        if (isset($params['keyword']) && strlen($params['keyword']) > 2) {
            $param[] = " a.title LIKE '%" . $params['keyword'] . "%'";
        }

        $condition = FunctionLib::addCondition($param, true);
        $sql = "SELECT a.id,a.gift_id,a.title,a.price,a.avatar,e.title as brand_title 
                        FROM " . T_GIFT_DETAIL . " a 
                        LEFT JOIN " . T_CART_DETAIL . " c on c.gift_detail_id=a.id
                        LEFT JOIN " . T_BRAND . " e on e.id=a.brand_id" . $condition . " 
                        ORDER BY a.title asc limit 1";
        $data = array();

        $re = DB::query($sql);
        while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
            $images = MediaUrl::fromImageTitle($row['avatar'], [640]);
            $temp = array(
                'id' => $row['id'],
                'brand_title' => $row['brand_title'],
                'gift_id' => $row['gift_id'],
                'title' => $row['title'],
                'price' => $row['price'],
                'image' => isset($images[640]) ? $images[640] : ''
            );
            $data[$row['id']] = $temp;
        }

        return $data;
    }

    static function getOffice($brand_id,$gift_id = 0, $lm = 0, $filter = false)
    {
        $cond = [];
        $cond[] = "brand_id={$brand_id}";
        $cond[] = "status=". IS_ON;
        if($gift_id > 0) {
            $brand_office_id = Balance::getBrandOfficeApply($gift_id);
            if($brand_office_id !== false){
                $cond[] = "id in ($brand_office_id)";
            }
        }
        $where = FunctionLib::addCondition($cond);
        $s_office = "SELECT id,address,address_en,latitude,longitude,city_id,isApply,brand_id,''as 'distance' from " . T_BRAND_OFFICE . " WHERE " . $where ;
        $stmt_office = DB::query($s_office);
        $data = $stmt_office->fetchAll(PDO::FETCH_ASSOC);
        return $data;
    }
}
