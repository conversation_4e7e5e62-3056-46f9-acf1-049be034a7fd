<?php
class ReceiverCore{
	static $subDir = 'receiver/';
	static $cacheTime = 86400;

	static function get($id){
		$item = DB::fetch("SELECT * FROM ".T_GIFT_RECEIVER." WHERE id = $id LIMIT 1");
		if($item){
            #$item['image_src'] = ImageUrl::getImageURL($item['image'], $item['created'], 80, 'receiver', 'receiver/');
		}
		return $item;
	}

	static function getList($params = array(), $field = array(), $per_page = 0){
		$items = array();
		$strInnerJoin = '';
		$strWhere = FunctionLib::addCondition(self::getWhere($params), true);
		$strField = 'c.*';
		if(!empty($field)){
			$strField = 'c.'.implode(', c.', $field);
		}
		$sql = "SELECT $strField FROM ".T_GIFT_RECEIVER. " AS c $strInnerJoin $strWhere ORDER BY c.id DESC";
		$re = $per_page? Pagging::pager_query($sql, $per_page) : DB::query($sql);
		if ($re) {
			while ($r = @mysql_fetch_assoc($re)) {
				$r['image'] = ImageUrl::getImageURL($r['image'], $r['created'], 80, 'receiver', 'receiver/');
	    		$r['del'] = Url::build_current(array('cmd' => 'receiver', 'action' => 'delete', 'id' => $r['id']));
	    		$r['edit'] = Url::build_current(array('cmd' => 'receiver', 'action' => 'edit', 'id' => $r['id']));
				$items[$r['id']] = $r;
			}
		}

		return $items;
	}

	static function getByID($arrReceiverID = array(), $field = array()){
		$strField = '*';
		if(!empty($field)){
			$strField = implode(', ', $field);
		}

		$items = array();
		$sql = "SELECT $strField FROM ".T_GIFT_RECEIVER." WHERE id IN(".implode(', ', $arrReceiverID).") ORDER BY id ASC";
		$re = DB::query($sql);
		if($re){
			while ($r = @mysql_fetch_assoc($re)) {
				$items[$r['id']] = $r;
			}
		}
		return $items;
	}

	static function delete($id){
		$item = self::get($id);
		if($item){
			$status = abs($item['status'] - 1);
			DB::update(T_GIFT_RECEIVER, array('status' => $status), "id = $id");
		}

		return true;
	}

	static function getWhere($params = array()){
		$where = array();
        $arrKeys = array('id', 'first_name','last_name', 'phone', 'email', 'status');
		foreach ($arrKeys as $key => $value) {
			if (isset($params[$value])) {
				if(gettype($params[$value]) == 'string') {
					if($params[$value] != ''){
						$where[] = $value. " LIKE '%".$params[$value]."%'";
					}
                }
                elseif($params[$value] != -1) $where[] = $value. " = ".$params[$value];
			}
		}
		return $where;
	}

    static function add($item){

        #Them dieu kien Phone
        if (isset($item['phone']) && $item['phone'] != '') {
            $existed = DB::fetch("SELECT * FROM " . T_GIFT_RECEIVER . " WHERE phone = '" . $item['phone'] . "'");
            if ($existed) {
				$update = [];
				if((empty($existed['fullname']) || FunctionLib::is_phone($existed['fullname'])) && isset($item['fullname'])){
					$update['fullname'] = $item['fullname'];
				}
				if(empty($existed['city_id']) && isset($item['city_id'])){
					$update['city_id'] = @$item['city_id'];
				}
				if(empty($existed['district_id']) && isset($item['district_id'])){
					$update['district_id'] = @$item['district_id'];
				}
				if(empty($existed['ward']) && isset($item['ward'])){
					$update['ward'] = @$item['ward'];
				}
				if(empty($existed['email']) && isset($item['email'])){
					$update['email'] = @$item['email'];
				}
				if(empty($existed['address']) && isset($item['address'])){
					$update['address'] = @$item['address'];
				}
				if(!empty($update)){
					DB::update(T_GIFT_RECEIVER,$update,"id={$existed['id']}");
				}
                return $existed;
            }
        }elseif (isset($item['email']) && $item['email'] != '') {
            $item['email'] = strtolower($item['email']);
            $existed = DB::fetch("SELECT * FROM " . T_GIFT_RECEIVER . " WHERE email = '" . $item['email'] . "'");
            if ($existed) {
                return $existed;
            }
        }

        if(isset($item['phone']) && $item['phone'] != '' && isset($item['email']) && $item['email'] != '' && $item['email'] == $item['phone'].'@urbox.vn'){
            $item['email'] = '';
        }
        $item['created'] = TIME_NOW;
        $id = DB::insert(T_GIFT_RECEIVER, $item);
        return self::get($id);

    }
	static function addOrUpdate($item){
		#Them dieu kien Phone
		if (isset($item['phone']) && $item['phone'] != '') {
			$existed = DB::fetch("SELECT * FROM " . T_GIFT_RECEIVER . " WHERE phone = '" . $item['phone'] . "'");
			if ($existed) {
				unset($item['phone']);
				DB::update(T_GIFT_RECEIVER,$item,"id={$existed['id']}");
				return $existed;
			}
		}
		
		if(isset($item['phone']) && $item['phone'] != '' && isset($item['email']) && $item['email'] != '' && $item['email'] == $item['phone'].'@urbox.vn'){
			$item['email'] = '';
		}
		$item['created'] = TIME_NOW;
		$id = DB::insert(T_GIFT_RECEIVER, $item);
		return self::get($id);
		
	}
	
	// lưu thông tin người thụ hưởng
	static function addBeneficiary($item){
		if(isset($item['phone']) && $item['phone'] != '' && isset($item['email']) && $item['email'] != '' && $item['email'] == $item['phone'].'@urbox.vn'){
			$item['email'] = '';
		}
		$item['created'] = TIME_NOW;
		$id = DB::insert(T_BENEFICIARY, $item);
		$data = DB::fetch("select * from " . T_BENEFICIARY . " WHERE id={$id}");
		return $data;
		
	}
    static function getAddress($id){
        return DB::fetch("SELECT * FROM ".T_ADDRESS." WHERE id = $id LIMIT 1");
    }
	static function saveAddress($entity,$nocard = false)
	{
		if (empty( $entity )|| $entity['city_id'] <= 0 || $entity['ward_id'] <= 0 || $entity['district_id'] <= 0 || $entity['receiver_id'] <= 0) return [];
		if($entity['card_id'] <= 0 && $nocard == false){
			return [];
		}
		$existed = DB::fetch("SELECT id FROM " . T_ADDRESS . " WHERE city_id = {$entity['city_id']} and ward_id = {$entity['ward_id']} and district_id = {$entity['district_id']} and receiver_id = {$entity['receiver_id']} and number = '{$entity['number']}' and card_id={$entity['card_id']}");
        if ($existed) {
            return $existed;
        }

        $id = DB::insert(T_ADDRESS, $entity);
        return self::getAddress($id);
    }
    static function listAddress($card_id){
        $s = "SELECT b.id,a.phone,a.email,b.number,c.title as province ,d.title as district,e.title as ward,if(a.fullname ='',a.first_name,a.fullname) as fullname
								FROM " . T_GIFT_RECEIVER . " a
                LEFT JOIN ". T_ADDRESS ." b on a.id=b.receiver_id
                LEFT JOIN ". T_PROVINCE ." c on c.id=b.city_id
                LEFT JOIN ". T_DISTRICT ." d on d.id=b.district_id
                LEFT JOIN ". T_WARD ." e on e.id=b.ward_id
                where b.card_id=$card_id ORDER BY id desc
        ";
        return DB::query($s)->fetchAll(PDO::FETCH_ASSOC);
    }
    static function viewAddress($id){
        $s = "SELECT a.fullname,a.phone,a.email,b.number,c.title as province ,d.title as district,e.title as ward FROM " . T_GIFT_RECEIVER . " a
                LEFT JOIN ". T_ADDRESS ." b on a.id=b.receiver_id
                LEFT JOIN ". T_PROVINCE ." c on c.id=b.city_id
                LEFT JOIN ". T_DISTRICT ." d on d.id=b.district_id
                LEFT JOIN ". T_WARD ." e on e.id=b.ward_id
                where b.id=$id
        ";
        return DB::query($s)->fetch(PDO::FETCH_ASSOC);
    }
	
	/*Bảo hiểm*/
	static function getCartByToken($token){
		if(empty($token)) return [];
		$enc_token = System::encrypt($token);
		$sql = "SELECT c.id,customer_id, receiver_id, cart_id, gift_detail_id,c.gift_id,c.price,c.app_id,gd.type,delivery,using_time
				FROM ". T_CART_DETAIL . " c
				LEFT JOIN ".T_GIFT_DETAIL." gd on c.gift_detail_id=gd.id
				WHERE receive_code='{$enc_token}'
				AND  c.status=2 and pay_status=2
				
		"
		;
		//AND gd.type=".GIFT_TYPE_RECEIVER
		$cart = DB::fetch($sql);//582
		if(!empty($cart))
			$cart['token'] = $token;
		return $cart;
	}
	static function updateCart($token)
	{
		$cart = self::getCartByToken($token);
		if (empty($token) || empty($cart)) return [];
		$id = $cart['id'];
		if (!empty( $cart ) && $cart['id'] > 0) {
			$gift_code = DB::fetch( "SELECT id from " . T_GIFT_CODE . " where cart_detail_id={$cart['id']} and status=1 LIMIT 1" );
			if (!empty( $gift_code ) && $gift_code['id'] > 0) {
				$newRow = array('active' => 2, 'used' => TIME_NOW,'apiCall'=> 14);
				$code = DB::update( T_GIFT_CODE, $newRow, "id={$gift_code['id']}" );
				//if ($code && $cart) return true;
			}
			$newCartRow = array(
				'using_time' => TIME_NOW,
				'delivery' => 3,
			);
			$cart = DB::update( T_CART_DETAIL, $newCartRow, " id =" . $id );
			
			return false;
		}
	}
	static function createOrderTopup($entity = []){
		return DB::insert(T_ORDER_TOPUP, $entity);
	}
	static function getBannerGift($id,$parent = 0){
		if($id <=0) return [];
		if($parent == 0) {
			$gift = DB::fetch( "SELECT id,avatar,title,gift_id from " . T_GIFT_DETAIL . " WHERE id=$id" );
		}else{
			//dùng cho module Gift- chưa dùng
			$gift = DB::fetch( "SELECT id,avatar,title,gift_id from " . T_GIFT_DETAIL . " WHERE id=$id and gift_id=$parent" );
		}
		
		if(!empty($gift)) {
			$c_image = MediaUrl::fromImageTitle( $gift['avatar'], [640] );
			$image = isset( $c_image[640] ) ? $c_image[640] : '';
			$gift['image'] = $image;
			$gift['content'] = "";
			if (isset( $gift['gift_id'] )) {
				$gift['content'] = DB::query( "SELECT note from " . T_GIFT_CONTENT . " WHERE gift_id='{$gift['gift_id']}' and status = 2 order by id limit 1" )->fetch( PDO::FETCH_COLUMN );
			}
			$gift['content'] = StringLib::post_db_parse_html( $gift['content'] );
			$gift['parent'] = DB::query("SELECT id,name from ". T_GIFT . " WHERE id='{$gift['gift_id']}'")->fetch(PDO::FETCH_ASSOC);
		}
		return $gift;
	}
	static function getDistrict($city_id = 0){
		if($city_id > 0){
			return DB::query("SELECT * FROM " . T_DISTRICT . " WHERE city_id={$city_id} and status=2")->fetchAll(PDO::FETCH_ASSOC);
		}
		return  [];
	}
	static function getWard($district_id = 0){
		if($district_id > 0){
			return DB::query("SELECT * FROM " . T_WARD . " WHERE district_id={$district_id} and status=2")->fetchAll(PDO::FETCH_ASSOC);
		}
		return  [];
	}
}
?>