<?php

class FunctionLib
{
    static $check_uri = false;

    static function check_uri(&$query_string = '')
    {
        if (!self::$check_uri) {
            $uri = $_SERVER['REQUEST_URI'];

            $query_string = $_SERVER['QUERY_STRING'] ? ('?' . $_SERVER['QUERY_STRING']) : '';
            $dir = (dirname($_SERVER['SCRIPT_NAME']) ? dirname($_SERVER['SCRIPT_NAME']) : '');
            $dir = str_replace('\\', '/', $dir);
            if ($dir && $dir != '/' && $dir != './') {
                if ($dir[0] != '/') {
                    $dir = '/' . $dir;
                }
                $dir .= ($dir[strlen($dir) - 1] != '/' ? '/' : '');
                $query_string = str_replace($dir, '', $uri);
            } else {
                if (strlen($uri) > 1) {
                    while ($uri[0] == '/') {
                        $uri = substr($uri, 1, strlen($uri) - 1);
                    }
                    $query_string = $uri;
                    unset($uri);
                } else {
                    $query_string = '';
                }
            }
            self::$check_uri = true;
        }
    }

    static function is_email($email = '')
    {
        return preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/", strtolower(trim($email)));
    }

    static function is_phone($value = '')
    {
        return preg_match('#^(0|\+[1-9]{2}|\+[1-9]{1})(1[0-9]{1}|3|5|7|8|9)(\d{8})$#', $value);
    }

    static function isUrlString($str = '')
    {
        return (bool)preg_match("#^[a-zA-Z][0-9-_a-zA-Z]*$#", $str);
    }

    /**
     * Kiem tra xem mat khau co phai la mat khau qua don gian, de bi nguoi khac doan khong<br />
     * Vi du: 123456, abcdef la cac mat khau khong an toan
     * @param string $password
     * @return boolean TRUE - Neu mat khau khong an toan
     */
    static function isNotSafePassword($password)
    {
        $listNotSafePassword = 'abc123,abc@123,123456,qwerty,qazwsx,1234567,12345678,123456789,1234567890,0123456789,654321,0123456,123abc,abcdef,qwertyuiop,1qaz2wsx,password,111111,iloveyou,123123,ashley,bailey,baseball,dragon,football,letmein,master,michael,monkey,passw0rd,shadow,sunshine,superman,trustno1';
        $isNotSafe = false;
        if (stripos(',' . $listNotSafePassword . ',', ',' . $password . ',') !== false) {
            $isNotSafe = true;
        }
        return $isNotSafe;
    }

    /*--------------------------------------------------------------------------*/
    /* 							FORMATER FUNCTIONS								*/
    /*--------------------------------------------------------------------------*/

    static function numberFormat($number = 0)
    {
        if ($number >= 1000) {
            return number_format($number, 0, ',', '.');
        }
        return $number;
    }

    static function priceFormat($price = 0, $currency = '')
    {
        if ($currency == '') {
            $currency = CGlobal::$currency;
        }
        return self::numberFormat($price) . " $currency";
    }

    static function dateFormat($time = TIME_NOW, $format = 'd/m - H:i', $vietnam = false)
    {
        $return = date($format, $time);
        if ($vietnam) {
            $days = array('Mon' => 'Thứ 2', 'Tue' => 'Thứ 3', 'Wed' => 'Thứ 4', 'Thu' => 'Thứ 5', 'Fri' => 'Thứ 6', 'Sat' => 'Thứ 7', 'Sun' => 'Chủ nhật');
            $return = date('H:i - ', $time) . $days[date('D', $time)] . ', ngày ' . date('d/m/Y', $time);
        }
        return $return;
    }

    //duration time
    static function duration_time($time)
    {
        $time = TIME_NOW - $time;

        if ($time > 0) {
            if ($time > (365 * 86400)) {
                return floor($time / (365 * 86400)) . ' năm trước';
            }

            if ($time > (30 * 86400)) {
                return floor($time / (30 * 86400)) . ' tháng trước';
            }

            if ($time > (7 * 86400)) {
                return floor($time / (7 * 86400)) . ' tuần trước';
            }
            if ($time > 86400) {
                return floor($time / (86400)) . ' ngày trước';
            }

            if ($time > 3600) {
                return floor($time / (3600)) . ' giờ trước';
            }

            if ($time > 60) {
                return floor($time / (60)) . ' phút trước';
            }
        }
        return ' vài giây trước';
    }

    /*--------------------------------------------------------------------------*/
    /* 							SOME EXTRA FUNCTION								*/
    /*--------------------------------------------------------------------------*/
    static function empty_all_dir($name, $remove_sub_dir = false, $remove_self = false)
    {
        if (is_dir($name)) {
            if ($dir = opendir($name)) {
                $dirs = array();
                while ($file = readdir($dir)) {
                    if ($file != '..' and $file != '.') {
                        if (is_dir($name . '/' . $file)) {
                            $dirs[] = $file;
                        } else {
                            @unlink($name . '/' . $file);
                        }
                    }
                }
                closedir($dir);
                foreach ($dirs as $dir_) {
                    self::empty_all_dir($name . '/' . $dir_, ($remove_self || $remove_sub_dir), ($remove_self || $remove_sub_dir));
                }
                if ($remove_self) {
                    @rmdir($name);
                }
            }
        }
    }

    static function addCondition($condition = array(), $where = false, $operator = 'AND')
    {
        if (!empty($condition)) {
            $numCondition = count($condition);
            $newCondition = array();
            foreach ($condition as $k => $c) {
                if (strpos($c, ' = ') > 0) {
                    $newCondition[$k] = $c;
                } else {
                    $newCondition[$numCondition] = $c;
                    $numCondition++;
                }
            }
            $c = implode(' ' . $operator . ' ', $newCondition);
            if ($where && $c) {
                $c = ' WHERE ' . $c;
            }
            return $c;
        }
        return '';
    }

    static function tokenData()
    {
        if (CGlobal::$tokenData == '') {
            CGlobal::$tokenData = md5(session_id() . TOKEN_SECRET);
        }
        return CGlobal::$tokenData;
    }

    static function trimID($strID, $character = ',')
    {
        $strID = trim($strID, $character);
        $strID = preg_replace("/$character+/", $character, $strID);
        $strID = preg_replace("/[^0123456789,]/", '', $strID);
        $arrID = explode($character, $strID);
        foreach ($arrID as $k => $v) {
            $v = (int)$v;
            if (trim($v) == '' || $v == 0) unset($arrID[$k]);
        }
        $strID = implode(",", array_unique($arrID));
        return $strID;
    }

    /**
     * Get either a Gravatar URL or complete image tag for a specified email address.
     *
     * @param string $email The email address
     * @param string $s Size in pixels, defaults to 80px [ 1 - 2048 ]
     * @param string $d Default imageset to use [ 404 | mm | identicon | monsterid | wavatar ]
     * @param string $r Maximum rating (inclusive) [ g | pg | r | x ]
     * @param boole $img True to return a complete IMG tag False for just the URL
     * @param array $atts Optional, additional key/value attributes to include in the IMG tag
     * @return String containing either just a URL or a complete image tag
     * @source http://gravatar.com/site/implement/images/php/
     */
    static function get_gravatar($email = '', $s = 80, $d = 'identicon', $r = 'g', $img = false, $atts = array())
    {
        $url = 'http://www.gravatar.com/avatar/';
        $url .= md5(strtolower(trim($email)));
        $url .= "?s=$s&d=$d&r=$r";
        if ($img) {
            $url = '<img src="' . $url . '"';
            foreach ($atts as $key => $val)
                $url .= ' ' . $key . '="' . $val . '"';
            $url .= ' />';
        }
        return $url;
    }

    static function aasort($array, $key, $dimention = false)
    {
        $sorter = array();
        $ret = array();
        reset($array);
        foreach ($array as $ii => $va) {
            $sorter[$ii] = $va[$key];
        }
        if ($dimention) asort($sorter);
        else arsort($sorter);
        foreach ($sorter as $ii => $va) {
            $ret[$ii] = $array[$ii];
        }
        return $ret;
    }

    static function getFirstKey($arr = array())
    {
        reset($arr);
        return key($arr);
    }

    static function getFirstElement($arr = array())
    {
        if (!empty($arr)) {
            $key = FunctionLib::getFirstKey($arr);
            return $arr[$key];
        }
    }

    static function getIndex($arr = array(), $id = 0)
    {
        foreach ($arr as $k => $v) {
            if ($v['id'] == $id) {
                return $k;
            }
        }
        return 0;
    }

    static function strToTimestamp($str = '', $format = 'd/m/Y H:i')
    {
        try {
            $datetime = DateTime::createFromFormat($format, $str);
            return $datetime ? $datetime->getTimestamp() : TIME_NOW;
        } catch (Exception $e) {
            return TIME_NOW;
        }
    }

    static function folderToString($strFolder = '[80,160,320,640,"square"]')
    {
        $jsonFolder = json_decode($strFolder);
        return implode($jsonFolder, ',');

    }

    static function ip_first($ips)
    {
        if (($pos = strpos($ips, ',')) != false) {
            return substr($ips, 0, $pos);
        }
        return $ips;
    }

    static function ip_valid($ips)
    {
        if (isset($ips)) {
            $ip = self::ip_first($ips);
            $ipnum = ip2long($ip);
            if ($ipnum !== -1 && $ipnum !== false && (long2ip($ipnum) === $ip)) { // PHP 4 and PHP 5
                if (($ipnum < 167772160 || $ipnum > 184549375) && // Not in 10.0.0.0/8
                    ($ipnum < -1408237568 || $ipnum > -1407188993) && // Not in **********/12
                    ($ipnum < -1062731776 || $ipnum > -1062666241))   // Not in ***********/16
                    return true;
            }
        }
        return false;
    }

    static function ip()
    {
        $check = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_REAL_IP',
            'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED',
            'HTTP_VIA', 'HTTP_X_COMING_FROM', 'HTTP_COMING_FROM');
        foreach ($check as $c) {
            if (isset($_SERVER[$c]) && self::ip_valid($_SERVER[$c])) {
                return self::ip_first($_SERVER[$c]);
            }
        }
        return $_SERVER['REMOTE_ADDR'];
    }


    static function getConfigLang()
    {
        $key = 'all-site-configs';
        $time = 31536000;
        $dir = 'configs/';
        $configs = array();
        $key_need = array(
            'lang'
        );
        $key_need = implode("','", $key_need);

        $cached = CacheLib::get($key, $time, $dir);
        if (empty($cached)) {
            $cached = DB::fetch_all("SELECT * FROM " . T_CONFIGS . " WHERE conf_key IN ('$key_need')");
            if (!empty($cached)) {
                CacheLib::set($key, $cached, $time, $dir);
            }
        }
        if (!empty($cached)) {
            foreach ($cached as $v) {
                $configs[$v['conf_key']] = $v['conf_val'];
            }
        }
        return $configs;
    }

    static function hiddenID($id = 0, $decode = false)
    {
        if ($decode) {
            $id = (1984 - 13 * 12) + $id;
        } else {
            $id = (13 * 12 - 1984) + $id;
        }
        return $id;
    }

    static function roundUpToAny( $n, $x = 5 ) {
        $round = round(($n+$x/2)/$x)*$x;
        if($round - $x == $n){
            return $n;
        }else{
            return $round;
        }
    }

    /**
     * Generate Hash Code By Random Character and Microtime
     *
     * @params int $number      Quantity Charactors return | Max 32
     *
     * @return string Hash Code
     */
    static function generateHashCode($number = 10){
        if($number < 1){
            $number = 1;
        }
        if($number > 32){
            $number = 32;
        }
        $characters = str_split('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789');
        shuffle($characters);
        $randomChar = '';
        foreach (array_rand($characters, $number) as $key) {
            $randomChar .= $characters[$key];
        }
        $hashCode = md5(microtime() . $randomChar);
        return substr($hashCode, 0,  $number);
    }
    
	static function isIpBlack(){
    	return true;
		$ip = FunctionLib::ip();
		$blackIp = URedis::hget("DB:Mongo:ip:black",$ip,0);
		if($blackIp){
			echo '<!DOCTYPE html>';
			echo '<html lang="vi" xmlns="http://www.w3.org/1999/xhtml" class="ios device-pixel-ratio-2 device-retina device-desktop device-macos support-position-sticky"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
			echo '<title>Home Page</title>';
			echo '<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">';
			echo '<meta http-equiv="EXPIRES" content="0">';
			echo '<meta http-equiv="X-UA-Compatible" content="IE=edge">';
			echo '<meta name="RESOURCE-TYPE" content="DOCUMENT">';
			echo '<meta name="DISTRIBUTION" content="GLOBAL">';
			echo '<meta name="AUTHOR" content="UrBox">';
			echo '<meta name="KEYWORDS" content="">';
			echo '<link href="https://page.urbox.vn/website/style/favicon.ico" rel="shortcut icon">';
			echo '<meta name="ROBOTS" content="INDEX, NO-FOLLOW">';
			echo '<body style="background-color:#F4F6F8">';
			echo '<div style="text-align: center;margin-top: 20px;">';
			echo '<div style="margin-bottom: 20px;"><img src="https://page.urbox.vn/website/style/home/<USER>/logo.svg" alt=""></div>';
			echo '<div style="margin-bottom: 20px;"><img src="https://page.urbox.vn/website/style/home/<USER>/image_Check.svg" alt=""></div>';
			echo '<div style="font-size: 32px;">Rất tiếc!</div>';
			echo '<div style="font-size: 17px; margin-bottom: 5px;">Thẻ của bạn đang tạm khóa để kiểm tra an toàn bảo mật</div>';
			echo '<div style="font-size: 17px;">Vui lòng gọi đến hotline <a href="tel:'.UB_CUSTOMER_PHONE_VALUE.'" class="external color-blue1">'.UB_CUSTOMER_PHONE.'</a> để được hỗ trợ</div>';
			echo '<div style="font-size: 12px;">CurrentIP '.System::ip().'</div>';
			echo '</div>';
			echo '</body>';
			echo '</html>';
			exit();
			#exit('<h1>ERR_TOO_MANY_REDIRECTS</h1>');
		}
	}
	static function saveLogAccess(\Slim\Http\Request $request,$response){
		$postBody = $request->getParsedBody();
		$getPath = $request->getUri()->getPath();
		$getParams = $request->getUri()->getQuery();
		$getHeader = $request->getHeaders();
		$header = [
			"HTTP_COOKIE" => isset($getHeader['HTTP_COOKIE'][0])?$getHeader['HTTP_COOKIE'][0]:"",
			"HTTP_USER_AGENT" => isset($getHeader['HTTP_USER_AGENT'][0])?$getHeader['HTTP_USER_AGENT'][0]:"",
			"CONTENT_TYPE" => isset($getHeader['CONTENT_TYPE'][0])?$getHeader['CONTENT_TYPE'][0]: "",
			"HTTP_HOST" => $getHeader['HTTP_HOST'][0],
		];
		$body = [
			"post" => !empty($postBody) ? $postBody: "",
			"get" => !empty($getPath) ? $getPath: "",
			"params" => !empty($getParams) ? $getParams: ""
		];
		$entity = [
			"appName" => PAGE_NAME,
			"link" => $header["HTTP_HOST"] . $getPath,
			"request" => json_encode($body),
			"response" => json_encode($response),
			"header" => json_encode($header)
		];
		LogApp::saveLog($entity);
 
	}
	static function generateStr($number = 10){
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		
		for ($i = 0; $i < $number; $i++) {
			$index = rand(0, strlen($characters) - 1);
			$randomString .= $characters[$index];
		}
		
		return $randomString;
	}
	static function sent_network($phone = ''){
		$keyNetW = md5(T_SMS_NETWORK);
		$phoneValidate = CacheLib::get($keyNetW);
		if(empty($phoneValidate)){
			$letWork = DB::fetch_all("SELECT * FROM ".T_SMS_NETWORK);
			if(!empty($letWork)){
				foreach ($letWork as $lwork){
					$exp = explode(',',$lwork['prefix']);
					foreach ($exp as $fP){
						$phoneValidate[$fP] = $lwork['id'];
					}
				}
			}
			CacheLib::set($keyNetW,$phoneValidate);
		}
		#Chuyển đúng định dạng số điện thoại
		$dauso0 = substr($phone,0,1);
		$dauso84 = substr($phone,0,2);
		$cuoiso84 = substr($phone,2);
		if($dauso0 != '0'){
			if($dauso0=='+'){
				$dausoC84 = substr($phone,0,3);
				if($dausoC84=='+84'){
					$cuoisoC84 = substr($phone,3);
					$phone = '0'.$cuoisoC84;
				}
			}else{
				if($dauso84 == 84){
					$phone = '0'.$cuoiso84;
				}else{
					$phone = '0'.$phone;
				}
			}
			
		}
		#Lay dau so ra kiem tra
		$dauso0 = substr($phone,0,3);
		if(isset($phoneValidate[$dauso0])){
			return $phoneValidate[$dauso0];
		}else{
			return 0;
		}
		
	}
}