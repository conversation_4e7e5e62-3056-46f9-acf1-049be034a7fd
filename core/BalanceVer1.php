<?php


class BalanceVer1 extends Balance
{
    static function cardIssue(){

    }

    static function getCatId($id = 0){
        if($id == 0 || $id == null) return [];
        $sql = "SELECT id FROM " . T_CATEGORY . " WHERE id NOT IN (" . implode(CGlobal::$catNotShow, ',') . ") AND status=1 AND parent_id=" . $id;
        $stmt = DB::query($sql);
        $data = [];
        array_push($data,$id);
        $cats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($cats as $cat) {
            array_push($data,$cat['id']);
        }
        return $data;
    }
    static function getApp($card){
        $app = DB::fetch("SELECT * FROM ". T_APP . " WHERE id=". $card['app_id']);
        $issue = DB::fetch("SELECT image FROM ". T_CARD_ISSUE . " WHERE id=". $card['issue_id']);
        $image = "";
        if(!empty($issue) && !empty($issue['image'])){
            $c_image = MediaUrl::fromImageTitle($issue['image'], []);
            $image = isset($c_image[0]) ? $c_image[0] : '';
        }
        if(!empty($app)) {
            $logo = MediaUrl::fromImageTitle($app['logo'], [320]);
            $app['logo'] = isset($logo[320]) ? $logo[320] : '';
            if($image == "") {
                $banner = MediaUrl::fromImageTitle($app['banner'], []);
                $image = isset($banner[0]) ? $banner[0] : '';
            }
            $app['image'] = $image;
            return $app;
        }
        return false;
        return [
            'logo' =>'',
            'image' =>$image
        ];
    }
    static function getGiftCard($token = null)
    {
		if($token == "") return false;
		$encrypt_token = System::encrypt($token,CRYPT_KEY);
        $s = DB::fetch("SELECT issue_id FROM " . T_CARD . " WHERE token='{$encrypt_token}' LIMIT 1");
        $sql = "SELECT image FROM " . T_CARD_ISSUE . "  WHERE  id='{$s['issue_id']}' LIMIT 1";
        $giftSet = DB::fetch($sql);
        $data['image'] = MediaUrl::fromImageTitle($giftSet['image']);
        $data['id'] = 1;
        return $data;
    }

    /**
     * @param $token int
     * @param $brand_id int
     * @return  array|null
     * */
    static function getGiftByBrand($token, $brand_id = null)
    {
		if($token == "") return false;
        $param = [];
        $param[] = 'code_quantity > 0';
        $param[] = 'status=1';
		$param[] = 'type<>3';
        $param[] = 'a.type<>8';
        $param[] = 'isUnfixPrice='. IS_OFF;
        //nếu lọc theo brand
        if ($brand_id > 0) {
            $param[] = " brand_id={$brand_id}";
            $order = " price asc";
        }

        $brand = self::getBrand($brand_id);
        if(empty($brand)) return [];
        $giftId2 = array();
        if($brand['online'] == IS_OFF) {
            $access_loc = self::getAccessLoc();
            if ($access_loc != 1) {
                //nếu ko cho phep truy cập vị trí
                $city_id = CookieLib::get("MY_CITY");
                if ($city_id != 0) {
                    $where_c = [];
                    if ($city_id == -1) {
                        // tinhr thanh khac
                        $provide = self::allProvide(true);
                        $provide_id = array_column($provide, 'id');
                        if (count($provide_id) > 0) {
                            $where_c[] = "  city_id NOT IN(" . implode(',', $provide_id) . ")";
                        }
                    } elseif ($city_id != 0) {
                        $where_c[] = " (city_id={$city_id} OR city_id=0)";
                    }

                    $sql_city = "SELECT gift_detail_id FROM " . T_GIFT_CITY;
                    $where_condition = FunctionLib::addCondition($where_c, true);
                    $sql_city .= $where_condition;
                    $stmt = DB::query($sql_city);
                    if ($stmt) {
                        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                            $giftId2[$row['gift_detail_id']] = $row['gift_detail_id'];
                        }
                    }
                }
            }
        }
        $card = Card::getByToken($token);
        if($card['app_id'] == 0) $card['app_id'] = 34;
        if($card['gift_id'] == 0){
            $app = Balance::getApp($card);
            $card['gift_id'] = $app['gift_id'];
        }
        System::$data['card'] = $card;

        $id_gift_set = isset(System::$data['card']['gift_id']) && System::$data['card']['gift_id'] > 0 ? System::$data['card']['gift_id'] : 0;
        $gift_lm = [];
        $gift_ids = [];
        if($id_gift_set  <=0){
            $s = "SELECT gift_detail_id FROM " . T_GIFT_LIMIT . " WHERE type=2 and status=".IS_ON." AND app_id =" . System::$data['card']['app_id'];
            $stmt = DB::query($s);
            $data_limit = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if(!empty($data_limit)) {
                $gift_lm = array_column($data_limit, 'gift_detail_id');
            }
        }else{
            $s1 = "SELECT gift_detail_id FROM ". T_GIFT_SET . " WHERE gift_id={$id_gift_set}";
            $stmt = DB::query($s1);
            $data_gift_id = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if(!empty($data_gift_id)) {
                $gift_ids = array_column($data_gift_id, 'gift_detail_id');
            }
        }
        if(!empty($giftId2) && $brand['online'] == IS_OFF) { // nếu tìm kiếm theo thành phố
            $giftId3=[];
            if(!empty($gift_lm)){
                $giftId3 = array_diff($giftId2,$gift_lm);
            }
            if (!empty($giftId3)) {
                $param[] = " id IN (" . implode($giftId3, ',') . ")";
            } else {
               // $param[] = " id = 0 ";
            }
        }else{ //nếu là bán hàng online
            if(!empty($gift_lm))
                $param[] = " id NOT IN (" . implode($gift_lm, ',') . ")";
        }
        if($gift_ids != null)
        $param[] = " id IN (" . implode($gift_ids, ',') . ")";
        $condition = FunctionLib::addCondition($param, true);
        $sql = "SELECT id,gift_id,title,price,avatar FROM " . T_GIFT_DETAIL . $condition . " ORDER BY $order";
        $re = DB::query($sql);
        $data = array();
        if ($re ) {
            while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($row['avatar'],[160]);
                $temp = array(
                    'id' => $row['id'],
                    'gift_id' => $row['gift_id'],
                    'title' => $row['title'],
                    'price' => $row['price'],
                    'link' => '/card/' . $token . '/' . $row['id'],
                    'image' => isset($images[160]) ? $images[160] : ''
                );
                $data[$row['id']] = $temp;
            }
        }
        return $data;
    }

    static function getBrandGiftSet($token = null)
    {
        if ($token == null) return false;

        $card = Card::getByToken($token);
        if($card['app_id'] == 0) $card['app_id'] = 34;
        if($card['gift_id'] == 0){
            $app = Balance::getApp($card);
            $card['gift_id'] = $app['gift_id'];
        }
        System::$data['card'] = $card;

        $param = [];
        $param[] = 'a.code_quantity > 0';
        $param[] = 'a.status=1';
        $param[] = 'a.isUnfixPrice='.IS_OFF;
        // gift
        $param[] = 'b.status in (1,2)';
        $id_gift_set = isset(System::$data['card']['gift_id']) && System::$data['card']['gift_id'] > 0 ? System::$data['card']['gift_id'] : 0;
        if($id_gift_set > 0) {
            $getSiteGift = DB::query("SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE status=". IS_ON ." and gift_id=" . $id_gift_set);
            if ($getSiteGift) {
                $id_site_gift = $getSiteGift->fetchAll(PDO::FETCH_ASSOC);
                $id_gift_detail = array_column($id_site_gift, 'gift_detail_id');
            }
            if (!empty($id_gift_detail)) {
                $param[] = " a.id IN (" . implode(',', $id_gift_detail) . ")";
            }
        }else{

            $s = "SELECT * FROM " . T_GIFT_LIMIT . " WHERE status=".IS_ON." AND app_id =" . System::$data['card']['app_id'];
            $stmt = DB::query($s);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $gift_gift = [];
            $gift_brand = [];
            foreach ($data as $item){
                if($item['type'] == 2){
                    array_push($gift_gift,$item['gift_detail_id']);
                }else{
                    array_push($gift_brand,$item['brand_id']);
                }
            }
            if(count($gift_gift) > 0)
                $param[] = "a.id NOT IN (" . implode(",", $gift_gift) . ")";
            if(count($gift_brand) > 0)
                $param[] = " a.brand_id NOT IN (" . implode(",", $gift_brand) . ")";

        }


        $condition = FunctionLib::addCondition($param, true);
        $sql = "SELECT a.brand_id,a.id FROM " . T_GIFT_DETAIL . " a LEFT JOIN ". T_GIFT ." b on a.gift_id=b.id " .  $condition . " GROUP BY a.brand_id ";
        $re = DB::query($sql);
        $brands = $re->fetchAll(PDO::FETCH_ASSOC);
        return $brands;
    }

    static function setBrandToCache($id)
    {
    	$id = (int) $id;
		if($id <= 0) return false;
        $brand_json = CookieLib::get('c_viewed_brands');
        $brand_ids = [];
        if (!empty($brand_json)) {
            $brand_ids = json_decode($brand_json, true);
        }
        if (!in_array($id, $brand_ids)) array_push($brand_ids, $id);
        $expire = time() + (86400 * 7);
        CookieLib::set('c_viewed_brands', json_encode($brand_ids), $expire);
    }

    static function removeCacheBrand($id)
    {
		$id = (int) $id;
		if($id <= 0) return false;
        $brand_json = CookieLib::get('c_viewed_brands');

        $brand_ids = [];
        if (!empty($brand_json)) {
            $brand_ids = json_decode($brand_json, true);
        }
       $index =  array_search($id,$brand_ids);
        unset($brand_ids[$index]);
        $expire = time() + (86400 * 7);
        CookieLib::set('c_viewed_brands', json_encode($brand_ids), $expire);
    }

    static function getBrandFromCache()
    {
        $brand_json = CookieLib::get('c_viewed_brands');
        if (empty($brand_json)) return null;
        $brand_ids = json_decode($brand_json, true);
        $brand_ids = array_filter($brand_ids);
        $brand_id = implode(",", $brand_ids);
        $data = [];
        if (count($brand_ids) > 0) {
            $sql = "SELECT id,title,logo FROM " . T_BRAND . " WHERE id in ({$brand_id}) and status=" . IS_ON;
            $stmt = DB::query($sql);
            $brands = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($brands as $brand) {
                $images = MediaUrl::fromImageTitle($brand['logo'],[160]);
                $brand['image'] = isset($images[160]) ? $images[160] : '';
                $data[] = $brand;
            }
        }
        return $data;
    }

    static function listBrands($cat = 0, $paging = true, $token = null, $row_per_page = 12, $page_no = 1, $group_by = false, $keyword = "")
    {
        $access_loc = self::getAccessLoc();
        $my_loc = self::getCurrentLoc();
        $brand = self::getBrandGiftSet($token);
        $brands = array_column($brand, 'brand_id');
        $brands = array_filter($brands);
        // nếu cho phép truy cập vi trí
        if ($access_loc == 1 && count($my_loc) == 2) {
            $data = self::allBrandByLoc($brands, $cat, $paging, $row_per_page, $page_no, $group_by, $keyword);
        } else {
            $data = self::allBrand($brands, $cat, $paging, $row_per_page, $page_no, $group_by, $keyword);
        }
        // remake image brand
        foreach ($data as $k => &$item) {
            if (isset($data[$k - 1]) && $data[$k - 1]['id'] == $item['id']) {
                $item['image'] = $data[$k - 1]['image'];
            } else {
                $images = MediaUrl::fromImageTitle($item['logo'],[160]);
                $item['image'] = isset($images[160]) ? $images[160] : '';
            }
        }

        return $data;
    }

    /**
     * @function trả về danh sách brand khi người dùng ko cho phép truy cập vị trí
     * @param $brands array Lấy brand id
     * @param $cat int Lấy brand category
     * @param $group_by bool có groupby ko, có khi lấy brand dạng grid
     * @param $paging bool có phân trang không ko, có khi lấy all brand
     * @param $row_per_page int số bản ghi 1 trang nếu có phân trang
     * @param $page_no int trang hiện tại trang nếu có phân trang
     * @return array
     * */
    static function allBrand($brands = [], $cat = null, $paging = false, $row_per_page = 12, $page_no = 1, $group_by = false, $keyword = "")
    {
        $where = "";
        if (!empty($brands)) {
            $brand_id = implode(",", $brands);
            $where .= " a.brand_id IN($brand_id) and ";
        }else{
			$where .= " a.brand_id = 0 AND";
		}
        if ($cat != null && $cat > 0){
			$cat = (int) $cat;
            $cat_ids = Balance::getCatId($cat);
            if(count($cat_ids) > 0) {
                $where .= " b.cat_id IN (" . implode(",", $cat_ids) . ") and ";
            }
        }
        $city_id = CookieLib::get("MY_CITY");
        if($city_id == -1){
            // tinhr thanh khac
            $provide = self::allProvide(true);
            $provide_id =array_column($provide,'id');
            if(count($provide_id) > 0)
                $where .= " (a.city_id NOT IN (".implode(",",$provide_id) .") OR online=".IS_YES.") AND ";
        }elseif ($city_id != 0) {
            $where .= " (a.city_id={$city_id} OR online=".IS_YES.") AND ";
        }
        if ($keyword != "") {
            $where .= " b.title LIKE '%{$keyword}%' AND ";
        }
        if ($group_by == true) $gr = " GROUP BY a.brand_id ";
        else $gr = "";
        $sql = "SELECT b.id,b.title,b.logo,a.address,a.brand_id FROM " . T_BRAND_OFFICE . " a LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id WHERE $where a.status=" . IS_ON . " AND b.status=" . IS_ON . " $gr HAVING b.id >0 ORDER BY b.sort=0,b.sort asc,b.title"; // a.status=" . IS_ON . " AND b.status=". IS_ON . "
        if ($paging == true) {
            $re = PagingGB::query($sql, $row_per_page, $page_no, 'brand_id,b.id');
            $data = array();
            if ($re && PagingGB::$totalPage >= $page_no) {
                while ($brand = $re->fetch(PDO::FETCH_ASSOC)) {
                    $brand['distance'] = '-';
                    $data[] = $brand;
                }
            }
        } else {
            $stmt = DB::query($sql);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($data as &$item) {
                $item['distance'] = '-';
            }
        }
        return $data;
    }

    /*Lấy danh sách brand theo vị trí người dùng, nếu bật định vị*/
    static function allBrandByLoc($brands = [], $cat = 0, $paging = false, $row_per_page = 12, $page_no = 1, $group_by = false, $keyword = "")
    {
        $my_loc = self::getCurrentLoc();
        $mylat = (float)$my_loc[0];
        $mylon = (float)$my_loc[1];
        $lon1 = $mylon - USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
        $lon2 = $mylon + USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
        $lat1 = $mylat - (USER_DISTANCE / 69);
        $lat2 = $mylat + (USER_DISTANCE / 69);
        $where = "";
        if (!empty($brands)) {
            $brand_id = implode(",", $brands);
            $where .= "  a.brand_id IN($brand_id) AND";
        }
        if ($keyword != "") {
            $where .= "  b.title LIKE '%{$keyword}%' AND";
        }
        if ($cat != null && $cat > 0){
			$cat = (int) $cat;
            $cat_ids = Balance::getCatId($cat);
            if(count($cat_ids) > 0) {
                $where .= "  b.cat_id IN (" . implode(",", $cat_ids) . ") and ";
            }
        }
        $expression = "(6371 * acos( cos( radians($mylat) ) * cos( radians( a.latitude ) ) * cos( radians( a.longitude ) - radians($mylon) ) + sin( radians($mylat) ) * sin( radians( a.latitude ) ) ) )";
        if ($group_by == true) $gr = " GROUP BY a.brand_id ";
        else $gr = "";
        $sql = "SELECT  b.id,a.address,b.title,b.logo,
               ROUND($expression,1) as `distance`
            FROM ".T_BRAND_OFFICE." a
            LEFT JOIN ".T_BRAND." b ON a.brand_id = b.id
            WHERE
             a.status=" . IS_ON . " AND b.status= " . IS_ON . " AND
               $where
                a.longitude BETWEEN $lon1 AND $lon2
                AND a.latitude BETWEEN $lat1 AND $lat2
               AND ($expression <= " . USER_DISTANCE . " OR online=".IS_YES.")

               $gr HAVING b.id >0
            ORDER BY $expression";
        // a.status = ".IS_ON." AND b.status= ".IS_ON."
        if ($paging == true) {
            $re = PagingGB::query($sql, $row_per_page, $page_no, 'brand_id,b.id');
            if ($re && PagingGB::$totalPage >= $page_no) {
                $data = $re->fetchAll(PDO::FETCH_ASSOC);
            } else {
                $data = [];
            }
        } else {
            $stmt = DB::query($sql);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        return $data;
    }

    static function getBrand($id){
        if ($id <= 0) return [];
        $id = (int) $id;
        $sql = "SELECT a.id,a.title,a.logo,a.online FROM " . T_BRAND . " a WHERE a.id={$id}";
        $stmt = DB::query($sql);
        $brand = $stmt->fetch(PDO::FETCH_ASSOC);
        return $brand;
    }
    static function getBrandById($id,$token,$gift_id = 0)
    {
        if ($id <= 0 || $token == "") return [];
        $allow_brand = self::getBrandGiftSet($token);
        $brands_id =array_column($allow_brand,'brand_id');
        if(!in_array($id,$brands_id)){
            return [];
        }
        $brand = self::getBrand($id);
        if(empty($brand)) return [];
        $access_loc = self::getAccessLoc();
        $where = ["brand_id={$brand['id']}"];
        $where[] = "status=" . IS_ON;
        if($access_loc != 1 && $brand['online'] == IS_OFF) {
            $city_id = CookieLib::get("MY_CITY");
            if ($city_id == -1) {
                // tinhr thanh khac
                $provide = self::allProvide(true);
                $provide_id = array_column($provide, 'id');
                if (count($provide_id) > 0)
                    $where []= " city_id NOT IN (" . implode(",", $provide_id) . ") AND ";
            } elseif ($city_id != 0) {
                $where[]= " city_id={$city_id} AND ";
            }
        }
        $offices = [];
        if (!empty($brand)) {
            if($gift_id > 0){
                $gift_brand_office = Balance::getBrandOfficeApply($gift_id);
                if(!is_bool($gift_brand_office)){
                    $where[] = "id in($gift_brand_office)";
                }
            }
            $whereCond = FunctionLib::addCondition($where);
            $child = "SELECT address,latitude,longitude,city_id from " . T_BRAND_OFFICE . " WHERE $whereCond";
            $stmt_child = DB::query($child);
            $offices = $stmt_child->fetchAll(PDO::FETCH_ASSOC);
            $allow_loc = self::getAccessLoc();
            foreach ($offices as &$office) {
                if ($allow_loc == 0) {
                    $office['show'] = 1;
                    $office['distance'] = '-';
                } else {
                    if($brand['online'] == IS_ON){
                        $office['distance'] = "-";
                        $distance = 0;
                    }else {
                        $distance = self::calcDistance($office['latitude'], $office['longitude']);
                        $office['distance'] = $distance;
                    }
                    if ($distance <= USER_DISTANCE || $brand['online'] == IS_YES) {
                        $office['show'] = 1;
                    } else $office['show'] = 0;
                }
            }
        }
        uasort($offices, function ($a, $b) {
            return strnatcmp($a['distance'], $b['distance']);
        });
        $brand['office'] = $offices;
        $images = MediaUrl::fromImageTitle($brand['logo'],[160]);
        $brand['image'] = isset($images[160]) ? $images[160] : '';
        return $brand;
    }

    static function getCurrentLoc()
    {

        $loc = CookieLib::get('MY_LOCATION');
        return explode(",", $loc);
    }

    static function calcDistance($lat1, $lon1, $lat2 = null, $lon2 = null)
    {
        if ($lat2 == null) {
            $loc = self::getCurrentLoc();
            $lat2 = @(float)$loc[0];
            $lon2 = @(float)$loc[1];
        }
        if ($lat1 != "" && $lat2 != "" && $lon1 != "" && $lon2 != "") {
            $R = 6371;
            $dLat = deg2rad($lat2 - $lat1);
            $dLon = deg2rad($lon2 - $lon1);
            $a =
                sin($dLat / 2) * sin($dLat / 2) +
                cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
                sin($dLon / 2) * sin($dLon / 2);
            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
            $d = $R * $c; // Distance in km
            return round($d, 1);
        }
        return "-";
    }

    function deg2rad($deg)
    {
        return $deg * (M_PI / 180);
    }

    static function getAccessLoc()
    {
        $allow_loc = CookieLib::get('ALLOW_LOCATION');
        return $allow_loc;
    }

    /**
     * @function getCityName trả về tên thành phố
     * */
    static function getCityName()
    {

        // nếu tắt định vị
        if (self::getAccessLoc() == 0) {
            $my_city = CookieLib::get("MY_CITY");
            if ($my_city == -1) {
                $city_name = "Tỉnh thành khác";
                return [
                    'id' => -1,
                    'title' => $city_name
                ];
            } elseif($my_city == 0) {
                $city_name = "Tất cả";
                return [
                    'id' => 0,
                    'title' => $city_name
                ];
            }else{
                $sql = "SELECT id, title FROM " . T_PROVINCE . " WHERE id={$my_city}";
                $stmt = DB::fetch($sql);
                return $stmt;
            }
        } else {
			$my_loc = self::getCurrentLoc();
            $geocode = file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?latlng={$my_loc[0]},{$my_loc[1]}&language=vi&sensor=false&key=".GOOGLE_API_KEY);
            $data = json_decode($geocode);
            $add_array = $data->results;
            $add_array = $add_array[0];
            $add_array = $add_array->address_components;
            $country = "";
            $state = "";
            $city = "Tỉnh thành khác";
            foreach ($add_array as $key) {
                if ($key->types[0] == 'administrative_area_level_2') {
                    $city = $key->long_name;
                }
                if ($key->types[0] == 'administrative_area_level_1') {
                    $state = $key->long_name;
                }
                if ($key->types[0] == 'country') {
                    $country = $key->long_name;
                }
            }
            // nếu bật định vị
            return [
                'id' => 0,
                'title' => implode(", ", [$city, $state])
            ];
        }
    }

    static function allProvide($is_city = false)
    {
        $where = "";
        if ($is_city == true) {
            $where = " AND is_city=1 ";
        }
        $sql = "SELECT id,title from " . T_PROVINCE . " WHERE status=".IS_ON." $where";
        $stmt = DB::query($sql);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return $data;
    }
    static function HotBrand($token){
    	if(empty($token)) return [];
        $brand = self::getBrandGiftSet($token);
        $id_brand = [];
        if($brand != null){
            $id_brand = array_column($brand,'brand_id');
        }
        $sql = "SELECT b.id,b.title,b.logo FROM " . T_BRAND_OFFICE . " a LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id WHERE b.is_hot=1 AND a.status=" . IS_ON . " AND b.status=" . IS_ON . " AND a.brand_id in (".implode(",",$id_brand).") GROUP BY b.id HAVING b.id >0"; // a.status=" . IS_ON . " AND b.status=". IS_ON . "
        $stmt = DB::query($sql);
		$data = [];
        if($stmt) {
			$data = $stmt->fetchAll( PDO::FETCH_ASSOC );
			foreach ($data as $k => &$item) {
				if (isset( $data[$k - 1] ) && $data[$k - 1]['id'] == $item['id']) {
					$item['image'] = $data[$k - 1]['image'];
				} else {
					$images = MediaUrl::fromImageTitle( $item['logo'], [160] );
					$item['image'] = isset( $images[160] ) ? $images[160] : '';
				}
			}
		}
        return $data;
    }
    /** ------------------
     * @function voucher cua tôi
     * @param $card_id int
     * @param $expired int 1 => available voucher, 2 => sắp hết hạn, 3 hết han
     * @return null|array
     */
    static function myVoucher($card_id = null,$expired = 1){
		if(empty($card_id)) return [];
        // $start_time = microtime(true);
		$sql = "SELECT distinct (gift_detail_id) as gift_detail_id FROM " . T_CART_DETAIL . " a LEFT JOIN ".T_CART." b on a.cart_id=b.id WHERE a.card_id =$card_id AND a.pay_status=" . IS_YES . " AND a.status=" . IS_ON . " AND b.pay_status=" . IS_YES . " AND b.status=" . IS_ON . " ORDER BY a.id DESC";
		$array = [];
		$recd = DB::query($sql);
		if (!empty($recd)) {
			$arr_id_gift_detail = $recd->fetchAll(PDO::FETCH_COLUMN);
			$arr_id_gift_detail = array_unique($arr_id_gift_detail);
			if (empty($arr_id_gift_detail)) {
				return [];
			}
            $arr_id_gift_detail = array_unique($arr_id_gift_detail);
            if(empty($arr_id_gift_detail)) return ['brand'=> null];
            // echo microtime();
            $five_day_after = time() + (5 * 24 * 60 * 60);
            $five_day_ago = time() - (5 * 24 * 60 * 60);
            $today = time();
            if($expired == 1)
                $sql = "
                SELECT * FROM(
                SELECT a.id,a.title ,a.logo,b.type, count(c.id) as soluong,max(c.id) as cart_detail_id,expired,max(e.created) as created,d.id as code_id,c.receive_code
                        FROM " . T_BRAND . " a
                        LEFT JOIN " . T_GIFT_DETAIL . " b on a.id=b.brand_id
                        LEFT JOIN " . T_CART_DETAIL . " c on b.id=c.gift_detail_id
                        LEFT JOIN " . T_GIFT_CODE . " d on c.id=d.cart_detail_id
                        LEFT JOIN " . T_CART . " e on c.cart_id=e.id
                        WHERE b.id IN (" . implode(',', $arr_id_gift_detail) . ")
                            AND (e.pay_status =".IS_ON." AND e.status=".IS_ON.") AND e.card_id=$card_id
							AND (c.pay_status = ".IS_ON." AND c.status=".IS_ON.")
                            AND (c.delivery = 1 OR c.delivery=2)
                            AND (d.expired =0 OR d.expired > $five_day_after OR d.expired is null)
                            AND (d.status = 1 or d.status is null)
                            group by a.id
                ) as t order by cart_detail_id DESC
                        ";
            else if( $expired == 2)
                $sql = "SELECT a.id,a.title ,a.logo,b.type, 1 as soluong, c.id as cart_detail_id,expired,d.id as code_id,c.receive_code
                        FROM ". T_BRAND . " a
                        LEFT JOIN ". T_GIFT_DETAIL . " b on a.id=b.brand_id
                        LEFT JOIN ". T_CART_DETAIL ." c on b.id=c.gift_detail_id
                        LEFT JOIN " . T_GIFT_CODE . " d on c.id=d.cart_detail_id
                        LEFT JOIN " . T_CART . " e on c.cart_id=e.id
                        WHERE b.id IN (". implode(',', $arr_id_gift_detail) . ")
                            AND (e.pay_status =".IS_ON." AND e.status=".IS_ON.") AND e.card_id=$card_id
							AND (c.pay_status = ".IS_ON." AND c.status=".IS_ON.")
                            AND (c.delivery = 1 OR c.delivery=2)
                            AND (d.expired > 0 AND d.expired between $today AND $five_day_after)
                            AND (d.status = 1 or d.status is null)
                            order by c.id desc";
            else $sql = "SELECT a.id,a.title ,a.logo,b.type, 1 as soluong, c.id as cart_detail_id,expired,d.id as code_id,c.receive_code
                        FROM ". T_BRAND . " a
                        LEFT JOIN ". T_GIFT_DETAIL . " b on a.id=b.brand_id
                        LEFT JOIN ". T_CART_DETAIL ." c on b.id=c.gift_detail_id
                        LEFT JOIN " . T_GIFT_CODE . " d on c.id=d.cart_detail_id
                        LEFT JOIN " . T_CART . " e on c.cart_id=e.id
                        WHERE b.id IN (". implode(',', $arr_id_gift_detail) . ")
                            AND (e.pay_status =".IS_ON." AND e.status=".IS_ON.") AND e.card_id=$card_id
							AND (c.pay_status = ".IS_ON." AND c.status=".IS_ON.")
                            AND (c.delivery = 1 OR c.delivery=2)
                            AND (d.expired > 0 AND d.expired between $five_day_ago AND $today)
                            AND (d.status = 1 or d.status is null)
                            order by c.id desc";
            $stmt = DB::query($sql);

            $brands = [];
            while ($item = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $images = MediaUrl::fromImageTitle($item['logo'],[160]);
                $item['image'] = isset($images['160'])? $images['160']: "";
				$item['receive_code'] = System::decrypt($item['receive_code']);
                $brands[] = $item;
            }
            $array['brand'] = $brands;
            // $end_time = microtime(true);
        }
        return $array;
    }

    //chi tiêt voucher
    static function detailVoucher($card_id,$brand_id,$cart_detail_id = 0){
		if(empty($card_id)) return [];
        $array = [];
        $rec = DB::query("SELECT id FROM ".T_CART." WHERE status=".IS_ON." AND pay_status=".IS_YES." AND card_id=".$card_id);
        $carts_id = [];
        if($rec){
            $carts_id = $rec->fetchAll(PDO::FETCH_COLUMN);
        }
        if(empty($carts_id)) return [];
        $condition = " AND (c.delivery = 1 OR c.delivery=2)";
        if($cart_detail_id > 0) $condition = " AND c.id=$cart_detail_id";
        $sql = "SELECT a.id,a.gift_id,a.title,a.avatar,a.type,b.expired,b.code,a.price,d.code_type,d.showbarcode,d.type,c.delivery,c.receive_code,d.article_id,receive_code,b.status as code_status,c.id as cart_detail_id,e.online,f.created,b.id as code_id,d.code_type,c.created as createdCart,b.used,b.valid_time,b.pin,b.serial,a.gift_id FROM " . T_GIFT_DETAIL . " a LEFT JOIN ". T_CART_DETAIL ." c on c.gift_detail_id=a.id LEFT JOIN " . T_GIFT_CODE . " b on c.id=b.cart_detail_id LEFT JOIN " . T_GIFT. " d on d.id=a.gift_id LEFT JOIN " . T_BRAND . " e on e.id=a.brand_id  LEFT JOIN " . T_CART . " f on f.id=c.cart_id  WHERE d.brand_id=$brand_id and c.status=" . IS_ON . " AND c.pay_status=".IS_YES." AND c.cart_id in (".implode(',',$carts_id).") $condition AND b.status=1 order by c.id desc";
        $stmt = DB::query($sql);
        $tmp_id = [];
        $k=0;
        if ($stmt) {
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            	if($row['used'] == 0){
            		DB::update(T_GIFT_CODE,['last_time' => TIME_NOW],"id={$row['code_id']}");
				}
                if (($row['code'] == null || $row['code'] == "") && $row['delivery'] == 1) {
                    self::assignCode($row['cart_detail_id']);
                }
				$row['code'] = System::decrypt($row['code'],CRYPT_KEY);
                $search_index = array_search($row['id'],$tmp_id);
                if($search_index !== false){
                    $row['image'] = $array['gifts'][$search_index]['image'];
                }else{
                    //$row['image'] = 1;
                    $images = MediaUrl::fromImageTitle($row['avatar'],[640]);
                    $row['image'] = isset($images[640]) ? $images[640] : '';
                }
                $tmp_id[] = $row['id'];

                //$qrCodeUrl = $row['code'];
                $row['link'] = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
                if ($row['showbarcode'] == 2) {
                    $qrCode = $row['code'];
                    //$qrCode = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode($row['code']) . '&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default';
                } else {
                    $qrCode = "https://urbox.vn/nha-cung-cap/kich-hoat-otp/". md5('NCCKICHHOATQUA'.$row['code']) .".html?codegift={$row['code']}";
                    if ($row['code_type'] == 3) {
                        $qrCode = $row['code'];
                    }
                    // $qrCode = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($qrCodeUrl);
                }
                $row['qrCode'] = $row['code'];
                $row['receive_code'] = System::decrypt($row['receive_code']);
                $row['link_active'] =  "https://urbox.vn/nha-cung-cap/kich-hoat-otp/". md5('NCCKICHHOATQUA'.$row['code']) .".html?codegift={$row['code']}";
                if($row['showbarcode'] == 5){
                    $extractSerial = explode(' PIN:',$row['serial']);
                    $row['serial'] = $extractSerial[0];
                    $row['pin'] = isset($extractSerial[1])?$extractSerial[1]: "";
                }
                $array['gifts'][] = $row;
                $k++;
            }
            $item = [];
            if(!empty($array['gifts'])){
                $item =  $array['gifts'][0];
            }
            if(!empty($item)){
				$content = Gift::getContent(Language::$activeLang, $item['gift_id']);
				if ($content['title'] != "") {
					$gift['title'] = $content['title'];
				}
				$array['note'] = html_entity_decode($content['note']);
            }else{$array['note']  = "";}
            if($brand_id  > 0){
                $s_brand = "SELECT address from " . T_BRAND_OFFICE . " WHERE brand_id={$brand_id} and status=".IS_ON;
                $stmt_brand = DB::query($s_brand);
                $array['office'] = $stmt_brand->fetchAll(PDO::FETCH_COLUMN);
            }
        }
        return $array;
    }
    static function usedVoucher($card_id){
		if(empty($card_id)) return [];
        $rec = DB::query("SELECT id FROM ".T_CART." WHERE status=".IS_ON." AND pay_status=".IS_YES." AND card_id=$card_id");
        $carts_id = [];
        if($rec){
            $carts_id= $rec->fetchALl(PDO::FETCH_COLUMN);
        }
		if(empty($carts_id)) return [];
        $gifts_details = [];
        $time_expired_num = time() - (1 * 24 * 60 * 60);
        if(!empty($carts_id)) {
            $sql = "SELECT a.id,gift_detail_id,avatar,title,receive_code FROM " . T_CART_DETAIL . " a LEFT JOIN " . T_GIFT_DETAIL . " b on a.gift_detail_id=b.id WHERE delivery=3 and a.status=" . IS_ON . " AND using_time > ".$time_expired_num." AND  cart_id IN(" . implode(',', $carts_id) . ")";
            $recd = DB::query($sql);
            if ($recd) {
                while ($row = $recd->fetch(PDO::FETCH_ASSOC)) {
                    $row['link'] = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
                    $images = MediaUrl::fromImageTitle($row['avatar'],[160]);
                    $row['image'] = isset($images[160]) ? $images[160] : '';
                    //$row['image'] = "";
                    $gifts_details[] = $row;
                }
            }
        }
        return $gifts_details;
    }
    static function updateCart($token,$id){
        $card = [];
        if ($token != null) {
            $card = Card::getByToken($token);
        }
        if(empty($card)) return false;
        if($id <=0) return false;
        $s = "SELECT a.id,b.id as cart_id,b.card_id from " . T_CART_DETAIL . " a LEFT JOIN ". T_CART ." b on a.cart_id = b.id where a.id={$id} LIMIT 1";
        $stmt = DB::fetch($s);
        if(!empty($stmt) && $stmt['card_id'] > 0 && $stmt['card_id'] == $card['id']){
            $gift_code = DB::fetch("SELECT id from ". T_GIFT_CODE . " where cart_detail_id={$stmt['id']} and status=1 LIMIT 1");
            if(!empty($gift_code) && $gift_code['id'] > 0) {
                $newRow = array('active' => 2, 'used' => TIME_NOW,'apiCall'=> 7);
                $code = DB::update(T_GIFT_CODE, $newRow, "id={$gift_code['id']}");
                $newCartRow = array(
                    'using_time' => TIME_NOW,
                    'delivery' => 3,
                );
                $cart  = DB::update(T_CART_DETAIL, $newCartRow, " id =" . $id);
                if($code && $cart) return true;
            }

            return false;
        }

        return false;
    }
    static function assignCode($cart_detail_id){
		if($cart_detail_id <=0) return false;
        if($cart_detail_id>0){
            $dataPost['idCartDetail'] = $cart_detail_id;
            $linkApi = LINK_URBOX_INTERNAL.'ajax.php?act=api&code=receivingGift';
            $curl = new CURL();
            $curl->post($linkApi,$dataPost);
        }
    }

//    survey
    static function  getAnswer($card_id){
		if($card_id <=0) return false;
		$card_id = (int)$card_id;
        $stmt_cus_survey = DB::query("SELECT survey_id  FROM " . T_SURVEY_RESULT . " WHERE card_id = '{$card_id}' GROUP BY survey_id ");
        $cus_survey = $stmt_cus_survey->fetchAll(PDO::FETCH_COLUMN);
        return $cus_survey;
    }
    static function checkSurvey($token)
    {
		if($token  == "") return false;
        $card = Card::getByToken($token);
        if($card != false && $card['survey_id']  > 0) {
            $survey = self::viewSurvey($card['survey_id']);
            if(!empty($survey)) {
                $cus_survey = self::getAnswer($card['id']);
                if (count($cus_survey) <= 0)
                    return $card['survey_id'];
                return 0;
            }return 0;
        }
        return 0;
    }

    static function getQuestion($question_id){
		if($question_id  == "") return false;
		$question_id = (int)$question_id;
        return DB::fetch("SELECT * FROM " . T_SURVEY_QUESTION . " WHERE id={$question_id}");
    }
    static function issetAnswer($question_id = 0,$card_id = 0){
		if($question_id  == "" || $card_id == "") return true;
        if($question_id ==0 || $card_id ==0) return true;
		$question_id = (int)$question_id;
		$card_id = (int)$card_id;
        return DB::fetch("SELECT id FROM " . T_SURVEY_RESULT . " WHERE question_id={$question_id} AND card_id={$card_id}");

    }
    static function saveSurvey($survey,$card){
    	if(empty($survey) || empty($card)){
    		return false;
		}
        // trong trường hợp thằng nào vui tính insert hàng loạt vào db
        if(is_numeric(implode('',array_keys($survey)))){
            foreach ($survey as $item) {
                self::saveSurvey($item,$card);
            }
        }else {
            if(!empty($card['phone'])){
            		$_phone = System::decrypt($card['phone'],CRYPT_KEY);
                $cus = self::getCus($_phone);
                if(isset($cus['id']) && $cus['id'] > 0){
                    $customer_id = $cus['id'];
                }else{
                    $cus = self::saveCus($_phone);
                    if(!empty($cus))
                        $customer_id = $cus;
                    else $customer_id = 0;
                }
            }else{
                $customer_id = 0;
            }
            $card_id = $survey['card_id'];
            $survey_id = $survey['survey_id'];
            $question_id = $survey['question_id'];
            $answer_id = $survey['answer_id'];
            $content = $survey['content'];
            if ($card_id > 0 && $survey_id > 0 && $question_id > 0) {
                $isset = self::issetAnswer($question_id,$card_id);
                if($isset == true  || !empty($isset)) return false;
                return DB::insert(T_SURVEY_RESULT, ['card_id' => $card_id, 'survey_id' => $survey_id,'customer_id'=>$customer_id ,'question_id' => $question_id, 'answer_id' => $answer_id, 'content' => $content, 'created' => TIME_NOW]);
            }
            return false;
        }
    }
    static function countRequire($id){
    	$id = (int)$id;
    	if($id <= 0) return false;
        return DB::fetch("SELECT COUNT(id) as qty FROM " . T_SURVEY_QUESTION . " WHERE survey_id ={$id} AND isRequired=". IS_ON);
    }
    static function viewSurvey($id){
		$id = (int)$id;
		if($id <= 0) return false;
        $survey = DB::fetch("SELECT id,title,description from ".T_SURVEY . " WHERE id={$id} AND status=".IS_ON);
        return $survey;
    }
    static function getSurvey($survey_id=0){
		$survey_id = (int)$survey_id;
		if($survey_id <= 0) return false;
        $survey_name = DB::fetch("SELECT id,title,description,image from ".T_SURVEY . " WHERE id={$survey_id}");
        $stmt_survey = DB::query("SELECT * FROM " .T_SURVEY_QUESTION . " WHERE survey_id={$survey_id}");
        $question = $stmt_survey->fetchAll(PDO::FETCH_ASSOC);
        foreach($question as $k=>$v){
            $ans = DB::query("SELECT id, title FROM " . T_SURVEY_ANSWER . " WHERE question_id= {$v['id']} and survey_id = {$survey_id}")->fetchAll(PDO::FETCH_ASSOC);
            $question[$k]['answer'] = $ans;
        }
        $banner = MediaUrl::fromImageTitle($survey_name['image'], [640]);
        $survey_name['image'] = isset($banner[640]) ? $banner[640] : '';

        return [
            'survey' => $survey_name,
            'question' =>$question
        ];
    }
    static function getCus($phone){
        $is_phone = FunctionLib::is_phone($phone);
        if($is_phone === false) return 0;
        $cus = "SELECT id FROM " . T_CUSTOMER . " WHERE phone={$phone}";
        return DB::fetch($cus);
    }
    static function saveCus($phone){
        $cus = self::getCus($phone);
        if($cus === 0 || (isset($cus['id']) && $cus['id'] > 0)) return false;
        $data = [
            'phone'=>$phone,
            'status'=> 1,
            'active'=> 1,
            'created'=> TIME_NOW
        ];
        return DB::insert(T_CUSTOMER,$data);
    }
}