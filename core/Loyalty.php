<?php


	class Loyalty
	{
		static function getToken($token){
			$token = StringLib::clean_value($token);
			$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
			if(empty($token)) return false;
			$sql = "SELECT * FROM ".T_LOYAL_TOKEN." WHERE token = '{$token}'";
			$rs = DB::fetch($sql);
			if(!empty($rs)){
				if(!in_array($rs['lang'],['en','vi'])){
					$rs['lang'] = 'en';
				}
                $rs['is_guest'] = 1;
                $rs['login_url'] = "#";
                $rs['register_url'] = "#";
			}
			return  $rs;
		}
		static function getLastToken($card = []){
			if(empty($card))return false;
			$sql = "SELECT id FROM ".T_LOYAL_TOKEN." WHERE site_user_id = '{$card['site_user_id']}' and campaign_id ='{$card['campaign_id']}' order by id desc";
			$last_id = DB::query($sql)->fetchColumn();
			return $last_id == $card['id'];
		}
		static function getApp($site_id){
			if(empty($site_id) || !is_int($site_id)) return false;
			$app =  DB::fetch("SELECT * FROM ".T_APP." WHERE id = ".$site_id);
			$banner = MediaUrl::fromImageTitle($app['banner'],[]);
			$app['banner'] = isset($banner[0]) ? $banner[0] : '';
			$banner_mobile = MediaUrl::fromImageTitle($app['banner_mobile'],[640]);
			$app['banner_mobile'] = isset($banner_mobile[640]) ? $banner_mobile[640] : '';
			$banner = MediaUrl::fromImageTitle($app['logo'],[320]);
			$app['logo'] = isset($banner[320]) ? $banner[320] : '';
			return $app;
		}
		static function hotVoucher($tokens,$limit=10,$isPhysical = 1,$length = 0,$params = []){
			if(empty($tokens)) return false;
			$param = [];
			$param[] = 'a.code_quantity > 0';
			$param[] = 'a.status=1';
			$param[] = 'a.is_hot=1';
			$campaign = self::getCampaign($tokens);
			$id_gift_set = $campaign['giftset_id'];

			$id_gift_detail = array(0);
			$id_gifts = [];
			#Lay them phan chi ban theo GIFT_SET
			$s_giftset = "SELECT gift_detail_id FROM ".T_GIFT_SET." WHERE gift_id=".$id_gift_set ." order by sort asc";
			$getSiteGift = DB::query($s_giftset);
			if($getSiteGift){
				while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)){
					$id_gift_detail[] = $row['gift_detail_id'];
				}
				if(!empty($id_gift_detail)){
					$id_gifts = array_merge($id_gifts,$id_gift_detail);
				}
			}
			$id_gifts = array_unique($id_gifts);
			if(isset($params['subID']) && is_array($params['subID'])){
				$id_gifts = array_diff($id_gifts,$params['subID']);
			}
			$param[] = "a.id in (". implode(",",$id_gifts) . ")";
			if($isPhysical == 1) {
				$param[] = "a.isPhysical={$isPhysical}";
			}else{
				$param[] = "(a.isPhysical={$isPhysical} OR a.type=9)";
			}
			$condition =  FunctionLib::addCondition($param, true);

			if(Language::$activeLang =='vi') {
				$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,openTime,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category,a.isPhysical,a.avatar as image
				FROM " . T_GIFT_DETAIL . " a
				LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
				LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
				$condition ORDER BY view DESC, FIELD(a.id," . implode(',', $id_gifts) .") LIMIT $limit";
			}else {
				$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,openTime,a.title as vi_title,e.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category,a.isPhysical,a.avatar as image
				FROM " . T_GIFT_DETAIL . " a
				LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
				LEFT JOIN " . T_GIFT_CONTENT . " e on a.gift_id=e.gift_id AND lang='".Language::$activeLang."'
				LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
				$condition ORDER BY view DESC, FIELD(a.id," . implode(',', $id_gifts).") LIMIT $limit";
			}

			$re = DB::query($sql);
			$data = array();
			if($re){
				while ($row = $re->fetch(PDO::FETCH_ASSOC)){
					$images = MediaUrl::fromImageTitle($row['logo'],[160]);
					$banner = MediaUrl::fromImageTitle($row['image'],[640]);
					$temp = array(
						'id'=>$row['id'],
						'brand_id'=>$row['brand_id'],
						'cat_id'=>$row['cat_id'],
						'gift_id'=>$row['gift_id'],
						'title'=>$length > 0 ?StringLib::truncateHtml($row['title'],$length):$row['title'],
						'openTime'=>$row['openTime'],
						'is_hot'=>$row['is_hot'],
						'isPhysical'=>$row['isPhysical'],
						'price'=>$row['price'],
						'price_point'=> self::point_format($row['price']/$campaign['exchange_rate']),
						'view'=>$row['view'],
						'image'=>isset($images[160])?$images[160]:'',
						'banner'=>isset($banner[640])?$banner[640]:'',
						'brand_title' => $row['brand_title'],
						'category' => $row['category']
					);
					$data[$row['id']] = $temp;
				}
			}
			if(!empty($data))
				$data = self::changePrice($data,["rate" => $campaign['exchange_rate'], "app_id" => $tokens['site_id']]);
			return $data;
		}
		static function listHotVoucher($tokens,$params = []){
			if(empty($tokens)) return false;
			$param = [];
			$param[] = 'a.code_quantity > 0';
			$param[] = 'a.status=1';
			$param[] = 'a.is_hot=1';
			$campaign = self::getCampaign($tokens);
			$id_gift_set = $campaign['giftset_id'];

			$isPhysical = isset($params['type'])?$params['type']: 1;
			$length = isset($params['length'])?$params['length']: 0;
			$pages = [
				'per_page' => isset($params['per_page'])?$params['per_page']: 10,
				'page_num' => isset($params['page_num'])?$params['page_num']: 10,
			];
			$id_gift_detail = array(0);
			$id_gifts = [];
			#Lay them phan chi ban theo GIFT_SET
			$s_giftset = "SELECT gift_detail_id FROM ".T_GIFT_SET." WHERE gift_id=".$id_gift_set ." order by sort ASC";
			$getSiteGift = DB::query($s_giftset);
			if($getSiteGift){
				while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)){
					$id_gift_detail[] = $row['gift_detail_id'];
				}
				if(!empty($id_gift_detail)){
					$id_gifts = array_merge($id_gifts,$id_gift_detail);
				}
			}
			$id_gifts = array_unique($id_gifts);
			if(isset($params['subID']) && is_array($params['subID'])){
				$id_gifts = array_diff($id_gifts,$params['subID']);
			}
			$param[] = "a.id in (". implode(",",$id_gifts) . ")";
			if($isPhysical == 1) {
				$param[] = "a.isPhysical={$isPhysical}";
			}elseif($isPhysical == 2){
				$param[] = "(a.isPhysical={$isPhysical} OR a.type=9)";
			}

			if(isset($params['gift_quantity']) && $params['gift_quantity']  > 0) {
				$param[] = 'd.code_quantity > 0';
				$condition =  FunctionLib::addCondition($param, true);
				$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,openTime,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category,a.avatar as image
				FROM " . T_GIFT_DETAIL . " a
				LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
				LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
				 LEFT JOIN ". T_GIFT_QUANTITY . " d on d.gift_detail_id=a.id and d.status =2 and d.app_id={$params['gift_quantity']}
				$condition ORDER BY view DESC, FIELD(a.id," . implode( ',', $id_gifts ) . ")";
			}else{
				$condition =  FunctionLib::addCondition($param, true);

				$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,openTime,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category,a.avatar as image
				FROM " . T_GIFT_DETAIL . " a
				LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
				LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
				$condition ORDER BY view DESC, FIELD(a.id," . implode( ',', $id_gifts ) . ")";
			}


			$re = PagingGB::query($sql, $pages['per_page'],$pages['page_num'],"a.id");
			#$re = DB::query($sql);
			$data = array();
			if($re){
				while ($row = $re->fetch(PDO::FETCH_ASSOC)){
					$images = MediaUrl::fromImageTitle($row['logo'],[160]);
					$banner = MediaUrl::fromImageTitle($row['image'],[640]);
					$temp = array(
						'id'=>$row['id'],
						'brand_id'=>$row['brand_id'],
						'cat_id'=>$row['cat_id'],
						'gift_id'=>$row['gift_id'],
						'title'=>$length > 0 ?StringLib::truncateHtml($row['title'],$length):$row['title'],
						'openTime'=>$row['openTime'],
						'is_hot'=>$row['is_hot'],
						'isPhysical'=>$row['isPhysical'],
						'price'=>$row['price'],
						'price_point'=> self::point_format($row['price']/$campaign['exchange_rate']),
						'view'=>$row['view'],
						'image'=>isset($images[160])?$images[160]:'',
						'banner'=>isset($banner[640])?$banner[640]:'',
						'brand_title' => $row['brand_title'],
						'category' => $row['category']
					);
					$data[$row['id']] = $temp;
				}
			}
			if(!empty($data)) {
				if(Language::$activeLang !='vi'){
					$gift_ids = array_column($data,'gift_id');
					$giftContent = self::giftContent( Language::$activeLang, $gift_ids );
					foreach ($data as &$r) {
						if(isset($giftContent[$r['gift_id']])){
							$r['title'] = !empty($giftContent[$r['gift_id']]['title'])?$giftContent[$r['gift_id']]['title']:$r['title'];
						}
					}
				}

				$data = self::changePrice( $data, ["rate" => $campaign['exchange_rate'], "app_id" => $tokens['site_id']] );
			}
			return $data;
		}

		static function getBrand($id){
			if ($id <= 0) return [];
			$id = (int) $id;
			$sql = "SELECT a.id,a.title,a.logo,a.online,a.cat_id FROM " . T_BRAND . " a WHERE a.id={$id}";
			$stmt = DB::query($sql);
			return $stmt->fetch(PDO::FETCH_ASSOC);
		}
		static function getBrandById($id,$gift_id= 0)
		{
			if ($id <= 0) return [];
			$brand = self::getBrand($id);
			if(empty($brand)) return [];
			$where = "";
			if($brand['online'] == IS_OFF) {
				$city_id = CookieLib::get("L_MY_CITY");
				if ($city_id == -1) {
					// tinhr thanh khac
					$provide = self::allProvide(true);
					$provide_id = array_column($provide, 'id');
					if (count($provide_id) > 0)
						$where .= " city_id NOT IN (" . implode(",", $provide_id) . ") AND ";
				} elseif ($city_id != 0) {
					$where .= " city_id={$city_id} AND ";
				}

			}
			$offices = [];
			if (!empty($brand)) {
				if($gift_id > 0) {
					$offices = self::getOfficeByGift($gift_id,$id);
				}
				if(empty($offices)) {
					$child = "SELECT address,latitude,longitude,city_id from " . T_BRAND_OFFICE . " WHERE brand_id={$brand['id']} AND $where status=" . IS_ON;
					$stmt_child = DB::query( $child );
					$offices = $stmt_child->fetchAll( PDO::FETCH_ASSOC );
					foreach ($offices as &$office) {
						$office['show'] = 1;
						$office['distance'] = '-';
					}
				}
			}

			$brand['office'] = $offices;
			$images = MediaUrl::fromImageTitle($brand['logo'],[160]);
			$brand['image'] = isset($images[160]) ? $images[160] : '';
			return $brand;
		}
		static function getLoyalImg($token){
			$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
			$data = self::getToken($token);
			$mediaLogo = MediaUrl::fromImageTitle($data['logo'],[160]);
			$result['logo'] = isset($mediaLogo[160])?$mediaLogo[160]:'';
			$mediaBanner = MediaUrl::fromImageTitle($data['banner'],[640]);
			$result['banner'] = isset($mediaBanner[640])?$mediaBanner[640]:'';
			return $result;
		}
		static function getCityName()
		{

			// nếu tắt định vị
			if (self::getAccessLoc() == 0) {
				$my_city = CookieLib::get("L_MY_CITY");
				if ($my_city == -1) {
					$city_name = "Tỉnh thành khác";
					return [
						'id' => -1,
						'title' => $city_name
					];
				} elseif($my_city == 0) {
					$city_name = "Tất cả";
					return [
						'id' => 0,
						'title' => $city_name
					];
				}else{
					$my_city = (int)$my_city;
					$sql = "SELECT id, title FROM " . T_PROVINCE . " WHERE id={$my_city}";
					return DB::fetch($sql);
				}
			} else {
				$my_loc = Loyalty::getCurrentLoc();
				if(count($my_loc) == 2) {
					$mylat = $my_loc[0];
					$mylon = $my_loc[1];
				}else{
					$mylat = 21.022736;
					$mylon = 105.8019441;
				}
				$geocode = file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?latlng={$mylat},{$mylon}&language=vi&sensor=false&key=". GOOGLE_API_KEY);
				$data = json_decode($geocode);
				if($data->status == "OK") {
					$add_array = $data->results;
					$state = "";
					$city = "";
					$route = "";
					foreach ($add_array as $key) {
						$add_item = $key->address_components;
						if ($key->types[0] == 'administrative_area_level_2') {
							$city = $add_item[0]->long_name;
						}
						if ($key->types[0] == 'administrative_area_level_1') {
							$state = $add_item[0]->long_name;
						}
						if ($key->types[0] == 'route') {
							$route = $key->formatted_address;
						}
					}
					if(empty($city) && empty( $state)) {
						$friendly_name = $route;
					}else{
						$friendly_name = implode( ", ", array_filter([$city, $state]));
					}
					return [
						'id' => 0,
						'title' => !empty($friendly_name)?$friendly_name:"Tỉnh thành khác"
					];
				}else{
					$id = (int)CookieLib::get("L_MY_CITY");
					CookieLib::set("L_ALLOW_LOCATION",0);
					$sql = "SELECT id, title FROM " . T_PROVINCE . " WHERE id={$id}";
					return DB::fetch($sql);
				}
				// nếu bật định vị


			}
		}
		static function getCurrentLoc()
		{
			$loc = CookieLib::get('L_MY_LOCATION');
			return explode(",", $loc);
		}
		static function getCategory($parent_id)
		{
			if ($parent_id <= 0) return array();
			$category = [];
			$re = DB::query("SELECT * FROM " . T_CATEGORY . " WHERE id NOT IN (" . implode(CGlobal::$catNotShow, ',') . ") AND status=1 AND parent_id=" . $parent_id . " ORDER BY weight ASC");
			if ($re) {
				while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
					$avatar = MediaUrl::fromImageTitle($row['avatar'],[640]);
					$banner = MediaUrl::fromImageTitle($row['image'],[640]);
					$temp = array(
						'id' => $row['id'],
						'title' => $row['title'],
						'image' => isset($avatar[640]) ? $avatar[640] : '',
						'banner' => isset($banner[0]) ? $banner[0] : '',
						'link' => '/card/' . System::$data['card']['token'] . '?cat_id=' . $row['id'],
					);
					$category[$row['id']] = $temp;
				}
			}
			return $category;
		}

		static function getCategoryApp($app_id = 0,$lang = 'vi')
		{
		    $reCategoryApp = DB::query("SELECT * FROM ".T_CATEGORY_APP." WHERE app_id = ".$app_id." AND status=2");
            $category = [];
		    if($reCategoryApp){
                while ($row = $reCategoryApp->fetch(PDO::FETCH_ASSOC)) {
                    $avatar = MediaUrl::fromImageTitle($row['image'],[640]);
                    $banner = MediaUrl::fromImageTitle($row['banner'],[640]);
                    $temp = array(
                        'id' => $row['category_id'],
                        'title' => $lang=='vi'?$row['title']:$row['title_en'],
                        'image' => isset($avatar[640]) ? $avatar[640] : '',
                        'banner' => isset($banner[0]) ? $banner[0] : '',
                        'link' => '/card/' . System::$data['card']['token'] . '?cat_id=' . $row['category_id'],
                    );
                    $category[$row['id']] = $temp;
                }
            }
			return $category;
		}
		static function viewCate($id){
			if($id <=0) return [];
			$data = DB::fetch("SELECT id,title,icon,image,avatar FROM ". T_CATEGORY . " WHERE id='{$id}' limit 1");
			if(!empty($data)) {
				$avatar = MediaUrl::fromImageTitle( $data['avatar'], [640] );
				$banner = MediaUrl::fromImageTitle( $data['image'], [640] );
				$data['image'] = isset( $avatar[640] ) ? $avatar[640] : '';
				$data['banner'] = isset( $banner[0] ) ? $banner[0] : '';
				return $data;
			}
			return [];
		}
		static function getCampaign($tokens, $showImg = 1){
			$campaign = DB::fetch("SELECT * FROM ".T_LOYAL_CAMPAIGN." WHERE id=".$tokens['campaign_id']);
			if($showImg == 2) {
				$images = MediaUrl::fromImageTitle( $campaign['point_icon'], [80] );
				$campaign['point_icon'] = isset( $images['80'] ) ? $images['80'] : "";
				$logo = MediaUrl::fromImageTitle( $campaign['logo'], [160] );
				$campaign['logo'] = isset( $logo['160'] ) ? $logo['160'] : "";
			}
			return $campaign;
		}
		static function giftFromCart($cart_detail_id = 0){
			if($cart_detail_id < 0 || $cart_detail_id == ""){
				return false;
			}
			$s = "SELECT a.id,a.title,a.gift_id FROM ". T_GIFT_DETAIL . " a LEFT JOIN ". T_CART_DETAIL . " b on a.id=b.gift_detail_id WHERE b.id=".$cart_detail_id;
			$data = DB::fetch($s);
			$content = Gift::getContent(Language::$activeLang,$data['gift_id']);
			if(isset($content['title']) && $content['title'] != ''){
				$data['title'] = $content['title'];
			}
			return $data;

		}

		//đang dùng cho trang receiver, không cần lấy nhiều thông tin
		static function getGift($id = 0){
			if($id < 0 || $id == ""){
				return false;
			}
			$s = "SELECT a.id,a.title,a.avatar,a.price,a.isPhysical,a.gift_id,b.title as brand_title FROM ". T_GIFT_DETAIL . " a LEFT JOIN ".T_BRAND ." b on a.brand_id=b.id WHERE a.id=".$id;

			$data = DB::fetch($s);
			$content = Gift::getContent(Language::$activeLang,$data['gift_id']);
			if(isset($content['title']) && $content['title'] != ''){
				$data['title'] = $content['title'];
			}
			$images = MediaUrl::fromImageTitle($data['avatar'],[320]);
			$data['banner'] = isset($images['320'])? $images['320']: "";
			return $data;

		}
		/**
		 * @param $tokens array
		 * @param $cat_id int
		 * @param $pages array
		 * @param $params array
		 * @return  array|null
		 * */
		static function getListGifts($tokens, $cat_id = 0,$pages = [],$params = [])
		{
			if(empty($tokens)) return [];
			if(empty($pages)){
				$pages['per_page'] = 10;
				$pages['page_num'] = 1;
			}
			if(empty($pages['per_page'])){
				$pages['per_page'] = 10;
			}
			if(empty($pages['page_num'])){
				$pages['page_num'] = 1;
			}
			$showAll = isset($params['showAll'])?$params['showAll']: 0;
			$param = [];
			$param[] = 'a.status=1';
			if($showAll == 0){
				$param[] = 'a.code_quantity > 0';
			}
			$orderBy = "";
			$campaign = self::getCampaign($tokens);
			if(isset($params['orderBy']) && in_array($params['orderBy'],['new','popular','priceDesc','priceAsc','default','category','populars'])){
				switch ($params['orderBy']){
					case "new": $orderBy = " a.id desc";break;
					case "popular": $orderBy = " a.view desc";break;
					case "priceDesc": $orderBy = " a.price desc";break;
					case "priceAsc": $orderBy = " a.price Asc";break;
					case "category": $orderBy = " a.cat_id Asc";break;
					case "populars": $orderBy = " a.is_hot DESC";break;
					default: break;
				}
			}
			if(isset($params['orderBy']) && $params['orderBy']=='populars'){
				$param[] = 'a.is_hot =1';
			}

			if(isset($params['priceRange'])) {
				$array_range = explode('-', $params['priceRange']);
				if (count($array_range) != 2) {
					$array_range[0] = 0;
					$array_range[1] = 60000;
				}
				$pFrom = $campaign['exchange_rate'] * $array_range[0];
				$pTo = $campaign['exchange_rate'] * $array_range[1];
				if($pTo != 60000 && $pFrom != 0) {
					$param[] = "a.price BETWEEN $pFrom AND $pTo";
				}
			}

			if(isset($params['keyword']) && !empty($params['keyword'])){
				$param[]= "(a.title like '%{$params['keyword']}%' OR b.title like '%{$params['keyword']}%')";
			}

			if(isset($params['isPhysical']) && in_array($params['isPhysical'],[1,2])){
				if($params['isPhysical'] == 1) {
					$param[] = "a.isPhysical={$params['isPhysical']}";
				}else{
					$param[] = "(a.isPhysical={$params['isPhysical']} OR a.type=9)";
				}
			}

			$id_gift_set = $campaign['giftset_id'];
			if ($cat_id > 0 ) {
				$cat_arr[$cat_id] =  $cat_id;
				$catData = DB::query("SELECT * FROM ".T_CATEGORY." WHERE status=1 AND parent_id=".$cat_id);
				if($catData){
					while ($rw = $catData->fetch(PDO::FETCH_ASSOC)) {
						$cat_arr[$rw['id']] =  $rw['id'];
					}
				}
				if(!empty($cat_arr)){
					$param[] = 'a.cat_id IN('.implode(',',$cat_arr).')';
				}
			}
			$id_gift_detail = array(0);
			$id_gifts = [];
			#Lay them phan chi ban theo GIFT_SET
			$s_giftset = "SELECT gift_detail_id FROM ".T_GIFT_SET." WHERE gift_id=".$id_gift_set ." order by id desc";
			$getSiteGift = DB::query($s_giftset);
			if($getSiteGift){
				while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)){
					$id_gift_detail[] = $row['gift_detail_id'];
				}
				if(!empty($id_gift_detail)){
					$id_gifts = array_merge($id_gifts,$id_gift_detail);
				}
			}

			//neếu quà vật lý thì ko tìm location
			if(!isset($params['isPhysical']) || $params['isPhysical'] != 2) {
				#loc theo LOCATION
				$allow = self::getAccessLoc();
				$curLoc = self::getCurrentLoc();
				if ($allow == 1 && count($curLoc) == 2) {
					$brand_id = self::getNearBrand($cat_id);
					if (empty($brand_id)) $brand_id = [0];
					$param[] = "a.brand_id in (" . implode(",", $brand_id) . ")";
					$param[] = "b.id in (" . implode(",", $brand_id) . ")";
				} else {
					$city_id = CookieLib::get("L_MY_CITY");
					if ($city_id != 0) {
						$where_c = ["gift_detail_id IN (" . implode(',', $id_gift_detail) . ")"];
						if ($city_id == -1) {
							// tinhr thanh khac
							$provide = self::allProvide(true);
							$provide_id = array_column($provide, 'id');
							if (count($provide_id) > 0) {
								$where_c[] = "  city_id NOT IN(" . implode(',', $provide_id) . ")";
							}
						} elseif ($city_id != 0) {
							$where_c[] = " (city_id={$city_id} OR city_id=0)";
						}

						$giftId2 = array();
						$sql_city = "SELECT gift_detail_id FROM " . T_GIFT_CITY;
						$where_condition = FunctionLib::addCondition($where_c, true);
						$sql_city .= $where_condition;
						$stmt = DB::query($sql_city);
						if ($stmt) {
							while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
								$giftId2[$row['gift_detail_id']] = $row['gift_detail_id'];
							}
							if (!empty($giftId2)) {
								$id_gifts = $giftId2;
								//$id_gifts = array_merge($id_gifts, $giftId2);
							} else {
								$id_gifts = [0];
								// $id_gifts = array_merge($id_gifts, [0]);
							}
						}
					}
				}
			}
			$id_gifts = array_unique($id_gifts);
			//loại bỏ các quà ra khỏi dách sách( đối với MSB, bỏ quà cashback)
			if(isset($params['subID']) && is_array($params['subID'])){
				$id_gifts = array_diff($id_gifts,$params['subID']);
			}
			$param[] = "a.id in (". implode(",",$id_gifts) . ")";
			$condition =  FunctionLib::addCondition($param, true);
			//LEFT JOIN " . T_BRAND_IMAGE . " d on b.id=d.brand_id AND d.type=" . IS_ON . " AND d.status=" . IS_ON . "
			//if(a.isPhysical = 2,a.avatar,d.image) as image
			if(isset($params['gift_quantity']) && $params['gift_quantity']  > 0) {
				$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,if(d.code_quantity is NULL,a.code_quantity,if(a.code_quantity=0,0,d.code_quantity)) as `gift_quantity`,openTime,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category, a.avatar as image
					FROM " . T_GIFT_DETAIL . " a
					LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
					LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
					 LEFT JOIN ". T_GIFT_QUANTITY . " d on d.gift_detail_id=a.id and d.status =2 and d.app_id={$params['gift_quantity']}
					$condition
        			ORDER BY gift_quantity <= 0 asc," . ($orderBy != '' ? $orderBy : "FIELD(a.id," . implode( ',', $id_gifts ) . ")");
				$re = PagingGB::query($sql, $pages['per_page'],$pages['page_num'],'a.id,d.code_quantity as gift_quantity');

			}else{
				$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,a.code_quantity,openTime,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category, a.avatar as image
					FROM " . T_GIFT_DETAIL . " a
					LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
					LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
					$condition
        			ORDER BY a.code_quantity <= 0 asc," . ($orderBy != '' ? $orderBy : "FIELD(a.id," . implode(',', $id_gifts) . ")");
				$re = PagingGB::query($sql, $pages['per_page'],$pages['page_num'],'a.id');

			}
			$data = array();
			if($re && PagingGB::$totalPage >= $pages['page_num']){
				while ($row = $re->fetch(PDO::FETCH_ASSOC)){
					$images = MediaUrl::fromImageTitle($row['logo'],[160]);
					$banner = MediaUrl::fromImageTitle($row['image'],[640]);
					if(isset($row['gift_quantity'])){
						$code_quantity = $row['gift_quantity'];
					}else{
						$code_quantity = $row['code_quantity'];
					}
					$temp = array(
						'id'=>$row['id'],
						'brand_id'=>$row['brand_id'],
						'cat_id'=>$row['cat_id'],
						'gift_id'=>$row['gift_id'],
						'title'=> (isset($params['length']) && $params['length'] > 0) ?StringLib::truncateHtml($row['title'],(int)$params['length']):$row['title'],
						'openTime'=>$row['openTime'],
						'is_hot'=>$row['is_hot'],
						'isPhysical'=>$row['isPhysical'],
						'price'=>$row['price'],
						'price_point'=> self::point_format($row['price']/$campaign['exchange_rate']),
						'view'=>$row['view'],
						'image'=>isset($images[160])?$images[160]:'',
						'banner'=>isset($banner[640])?$banner[640]:'',
						'brand_title' => !empty($row['brand_title'])?$row['brand_title']:"&nbsp;",
						'code_quantity' => $code_quantity
					);
					$content = Gift::getContent(Language::$activeLang,$row['gift_id']);
					if($content['title'] != "" && Language::$activeLang != "vi"){
						$temp['title'] = (isset($params['length']) && $params['length'] > 0) ?StringLib::truncateHtml($content['title'],(int)$params['length']):$content['title'];
					}
					$temp['content'] = $content;
					$data[$row['id']] = $temp;
				}
			}
			$gift_id = array_keys($data);
			if(!empty($gift_id)) {
				$data = self::changePrice($data,["rate" => $campaign['exchange_rate'], "app_id" => $tokens['site_id']]);
			}

			return $data;
		}

		static function getGiftById($tokens,$id=0){
			$id = (int) $id;
			if($id <=0) return [];
			if(empty($tokens)) return [];
			$campaign = self::getCampaign($tokens);
			$getSiteGift = DB::query("SELECT gift_detail_id FROM ".T_GIFT_SET." WHERE gift_detail_id=$id AND gift_id=".$campaign['giftset_id']);
			$id_gift_detail = array();
			if($getSiteGift){
				while ($row = $getSiteGift->fetch(PDO::FETCH_ASSOC)){
					$id_gift_detail[] = $row['gift_detail_id'];
				}
			}
			$gift = [];
			if(in_array($id,$id_gift_detail)){
				$gift = GiftDetail::get($id);
				if(!empty($gift)){

					$query = DB::query( "SELECT code_quantity FROM " . T_GIFT_QUANTITY . " WHERE app_id={$tokens['site_id']} and gift_detail_id={$gift['id']} and status=".IS_ON." order by id DESC" )->fetch(PDO::FETCH_ASSOC);
					if(!empty($query)){
						$code_quantity = $query['code_quantity'];
						if($code_quantity > $gift['code_quantity']){
							$code_quantity =  $gift['code_quantity'];
						}
						$gift['code_quantity'] = $code_quantity;
					}

					$price = self::changePrice([],['id'=>$gift['id'],"price"=>$gift['price'],"rate" => $campaign['exchange_rate'], "app_id" => $tokens['site_id']]);
					$gift['price'] = $price['price'];
					$gift['price_point'] = $price['price_point'];
					$gift['price_promo'] = $price['price_promo'];
					$gift['point_promo'] = $price['point_promo'];
					$gift['is_promo'] = $price['is_promo'];
					DB::update(T_GIFT_DETAIL,array('view'=>$gift['view']+1),'id='.$id);
					$stmtCombo = DB::query("SELECT b.gift_detail_id,b.quantity FROM ".T_GIFT. " a LEFT JOIN ".T_CART_TEMPLATE_DETAIL ." b on a.cart_template_id=b.cart_template_id WHERE a.cart_template_id >0 AND a.id={$gift['gift_id']} GROUP BY b.gift_detail_id");
					while ($cb = $stmtCombo->fetch(PDO::FETCH_ASSOC)){
						$gift_template[$cb['gift_detail_id']] = $cb['quantity'];
					}

					$cbo_array = [];

					if(!empty($gift_template)) {
						$id_gift_template = array_keys($gift_template);
						$str_id_gift_template = implode(',',$id_gift_template);
						$gift_combo = DB::query("SELECT a.id,a.title,a.avatar,view,c.title as category, a.gift_id,a.cat_id
                        FROM " . T_GIFT_DETAIL . " a
                        LEFT JOIN ".T_BRAND." b on a.brand_id=b.id
                        LEFT JOIN ".T_CATEGORY." c on b.cat_id=c.id
                        WHERE a.id in({$str_id_gift_template})
                      ");
						while ($cbo = $gift_combo->fetch(PDO::FETCH_ASSOC)){
							$cbo_img = MediaUrl::fromImageTitle($cbo['avatar'],[640]);
							$cbo['image'] = isset($cbo_img[640]) ? $cbo_img[640] : '';
							$cbo['qty'] = isset($gift_template[$cbo['id']])? $gift_template[$cbo['id']]:1;
							$cbo_array[$cbo['id']] = $cbo;
						}
					}
					$gift['ComboGift'] = $cbo_array;

                    $content = Gift::getContent(Language::$activeLang, $gift['gift_id']);
                    if ($content['title'] != "" && Language::$activeLang !='vi') {
                        $gift['title'] = $content['title'];
                    }

					$gift['brand'] = DB::fetch("SELECT a.title,a.logo,a.created,a.openTime,a.priceRange,a.online,b.title as cat_title FROM ".T_BRAND." a LEFT JOIN ". T_CATEGORY ." b on a.cat_id=b.id WHERE a.id=".$gift['brand_id']);
					$gift['content'] = StringLib::post_db_parse_html($content['content']);
					$gift['note'] = StringLib::post_db_parse_html($content['note']);
					$gift['cat'] = DB::fetch("SELECT id,title from ". T_CATEGORY .  " WHERE id={$gift['cat_id']} and status=1 limit 1");
					$s_image = "SELECT image from " . T_GIFT_IMAGE . " WHERE gift_detail_id={$id} and image !='' and status=". IS_ON;;
					$stmt_image = DB::query($s_image);
					$brand_image = [];
					while ($ri = $stmt_image->fetch(PDO::FETCH_ASSOC)){
						$banner = MediaUrl::fromImageTitle($ri['image'],[640]);
						if(isset($banner[0]) && $banner[0] != ""){
							$brand_image[] = $banner[0];
						}
					}
					$images = MediaUrl::fromImageTitle($gift['brand']['logo'],[80]);
					$gift['brand']['logo'] = isset($images[80]) ? $images[80] : '';
					$gift['brand']['banner'] = $brand_image;
				}
			}
			return $gift;
		}
		static function getBrandGiftSet($tokens = null)
		{
			if ($tokens == null) return false;
			$campaign = Loyalty::getCampaign($tokens);
			$param = [];
			$param[] = 'a.code_quantity > 0';
			$param[] = 'a.status=1';
			// gift
			$param[] = 'b.status in (1,2)';
			$id_gift_set = isset($campaign['gift_id']) && $campaign['gift_id'] > 0 ? $campaign['gift_id'] : 0;
			if($id_gift_set > 0) {
				$getSiteGift = DB::query("SELECT gift_detail_id FROM " . T_GIFT_SET . " WHERE status=". IS_ON ." and gift_id=" . $id_gift_set);
				if ($getSiteGift) {
					$id_site_gift = $getSiteGift->fetchAll(PDO::FETCH_ASSOC);
					$id_gift_detail = array_column($id_site_gift, 'gift_detail_id');
				}
				if (!empty($id_gift_detail)) {
					$param[] = " a.id IN (" . implode(',', $id_gift_detail) . ")";
				}
			}else{

				$s = "SELECT * FROM " . T_GIFT_LIMIT . " WHERE app_id =" . $tokens['site_id'];
				$stmt = DB::query($s);
				$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
				$gift_gift = [];
				$gift_brand = [];
				foreach ($data as $item){
					if($item['type'] == 2){
						array_push($gift_gift,$item['gift_detail_id']);
					}else{
						array_push($gift_brand,$item['brand_id']);
					}
				}
				if(count($gift_gift) > 0)
					$param[] = "a.id NOT IN (" . implode(",", $gift_gift) . ")";
				if(count($gift_brand) > 0)
					$param[] = " a.brand_id NOT IN (" . implode(",", $gift_brand) . ")";

			}


			$condition = FunctionLib::addCondition($param, true);
			$sql = "SELECT a.brand_id,a.id FROM " . T_GIFT_DETAIL . " a LEFT JOIN ". T_GIFT ." b on a.gift_id=b.id " .  $condition ;
			$re = DB::query($sql);
			$brands = $re->fetchAll(PDO::FETCH_ASSOC);
			return $brands;
		}
		static function getGiftOfSet($id,$tokens){
			if($id <=0) return [];
			if(empty($tokens)) return [];
			$camp = Loyalty::getCampaign($tokens);
			$data = DB::fetch("SELECT id from " . T_GIFT_SET .  " WHERE gift_id={$camp['giftset_id']} and gift_detail_id = {$id}");
			if(empty($data) || count($data) <=0 ) return false;
			return true;
		}
		static function allProvide($is_city = false)
		{
			$where = "WHERE status = 2 AND parent_id =0 ";
			if ($is_city == true) {
				$where .= " AND is_city=1 ";
			}
			$sql = "SELECT id,title from " . T_PROVINCE . " $where ORDER BY safe_title asc ";
			$stmt = DB::query($sql);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
			return $data;
		}
		static function allDistrict($city_id = 0){
			$city_id = (int) $city_id;
			$where = "";
			if ($city_id != 0) {
				$where = " AND city_id=$city_id ";
			}
			$sql = "SELECT id, title from " . T_PROVINCE . " WHERE status=".IS_ON." $where ORDER BY title asc";
			$stmt = DB::query($sql);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
			return $data;
		}

		/** ------------------
		 * @function voucher cua tôi
		 * @param $site_user_id int
		 * @param $expired int 1 => available voucher, 2 => sắp hết hạn, 3 hết han
		 * @return null|array
		 */
		static function myVoucher($site_user_id = null,$expired = 1){
			if(empty($site_user_id)) return [];
			// $start_time = microtime(true);
			$rec = DB::query("SELECT cart_id FROM ".T_LOYAL_ORDER." WHERE (receiver_id IS NULL OR receiver_id=0) AND pay_status=3 AND status=".IS_ON." AND site_user_id='$site_user_id'");
			$carts_id = [];
			if($rec){
				$carts_id = $rec->fetchAll(PDO::FETCH_COLUMN);
			}
			$array = [];
			if(!empty($carts_id)) {
				$carts_id = array_unique($carts_id);
				$arr_id_gift_detail = [];
				$recd = DB::query("SELECT gift_detail_id FROM " . T_CART_DETAIL . " WHERE pay_status=" . IS_YES . " AND status=" . IS_ON . " AND cart_id IN(" . implode(',', $carts_id) . ") ORDER BY id DESC");
				if ($recd) {
					$arr_id_gift_detail = $recd->fetchAll(PDO::FETCH_COLUMN);
				}
				$arr_id_gift_detail = array_unique($arr_id_gift_detail);
				if (empty($arr_id_gift_detail)) $arr_id_gift_detail = [0];
				// echo microtime();
				$five_day_after = time() + (5 * 24 * 60 * 60);
				$five_day_ago = time() - (5 * 24 * 60 * 60);
				$today = time();
				$cond = [];
				$cond[] = "b.id IN (" . implode(',', $arr_id_gift_detail) . ")";
				$cond[] = "c.cart_id IN (" . implode(',', $carts_id) . ")";
				$cond[] ='b.isPhysical=1';
				if ($expired == 1) {
					$cond[] = "c.delivery <> 3";
					$cond[] = "(d.expired =0 OR d.expired > $five_day_after OR d.expired is null)";
					$cond[] = "(d.status = 1 or d.status is null)";
				} elseif ($expired == 2) {
					$cond[] = "(c.delivery = 1 OR c.delivery=2)";
					$cond[] = "(d.expired > 0 AND d.expired between $today AND $five_day_after)";
					$cond[] = "d.status = 1";
				}
				$condString  = FunctionLib::addCondition($cond);
				$sql = "
            SELECT * FROM(
            SELECT a.id,a.title ,a.logo, count(c.id) AS soluong,b.title as gift_name,b.gift_id,max(c.id) AS cart_detail_id,expired,max(e.created) AS created,d.id AS code_id
                FROM " . T_BRAND . " a
                LEFT JOIN " . T_GIFT_DETAIL . " b on a.id=b.brand_id
                LEFT JOIN " . T_CART_DETAIL . " c on b.id=c.gift_detail_id
                LEFT JOIN " . T_GIFT_CODE . " d on c.id=d.cart_detail_id
                LEFT JOIN " . T_CART . " e on c.cart_id=e.id
                where $condString
                group by a.id
            ) AS t order by cart_detail_id DESC";

				//if($expired == 2) echo $sql;
				//voucher đã hết hạn sử dụng nhưng chưa dùng
				if($expired== 3) $sql = "SELECT a.id,a.title,b.title as gift_name,b.gift_id,b.avatar ,a.logo, c.id as cart_detail_id,expired,d.id as code_id
                        FROM ". T_BRAND . " a
                        LEFT JOIN ". T_GIFT_DETAIL . " b on a.id=b.brand_id
                        LEFT JOIN ". T_CART_DETAIL ." c on b.id=c.gift_detail_id
                        LEFT JOIN " . T_GIFT_CODE . " d on c.id=d.cart_detail_id
                        WHERE b.id IN (". implode(',', $arr_id_gift_detail) . ")
                            AND c.cart_id IN (". implode(',', $carts_id) . ") AND c.status = 2
                            AND (c.delivery = 1 OR c.delivery=2)
                            AND (d.expired > 0 AND d.expired between $five_day_ago AND $today)
                            and d.status = 1
                            order by expired desc";
				$stmt = DB::query($sql);

				$brands = [];
				while ($item = $stmt->fetch(PDO::FETCH_ASSOC)) {
					$images = MediaUrl::fromImageTitle($item['logo'],[160]);
					$item['image'] = isset($images['160'])? $images['160']: "";
					if(isset($item['avatar'])){
						$avt = MediaUrl::fromImageTitle($item['avatar'],[320]);
						$item['avatar'] = isset($avt['320'])? $avt['320']: "";
					}
					if(Language::$activeLang != "vi") {
						$content = Gift::getContent(Language::$activeLang,$item['gift_id']);
						if (isset($content['title']) && $content['title'] != '') {
							$item['gift_name'] = $content['title'];
						}
					}
					$brands[] = $item;
				}
				$array['brand'] = $brands;
				// $end_time = microtime(true);
			}
			return $array;
		}

		static function desktopVoucher($site_user_id = null,$campaign = [],$group = false){
			if(empty($site_user_id)) return [];
			$rec = DB::query("SELECT cart_id FROM ".T_LOYAL_ORDER." WHERE (receiver_id IS NULL OR receiver_id=0) AND pay_status=3 AND status=".IS_ON." AND site_user_id='$site_user_id' AND campaign_id={$campaign['id']}");
			$carts_id = [];
			if($rec){
				$carts_id = $rec->fetchAll(PDO::FETCH_COLUMN);
			}
			if(empty($carts_id)) return [];
			$id_string = implode(',',$carts_id);
			$today = TIME_NOW;
			$cond = [];
			$cond[] = "((a.delivery <> 3 and b.isPhysical != 2 AND  b.type != 9) OR (b.isPhysical = 2 OR  b.type = 9) )";
			$cond[] = "a.cart_id in ($id_string)";
			$cond[] = "(c.expired =0 OR c.expired > $today OR c.expired is null)";
			$cond[] = "b.status = 1";
			$cond[] = "(a.status = 2 OR a.status = 3) and a.pay_status=2";
			#$cond[] = "(b.isPhysical=1 AND b.type != 9)";
			$cond[] = "c.status =1";
			$condString = FunctionLib::addCondition($cond);

			$sql = "SELECT  a.id,a.app_id,a.status,a.gift_detail_id,a.delivery,a.pay_status, b.title,a.price,b.avatar,b.id as gift_id,b.gift_id as gift_id_parent,b.isPhysical,b.type, c.expired,a.created,d.logo as brand_logo,d.id as brand_id,d.title as brand_name
					FROM " . T_CART_DETAIL . " a
					LEFT JOIN " . T_GIFT_DETAIL . " b ON a.gift_detail_id=b.id
					LEFT JOIN " . T_GIFT_CODE . " c ON c.cart_detail_id=a.id AND c.status >=0
					LEFT JOIN " . T_BRAND . " d ON d.id=a.brand_id
					WHERE $condString
					ORDER BY a.id DESC
		";
			$child = DB::query($sql);
			$data = [];
			while ($row = $child->fetch(PDO::FETCH_ASSOC)) {
				$images = MediaUrl::fromImageTitle($row['avatar'], [640]);
				$row['image'] = isset($images[640]) ? $images[640] : '';
				$logo = MediaUrl::fromImageTitle($row['brand_logo'], [320]);
				$row['brand_logo'] = isset($logo[320]) ? $logo[320] : '';
				if(Language::$activeLang != "vi") {
					$content = Gift::getContent(Language::$activeLang,$row['gift_id_parent']);
					if (isset($content['title']) && $content['title'] != '') {
						$row['title'] = $content['title'];
					}
				}
				$row['price_point'] = self::point_format($row['price']/$campaign['exchange_rate']);
				$row['qty'] = 1;
				$data[$row['id']] = $row;
			}
			if(!empty($data)) {
				$firstRow = current($data);
				$data = self::changePrice( $data, ["rate" => $campaign['exchange_rate'], "app_id" => $firstRow['app_id'],'field'=>'gift_detail_id'] );
				$cart_detail_ids = array_keys($data);
				if(!empty($cart_detail_ids)) {
					$str_cart_detail_id  = implode(",",$cart_detail_ids);
					$delivery = DB::fetch_all( "SELECT cart_detail_id as id,process FROM " . T_DELIVERY_DETAIL . " WHERE cart_detail_id in($str_cart_detail_id)" );
					foreach ($data as &$da){
						if(isset($delivery[$da['id']])){
							$da['delivery_info'] = $delivery[$da['id']];
						}else{
							$da['delivery_info'] = [];
						}
					}
				}
			}
			if($group == true) {
				$voucher = [];
				foreach ($data as $item) {
					if (isset( $voucher[$item['brand_id']] )) {
						$voucher[$item['brand_id']]['qty'] += 1;
					} else {
						$voucher[$item['brand_id']] = $item;
						$voucher[$item['brand_id']]['qty'] = 1;
					}
				}
				return $voucher;
			}
			return $data;
		}
		/*Lấy danh sách quà vật lý đã mua*/
		static function physicalGift($site_user_id = null){
			if(empty($site_user_id)) return [];
			if(Language::$activeLang == 'vi') {
				$s = "SELECT  b.id,gift_detail_id,avatar,title,receive_code
					FROM " . T_LOYAL_ORDER . " a
					LEFT JOIN " . T_CART_DETAIL . " b ON a.cart_id=b.cart_id
					LEFT JOIN " . T_GIFT_DETAIL . " d  ON b.gift_detail_id=d.id
					WHERE a.pay_status=3 and b.pay_status=" . IS_NO . " and site_user_id='{$site_user_id}' and d.isPhysical=" . IS_YES ;//.  and d.justGetOrder= . IS_YES;
			}else{
				$s = "SELECT  b.id,b.gift_detail_id,avatar,d.title as vi_title, e.title,receive_code
					FROM " . T_LOYAL_ORDER . " a
					LEFT JOIN " . T_CART_DETAIL . " b ON a.cart_id=b.cart_id
					LEFT JOIN " . T_GIFT_DETAIL . " d  ON b.gift_detail_id=d.id
					LEFT JOIN " . T_GIFT_CONTENT . " e  ON e.gift_id=d.gift_id
					WHERE a.pay_status=3 and b.pay_status=" . IS_NO . " and site_user_id='{$site_user_id}' and d.isPhysical=" . IS_YES;// .  and d.justGetOrder= . IS_YES;
			}
			$data = [];
			$stmt = DB::query($s);
			while($row = $stmt->fetch(PDO::FETCH_ASSOC)){
				$images = MediaUrl::fromImageTitle($row['avatar'],[640]);
				$row['image'] = isset($images['640'])? $images['640']: "";
				$row['link'] = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
				$data[] = $row;
			}
			return $data;
		}
		//chi tiêt voucher - mobile
		static function detailVoucher($site_user_id,$campaign_id = 0,$exchange_rate = 1,$id = 0){
			$id = (int)$id;
			if($id <=0 || $campaign_id <=0 || empty($site_user_id)) return [];
			$array = [];

			$carts_id = DB::query("SELECT cart_id FROM ".T_LOYAL_ORDER." WHERE campaign_id='$campaign_id' AND  (receiver_id IS NULL OR receiver_id=0) AND pay_status=3 AND status=".IS_ON." AND site_user_id='$site_user_id'")->fetchAll(PDO::FETCH_COLUMN);
			if(empty($carts_id)) return [];
			$id_string = implode(',',$carts_id);
			$day_limit = TIME_NOW + (0 * 24 * 60 * 60);
			$cond = [];
			$cond[] = "a.id =$id AND a.pay_status = ".IS_ON." AND (a.status=3 OR a.status=".IS_ON. ")";
			$cond[] = "a.cart_id in($id_string)";
			$cond[] = "(b.status = 1 or b.status is null)";
			$cond[] = "(((a.delivery = 1 OR a.delivery=2 OR a.delivery=9) AND c.id is NULL) OR c.id  > 0)";
			$cond[] = "(b.expired = 0 OR b.expired >= $day_limit OR b.expired is null)";

			//chỉ lấy đơn hàng voucher, ko lấy vật lý ra
			//$cond[] = "c.isPhysical=1";
			$condString = FunctionLib::addCondition($cond);
			#$start  = microtime(true);
			$sqlCart = "SELECT a.id,a.cart_id,a.status,a.receiver_id,a.money,a.gift_id,a.receive_code,a.brand_id,a.gift_detail_id,a.created,a.app_id,a.delivery_city,a.delivery_district,a.delivery_ward,a.delivery_address,a.delivery_note,b.expired,b.code,b.id as code_id,b.valid_time,b.used,a.delivery_required,a.delivery,c.process,d.transaction_id
						FROM ".T_CART_DETAIL . " a
						LEFT JOIN ". T_GIFT_CODE . " b on a.id=b.cart_detail_id
						LEFT JOIN " . T_DELIVERY_DETAIL . " c on a.id=c.cart_detail_id
						LEFT JOIN " . T_CART . " d on a.cart_id=d.id
						WHERE $condString ORDER BY a.id desc";
			$row = DB::query($sqlCart)->fetch(PDO::FETCH_ASSOC);
			#$end = microtime(true);



			if(!empty($row)){
				$gift = DB::query("SELECT a.id,a.type,title,avatar,a.price,a.isPhysical,b.showbarcode,a.cat_id,a.type,code_type from " . T_GIFT_DETAIL . " a LEFT JOIN ".T_GIFT." b on a.gift_id=b.id where a.id={$row['gift_detail_id']}")->fetch(PDO::FETCH_ASSOC);
				$images = MediaUrl::fromImageTitle($gift['avatar'],[640]);
				$gift['image'] = isset($images[640]) ? $images[640] : '';

				$row['gift'] = $gift;

				if($row['used'] == 0){
					DB::update(T_GIFT_CODE,['last_time'=>TIME_NOW],"id={$row['code_id']}");
				}
				$row['brand'] = Loyalty::getBrandById($row['brand_id'],$row['gift_id']);
				if($gift['type'] == 9 || $gift['isPhysical'] == 2){
					$s_image = "SELECT image from " . T_GIFT_IMAGE . " WHERE gift_detail_id={$gift['id']} and image  != '' and status=". IS_ON;
					$stmt_image = DB::query($s_image);
					$brand_image = [];
					while ($ri = $stmt_image->fetch(PDO::FETCH_ASSOC)){
						$banner = MediaUrl::fromImageTitle($ri['image'],[640]);
						if(isset($banner[640]) && $banner[640] != ""){
							$brand_image[] = $banner[640];
						}
					}
					$row['brand']['brand_image'] = $brand_image;
					if(isset($row['brand']['cat_id']) && $row['brand']['cat_id'] > 0) {
						$cat = DB::fetch( "SELECT id,title FROM " . T_CATEGORY . " WHERE id={$row['brand']['cat_id']}" );
						$row['brand']['cat_title'] = $cat['title'];
					}else{
						$row['brand']['cat_title'] = "";
					}

					if($row['receiver_id'] > 0){
						//delivery_city,a.delivery_district,a.delivery_ward,a.delivery_add
						$city = DB::fetch("SELECT title FROM " . T_PROVINCE . " WHERE id= {$row['delivery_city']}",'title');
						$district = DB::fetch("SELECT title FROM " . T_DISTRICT . " WHERE id= {$row['delivery_district']}",'title');
						//$ward = DB::fetch("SELECT title FROM " . T_WARD . " WHERE id= {$row['delivery_ward']}",'title');
						$ward = $row['delivery_ward'];
						$add = [
							$row['delivery_address'],
							$ward,
							$district,
							$city
						];
						$add = array_unique($add);
						$add = implode(", ",$add);
						#$receiver = DB::fetch("SELECT * FROM " . T_GIFT_RECEIVER . " WHERE id= {$row['receiver_id']}");
						#$row['receiver'] = $receiver;
						$row['address'] = $add;
						$cartNote = DB::fetch("SELECT fullname,phone FROM " . T_CART . " WHERE id={$row['cart_id']}");
						$row['fullname'] = $cartNote['fullname'];
						$row['phone'] = $cartNote['phone'];
					}

				}

				$row['price_point'] = self::point_format($row['money']/$exchange_rate);
				if($row['code'] == null || $row['code'] == ""){
					$code = "";
					GiftCode::assignCode($row['id'],$code);
					$row['code'] = $code;
				}
				$row['code'] = System::decrypt($row['code']);
				$row['link'] = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
				$content = Gift::getContent(Language::$activeLang,$row['gift_id']);

				$row['content'] =  html_entity_decode($content['content']);
				$row['note'] = html_entity_decode($content['note']);
				if(Language::$activeLang != "vi"){
					$row['gift']['title'] = $content['title'];
				}
				$prices = self::changePrice( [], ['id'=>$row['gift_detail_id'],"rate" => $exchange_rate,'price'=>$row['money'], "app_id" => $row['app_id']]);
				if(isset($prices['price_point'])){
					$row['price_point'] = $prices['price_point'];
				}
				$array = $row;
			}
			#echo $end-$start;


			return $array;
		}
		static function usedVoucher($site_user_id){
			if($site_user_id <=0) return [];
			$rec = DB::query("SELECT cart_id FROM ".T_LOYAL_ORDER." WHERE (receiver_id IS NULL OR receiver_id=0) AND pay_status=3 AND status=".IS_ON." AND site_user_id='$site_user_id'");
			$carts_id = [];
			if($rec){
				$carts_id= $rec->fetchALl(PDO::FETCH_COLUMN);
			}
			$gifts_details = [];
			if(!empty($carts_id)) {
				$sql = "SELECT a.id,b.gift_id,gift_detail_id,avatar,title,receive_code FROM " . T_CART_DETAIL . " a LEFT JOIN " . T_GIFT_DETAIL . " b on a.gift_detail_id=b.id WHERE delivery=3 and a.status=" . IS_ON . "  AND  b.isPhysical =1 AND cart_id IN(" . implode(',', $carts_id) . ")";
				$recd = DB::query($sql);
				if ($recd) {
					while ($row = $recd->fetch(PDO::FETCH_ASSOC)) {
						#$row['code'] = System::decrypt($row['code'],CRYPT_KEY);
						$row['link'] = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
						$images = MediaUrl::fromImageTitle($row['avatar'],[160]);
						$row['image'] = isset($images[160]) ? $images[160] : '';
						//$row['image'] = "";
						if(Language::$activeLang != "vi"){
							$content = Gift::getContent(Language::$activeLang,$row['gift_id']);
							if($content['title'])$row['title'] =   $content['title'];
						}
						$gifts_details[] = $row;
					}
				}
			}
			return $gifts_details;
		}
		static function updateUse($token,$id){
			$card = [];
			if ($token != null) {
				$card = Loyalty::getToken($token);
			}
			if(empty($card)) return false;
			if($id <=0) return [];
			$loyalOrder = DB::query("SELECT cart_id from " . T_LOYAL_ORDER . " WHERE site_user_id='{$card['site_user_id']}' and pay_status=3 and campaign_id={$card['campaign_id']}")->fetchAll(PDO::FETCH_COLUMN);
			if(!empty($loyalOrder)) { // nếu user đó có đơn hàng mới check tiếp
				$str_cart = implode(",",$loyalOrder);
				$s = "SELECT id from " . T_CART_DETAIL . "  where id={$id} and cart_id in({$str_cart})";
				$stmt = DB::fetch( $s );
				if (!empty( $stmt ) && $stmt['id'] > 0) {
					$gift_code = DB::fetch( "SELECT id from " . T_GIFT_CODE . " where cart_detail_id={$stmt['id']} and status=1 and used =0 LIMIT 1" );
					if (!empty( $gift_code ) && $gift_code['id'] > 0) {
						$newRow = array('active' => 2, 'used' => TIME_NOW,'apiCall'=> 13);
						$code = DB::update( T_GIFT_CODE, $newRow, "id={$gift_code['id']}" );
						$newCartRow = array(
							'using_time' => TIME_NOW,
							'delivery' => 3,
						);
						$cart = DB::update( T_CART_DETAIL, $newCartRow, " id =" . $id );
						if ($code && $cart) return true;
					}
				}
			}
			return false;
		}
		static function assignCode($cart_detail_id){
			$cart_detail_id = (int)$cart_detail_id;
			if($cart_detail_id>0){
				$dataPost['idCartDetail'] = $cart_detail_id;
				$linkApi = LINK_URBOX_INTERNAL.'ajax.php?act=api&code=receivingGift';
				$curl = new CURL();
				$curl->post($linkApi,$dataPost);
			}
		}
		static function calcDistance($lat1, $lon1, $lat2 = null, $lon2 = null)
		{
			if ($lat2 == null) {
				$loc = self::getCurrentLoc();
				$lat2 = @$loc[0];
				$lon2 = @$loc[1];
			}
			if ($lat1 != "" && $lat2 != "" && $lon1 != "" && $lon2 != "") {
				$R = 6371;
				$dLat = deg2rad($lat2 - $lat1);
				$dLon = deg2rad($lon2 - $lon1);
				$a =
					sin($dLat / 2) * sin($dLat / 2) +
					cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
					sin($dLon / 2) * sin($dLon / 2);
				$c = 2 * atan2(sqrt($a), sqrt(1 - $a));
				$d = $R * $c; // Distance in km
				return round($d, 1);
			}
			return "-";
		}
		// trả về id các brand gần đây => lấy quà thuộc các thương hiệu này. => Tìm dc quà gần đây
		static function getNearBrand($cat = 0)
		{
			$my_loc = self::getCurrentLoc();
			if(count($my_loc) == 2) {
				$mylat = (float)$my_loc[0];
				$mylon = (float)$my_loc[1];
			}else{
				$mylat = 21.022736;
				$mylon = 105.8019441;
			}
			$where = [];
			if (!empty($brands)) {
				$brand_id = implode(",", $brands);
				$where[] = "a.brand_id IN($brand_id)";
			}

			if ($cat != null && $cat > 0){
				$cat = (int) $cat;
				$cat_ids = Balance::getCatId($cat);
				if(count($cat_ids) > 0) {
					$where[]= "b.cat_id IN (" . implode(",", $cat_ids) . ") ";
				}
			}
			$where[] = " a.status=" . IS_ON . " AND b.status= " . IS_ON;
			$expression = "(6371 * acos( cos( radians($mylat) ) * cos( radians( a.latitude ) ) * cos( radians( a.longitude ) - radians($mylon) ) + sin( radians($mylat) ) * sin( radians( a.latitude ) ) ) )";
			$where[]= "($expression <= " . USER_DISTANCE . " OR online=".IS_YES.")";
			$where_str = FunctionLib::addCondition($where);
			$sql = "SELECT  b.id
            FROM ".T_BRAND_OFFICE." a
            LEFT JOIN ".T_BRAND." b ON a.brand_id = b.id
            WHERE
               $where_str
               GROUP BY b.id HAVING b.id >0";

			$stmt = DB::query($sql);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
			if(!empty($data)) $data = array_column($data,'id');
			return $data;
		}

		function deg2rad($deg)
		{
			return $deg * (M_PI / 180);
		}

		static function getAccessLoc()
		{
			$allow_loc = CookieLib::get('L_ALLOW_LOCATION');
			return $allow_loc;
		}

		//brand ở trang chi tiết quà

		static function listBrands($brands, $gift_id= 0 )
		{
			$brands = (int)$brands;
			$access_loc = self::getAccessLoc();
			$my_loc = self::getCurrentLoc();
			// nếu cho phép truy cập vi trí
			if ($access_loc == 1 && count($my_loc) == 2) {
				$data = self::allBrandByLoc($brands,$gift_id );
			} else {
				$data = self::allBrand($brands, $gift_id);
			}
			// remake image brand
			return $data;
		}

		static function allBrand($brands = 0 ,$gift_id = 0)
		{
			$city_id = CookieLib::get("L_MY_CITY");
			$where  = [];
            $where[] = " a.brand_id=$brands AND a.status=" . IS_ON . " AND b.status=" . IS_ON;
			if($city_id == -1){
				// tinhr thanh khac
				$provide = self::allProvide(true);
				$provide_id =array_column($provide,'id');
				if(count($provide_id) > 0)
					$where[]= " (a.city_id NOT IN (".implode(",",$provide_id) .") OR online=".IS_YES.") ";
			}elseif ($city_id != 0) {
				$where[]= " (a.city_id={$city_id} OR online=".IS_YES.") ";
			}

            $office_id = self::getBrandOfficeApply($gift_id);
            if($office_id !== false){
                $where[] = "a.id in($office_id)";
            }
            $where_condition = FunctionLib::addCondition($where);
			$sql = "SELECT a.address,a.latitude,a.longitude,isApply
                FROM " . T_BRAND_OFFICE . " a
                LEFT JOIN ".T_BRAND." b on a.brand_id = b.id
                WHERE $where_condition";

			$stmt = DB::query($sql);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
			foreach ($data as &$item) {
				$item['distance'] = '-';
			}
			return $data;
		}
		static function allBrandByLoc($brands = 0,$gift_id = 0)
		{
			$my_loc = self::getCurrentLoc();
			$mylat = $my_loc[0];
			$mylon = $my_loc[1];
			$lon1 = $mylon - USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
			$lon2 = $mylon + USER_DISTANCE / abs(cos(deg2rad($mylat)) * 69);
			$lat1 = $mylat - (USER_DISTANCE / 69);
			$lat2 = $mylat + (USER_DISTANCE / 69);
            $office_id = self::getBrandOfficeApply($gift_id);
            $where = "";
            if($office_id !== false){
                $where = "a.id in($office_id) AND ";
            }

			$expression = "(6371 * acos( cos( radians($mylat) ) * cos( radians( a.latitude ) ) * cos( radians( a.longitude ) - radians($mylon) ) + sin( radians($mylat) ) * sin( radians( a.latitude ) ) ) )";
			$sql = "SELECT  a.address,a.latitude,a.longitude,isApply,
               ROUND($expression,1) as `distance`
            FROM ".T_BRAND_OFFICE." a
            LEFT JOIN ".T_BRAND." b on a.brand_id = b.id
            WHERE
                a.status=" . IS_ON . " AND
                a.brand_id =$brands AND
                $where
                a.longitude BETWEEN $lon1 AND $lon2
                AND a.latitude BETWEEN $lat1 AND $lat2
                AND ($expression <= " . USER_DISTANCE . " OR online=".IS_YES.")
            ORDER BY $expression";

			$stmt = DB::query($sql);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);

			return $data;
		}
		static function getOfficeByGift($gift_id,$brand_id){
			if($gift_id <=0 || $brand_id <= 0) return [];
			$GiftBrandOffice = DB::query("SELECT distinct(brand_office_id) as id FROM ". T_GIFT_BRAND_OFFICE . " WHERE brand_id='$brand_id' and gift_id='{$gift_id}'")->fetchAll(PDO::FETCH_ASSOC);
			if(!empty($GiftBrandOffice)){
				if(in_array(0,$GiftBrandOffice)){
					// nếu =0 => lấy tất cả office thuộc brand đó
					return [];
				}
				$city_id = CookieLib::get("L_MY_CITY");

				$cond = [];
				$GiftBrandOfficeId = array_column($GiftBrandOffice,'id');
				$str_brand_office = implode(',', $GiftBrandOfficeId);
				$cond[] = "a.id in($str_brand_office)";
				$cond[] = "a.status = 2";
				if($city_id == -1){
					// tinhr thanh khac
					$provide = self::allProvide(true);
					$provide_id =array_column($provide,'id');
					if(count($provide_id) > 0){
						$cond[] = " (a.city_id NOT IN (".implode(",",$provide_id) .") OR b.online=".IS_YES.") ";
					}
				}elseif ($city_id != 0) {
					$cond[] =" (a.city_id={$city_id} OR b.online=".IS_YES.") ";
				}
				$condStr = FunctionLib::addCondition($cond);
				return DB::query("SELECT a.id, address, isApply, latitude, longitude FROM ". T_BRAND_OFFICE . " a
				 				LEFT JOIN ". T_BRAND ." b on a.brand_id=b.id
				 WHERE $condStr ")->fetchAll(PDO::FETCH_ASSOC);
			}
			// nếu ko có office nào thì lấy tất cả brand
			return [];
		}
		static function setGiftToCache($id,$sub_fix='')
		{
			if($id <=0) return [];
			$gift_json = CookieLib::get('viewed_gifts'.$sub_fix);
			$gifts = [];
			if (!empty($gift_json) && $gift_json  != "null") {
				$gifts = json_decode($gift_json, true);
			}
			if (in_array($id, $gifts)) {
				foreach($gifts as $key=> $g){
					if($g == $id) unset($gifts[$key]);
				}
				$gifts = array_values($gifts);
			}
			array_unshift($gifts,$id);

			$expire = time() + (86400 * 7);
			CookieLib::set('viewed_gifts'.$sub_fix, json_encode($gifts), $expire);
		}
		static function removeGiftCache($id,$sub_fix='')
		{
			if($id <=0) return [];
			$gift_json = CookieLib::get('viewed_gifts'.$sub_fix);
			$gift_ids = [];
			if (!empty($gift_json)) {
				$gift_ids = json_decode($gift_json, true);
			}
			$index =  array_search($id,$gift_ids);
			unset($gift_ids[$index]);
			$expire = time() + (86400 * 7);
			CookieLib::set('viewed_gifts'.$sub_fix, json_encode($gift_ids), $expire);
		}
		static function getGiftFromCache($tokens,$isPhysic = 0,$length=0,$sub_fix ='')
		{
			$gift_json = CookieLib::get('viewed_gifts'.$sub_fix);
			if (empty($gift_json) || empty($tokens)) return null;
			$gift_id = json_decode($gift_json, true);
			$gift_id = array_filter($gift_id);
			if (empty($gift_id)) return null;
			foreach ($gift_id as &$item) {
				$item = (int)$item;
			}
			$gift_id = implode(",", $gift_id);
			$campaign = self::getCampaign($tokens);
			$param = array();
			$param[] = 'code_quantity>0';
			$param[] = 'a.status=1';
			$param[] = "a.id in ($gift_id)";

			if($isPhysic == 2){
				$param[] = 'a.isPhysical=2';
			}
			if($isPhysic == 1){
				$param[] = 'a.isPhysical=1';
			}

			$condition =  FunctionLib::addCondition($param, true);

			$sql = "SELECT a.id,a.brand_id,a.cat_id,a.gift_id,a.title,a.price,a.view,a.avatar,a.is_hot,a.isPhysical,b.logo,b.title as brand_title,c.title as category,a.isPhysical,a.avatar as image
        FROM " . T_GIFT_DETAIL . " a
        LEFT JOIN " . T_BRAND . " b on a.brand_id=b.id
        LEFT JOIN " . T_CATEGORY . " c on a.cat_id=c.id
        $condition ORDER BY FIELD(a.id," . $gift_id . ")";

			$re = DB::query($sql);
			$data = array();
			if ($re) {
				while ($row = $re->fetch(PDO::FETCH_ASSOC)) {
					$images = MediaUrl::fromImageTitle($row['logo'], [160]);
					$banner = MediaUrl::fromImageTitle($row['image'], [640]);
					$temp = array(
						'id' => $row['id'],
						'brand_id' => $row['brand_id'],
						'cat_id' => $row['cat_id'],
						'gift_id' => $row['gift_id'],
						'title' => $length > 0? StringLib::truncateHtml($row['title'],$length):$row['title'],
						'is_hot' => $row['is_hot'],
						'isPhysical' => $row['isPhysical'],
						'price' => $row['price'],
						'price_point' => self::point_format($row['price']/$campaign['exchange_rate']),
						'view' => $row['view'],
						'image' => isset($images[160]) ? $images[160] : '',
						'banner' => isset($banner[640]) ? $banner[640] : '',
						'brand_title' => !empty($row['brand_title'])?$row['brand_title']:"&nbsp;",
						'category' => $row['category']
					);
					$content = Gift::getContent(Language::$activeLang,$row['gift_id']);
					if($content['title'] != ""){
						$temp['title'] = $content['title'];
					}
					$temp['content'] = $content;
					$data[$row['id']] = $temp;
				}
				if(!empty($data))
					$data = self::changePrice($data,["rate" => $campaign['exchange_rate'], "app_id" => $tokens['site_id']]);
				return $data;
			}
		}
		static function changePrice($array,$option){
			$id = isset($option['id']) ? $option['id']: 0;
			$price = isset($option['price'])? $option['price']: 0;
			$rate = isset($option['rate']) && $option['rate'] !=0 ? $option['rate']: 1;
			$app_id = $option['app_id'];
			$field = isset($option['field'])?$option['field']:"id";
			$cond = [];
			$cond[] = "app_id={$app_id}";
			$cond[] = "status=".IS_ON;


			$conditions = FunctionLib::addCondition($cond);
			if(empty($array)) {
				$conditions .= " AND gift_detail_id ={$id}";
				$newPoint = DB::fetch("SELECT point,price,price_promo,point_promo,is_promo,start_promo,end_promo FROM " . T_GIFT_PRICE . " WHERE  $conditions order by id desc");
				$data['price_point'] = self::point_format($price/$rate);
				$data['price'] = $price;
				$data['price_promo'] = 0;
				$data['point_promo'] = 0;
				$data['is_promo'] = 1;
				if(!empty($newPoint)){
					$data['price_point'] = $newPoint['point'];
					$data['price'] = $newPoint['price'];
					$_salePrice = self::_calcPromoSale($newPoint);
					$data['price_promo'] = $_salePrice['price_promo'];
					$data['point_promo'] = $_salePrice['point_promo'];
					$data['is_promo'] = $newPoint['is_promo'];
				}
			}else{
				if($field =="id") {
					$gift_id = array_keys( $array );
				}else{
					$gift_id = array_column( $array,$field );
				}
				if(empty($gift_id)) $gift_id[] = 0;
				$string_id = implode(',',$gift_id);
				$conditions .= " AND gift_detail_id IN ({$string_id})";
				$sql  = DB::query("SELECT point,price,gift_detail_id,price_promo,point_promo,is_promo,start_promo,end_promo FROM " . T_GIFT_PRICE . " WHERE  $conditions order by id desc");
				while ($r = $sql->fetch(PDO::FETCH_ASSOC)){
                    if(!isset($array[$r['gift_detail_id']]['price_promo'])) {
                        if ($field == 'id') {
                            if (isset($array[$r['gift_detail_id']])) {
                                $array[$r['gift_detail_id']]['price_point'] = $r['point'];
                                $array[$r['gift_detail_id']]['price'] = $r['price'];
                                $_salePrice = self::_calcPromoSale($r);
                                $array[$r['gift_detail_id']]['price_promo'] = $_salePrice['price_promo'];
                                $array[$r['gift_detail_id']]['point_promo'] = $_salePrice['point_promo'];
                                $array[$r['gift_detail_id']]['is_promo'] = $_salePrice['is_promo'];
                            }
                        } else {
                            foreach ($array as &$datum) {
                                if ($datum[$field] == $r['gift_detail_id']) {
                                    $datum['price_point'] = $r['point'];
                                    $datum['price'] = $r['price'];
                                    $_salePrice = self::_calcPromoSale($r);
                                    $datum['price_promo'] = $_salePrice['price_promo'];
                                    $datum['point_promo'] = $_salePrice['point_promo'];
                                    $datum['is_promo'] = $_salePrice['is_promo'];
                                }
                            }
                        }
                    }
				}
				foreach ($array as &$item) {
					if(!isset($item['point_promo'])){
						$item['price_promo'] = 0;
						$item['point_promo'] = 0;
						$item['is_promo'] = 1;
					}
				}

				$data = $array;
			}
			return $data;

		}
		static function _calcPromoSale($data){
			if($data['start_promo'] > TIME_NOW || TIME_NOW > $data['end_promo']){
				$data['price_promo'] = 0;
				$data['point_promo'] = 0;
				$data['is_promo'] = 1;
			}
			return $data;
		}
		static function history1($card){
			if(empty($card)) return [];
			$data = DB::query("SELECT a.id,a.point,a.pay_status,a.cart_id,a.money,a.created FROM ". T_LOYAL_ORDER . " a WHERE a.site_user_id ='{$card['site_user_id']}' and status=".IS_ON." ORDER BY id desc")->fetchAll(PDO::FETCH_ASSOC);
			if(!empty($data)){
				$cartIds = array_unique(array_column($data,'cart_id'));
				$str_cartIds = implode(',',$cartIds);
				$sqlCart = "SELECT DISTINCT(a.cart_id) as id,count(a.cart_id) as qty,title,isPhysical,process
							from ". T_CART_DETAIL . " a
							LEFT JOIN ".T_GIFT_DETAIL." b on a.gift_detail_id = b.id
							LEFT JOIN  ". T_DELIVERY_DETAIL." c on c.cart_detail_id = a.id
							WHERE a.cart_id in ($str_cartIds) GROUP BY a.cart_id";
				$carts = DB::fetch_all($sqlCart);
				if(Language::$activeLang != "vi"){
					$giftIds = array_keys($carts);
					$giftContent = self::giftContent(Language::$activeLang,$giftIds);

					foreach ($carts as &$itemCart) {
						$item  = @$giftContent[$itemCart['gift_id']];
						if(!isset($item['title']) && !empty($item['title'])){
							$itemCart['title'] = $item['title'];
						}
					}
				}
				foreach ($data as &$item) {
					$item['cart'] = isset($carts[$item['cart_id']])?$carts[$item['cart_id']]:array();
				}
			}


			return $data;
		}
		static function history($tokens){
			if(empty($tokens)) return [];
			$sql = "SELECT cart_id as id,exchange_rate,point,0 as qty FROM " . T_LOYAL_ORDER ." WHERE site_user_id='{$tokens['site_user_id']}' AND status=".IS_ON." AND campaign_id={$tokens['campaign_id']} order by id desc";
			$loyalOrder = DB::fetch_all($sql);
			if(empty($loyalOrder)) return [];

			$cart_ids = array_column($loyalOrder,'id');
			$str_cart_ids = implode(',',$cart_ids);

			$sqlCart = "SELECT a.id,a.cart_id,a.created,a.delivery_required,a.delivery,b.title,b.isPhysical,b.gift_id,c.process,a.pay_status
					FROM ". T_CART_DETAIL ." a
					LEFT JOIN " . T_GIFT_DETAIL . " b on a.gift_detail_id=b.id
					LEFT JOIN " . T_DELIVERY_DETAIL . " c on a.id=c.cart_detail_id
					WHERE a.cart_id in($str_cart_ids)
					";
			$carts = DB::query($sqlCart)->fetchAll(PDO::FETCH_ASSOC);

			if(Language::$activeLang != "vi") {
				$giftIds = array_column($carts,'gift_id');
				$giftIds= array_filter(array_unique($giftIds));
				$content = self::giftContent(Language::$activeLang,$giftIds);
			}else{
				$content = [];
			}
			foreach ($carts as $cart) {
				$_title = $cart['title'];
				if(!empty($content)) {
					$item = @$content[$cart['gift_id']];
					if (isset( $item['title'] ) && !empty( $item['title'] )) {
						$_title = $item['title'];
					}
				}
				if(isset($loyalOrder[$cart['cart_id']])){
					$loyalOrder[$cart['cart_id']]['qty'] +=1;
					$loyalOrder[$cart['cart_id']]['pay_status'] = $cart['pay_status'];
					$loyalOrder[$cart['cart_id']]['process'] = $cart['process'];
					$loyalOrder[$cart['cart_id']]['created'] = $cart['created'];
					$loyalOrder[$cart['cart_id']]['title'] = $_title;
					$loyalOrder[$cart['cart_id']]['isPhysical'] = $cart['isPhysical'];
					$loyalOrder[$cart['cart_id']]['cart_detail_id'] = $cart['id'];
					$loyalOrder[$cart['cart_id']]['gift_id'] = $cart['gift_id'];
					$loyalOrder[$cart['cart_id']]['delivery_required'] = $cart['delivery_required'];
					$loyalOrder[$cart['cart_id']]['delivery'] = $cart['delivery'];
				}
			}
			return $loyalOrder;
		}
		static function point_format($point,$decimals = 2,$significance = 0.01,$dec_point = '.', $thousands_sep = ",")
		{
			#$ceil_number = ceil($point / $significance)*$significance;
			#$point_str1 = number_format($ceil_number,$decimals,$dec_point,$thousands_sep);
			#$point_str = rtrim($point_str1,"0");
			#if(substr($point_str,-1) == "."){
			#	return rtrim($point_str,".");
			#}
			$ceil_number = round($point,0);
			return $ceil_number;
		}
		//lưu thông tin đổi quà vật lý
		static function saveReceiver($form,$carts)
		{
			if(empty($form) || empty($carts)) return false;
			$phone = $form['phone'];
			$email = $form['email'];
			$city_id = $form['city_id'];
			$district_id = $form['district_id'];
			$ward_id = $form['ward_id'];
			$address = $form['address'];
			$full_name = $form['fullname'];
			$note = $form['note'];


			if ($phone == "" || !FunctionLib::is_phone( $phone )) {
				return false;
			}
			if ($email == "" || !FunctionLib::is_email( $email )) {
				return false;
			}
			if ($city_id == 0 || $district_id == 0 || $address == "") {
				return false;
			}


			$row = array(
				'city_id' => $city_id,
				'district_id' => $district_id,
				'address' => $address,
				'fullname' => $full_name,
				'phone' => $phone,
				'email' => $email,
				'ward' => $ward_id,
				'created' => TIME_NOW,
			);
			$checkReceiver = ReceiverCore::add( $row );
			$AddEntity = [
				'receiver_id' => $checkReceiver['id'],
				'city_id' => $city_id,
				'district_id' => $district_id,
				'ward_id' => $ward_id,
				'number' => $address,
				'status' => IS_ON
				//'notes' => $note,
			];

			$cartDetail = self::getCartDetailFromCart($carts['id']);
			foreach ($cartDetail as $cart) {
				$AddEntity['card_id'] = $cart['card_id'];
				if(is_numeric($ward_id) && $ward_id > 0) {
					$ward_id = DB::fetch( "SELECT title FROM " . T_WARD . " WHERE id= {$ward_id}", 'title' );
				}
				DB::update( T_CART_DETAIL,
					[
						'receiver_id' => $checkReceiver['id'],
						'delivery_note' => $note,
						'delivery_required' => 2,
						'client_time' => TIME_NOW,
						'delivery_city' => (int)$city_id,
						'delivery_district' => (int)$district_id,
						'delivery_ward' => $ward_id,
						'delivery_address' => $address,
						#'delivery' => 1,
						#'using_time' => TIME_NOW
					]
					,
					"id={$cart['id']}"
				);
			}
			$add = ReceiverCore::saveAddress($AddEntity,1);
			if(!isset($add['id'])) $add['id'] = 0;
			DB::update( T_CART,
				[
					'delivery_note' => $note,
					'email' => $email,
					'phone' => $phone,
					'address' => $address,
					'fullname' => $full_name,
					'receiver_id' => $checkReceiver['id'],
					'address_id' => $add['id']
				]
				,
				"id={$carts['id']}"
			);
			return true;
		}
		static function getCartDetailFromCart($cart_id = 0){
			if($cart_id <=0 || empty($cart_id)) return false;
			return DB::fetch_all("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id={$cart_id}");
		}
		static function giftContent($lang = 'en',$giftIds =[]){
			if(is_array($giftIds)) $giftIds = implode(',',$giftIds);
			$sql = "SELECT gift_id,title,note,content FROM ".T_GIFT_CONTENT." WHERE lang='$lang' AND gift_id in($giftIds) AND status = ".IS_ON . " ORDER BY id desc";
			$content = DB::fetch_all("SELECT * FROM ($sql) as t group by gift_id");
			$data = [];
			foreach ($content as $item) {
				$data[$item['gift_id']]  = $item;
			}
			return $data;
		}
		function sendEmailGiftPhysical($card ,$options =[]){
			if(empty($card) || empty($options)){
				System::apiError('Token không hợp lệ');
			}
			$app = Loyalty::getApp($card['site_id']);

			$carts = DB::fetch_all( "SELECT id,gift_id,cart_id,gift_detail_id FROM " . T_CART_DETAIL . " WHERE cart_id=" . $options['cart_id'] );
			if (empty( $carts )) {
				System::apiError( 'Có lỗi xảy ra vui lòng liên hệ ban quản trị.' );
			}
			if ($carts) {
				foreach ($carts as $cart) {
					#Chi Dulux moi gui mail
					$gift = Loyalty::giftFromCart( $cart['id'] );

					$content = '<h3>Thông tin khách hàng đổi quà ' . $app['title'] . ':</h3><br/><br/>
						<table cellpadding="1" cellspacing="1" width="100%">
						<tr><td>Họ tên:</td><td>' . $options['fullname'] . '</td>
						<tr><td>Số điện thoại:</td><td>' . $options['phone'] . '</td>
						<tr><td>Đơn hàng:</td><td>' . $cart['cart_id'] . '</td>
						<tr><td>Đơn hàng con:</td><td>' . $cart['id'] . '</td>
						<tr><td>Tên quà:</td><td>' . $gift['title'] . '</td>
						<tr><td>Tỉnh thành:</td><td>' . DB::fetch( "SELECT * FROM ___province WHERE id=" . $options['city'], 'title' ) . '</td>
						<tr><td>Quận huyện:</td><td>' . DB::fetch( "SELECT * FROM " . T_DISTRICT . " WHERE id=" . $options['district_id'], 'title' ) . '</td>
						<tr><td>Địa chỉ:</td><td>' . $options['address'] . '</td>
						<tr><td>Ghi chú thêm:</td><td>' . $options['note'] . '</td>
						<tr><td>Số lương:</td><td>1</td></tr>
						<tr><td>Thời gian đổi:</td><td>' . FunctionLib::dateFormat( TIME_NOW, 'd/m/Y H:i:s' ) . '</td></tr>

						</table>';

					$rowInsertEmail = array(
						'subject' => 'Khách hàng đổi quà thành công',
						'email' => '<EMAIL>',
						'content' => $content,
						'time_send' => 0,
						'created' => TIME_NOW,
					);
					DB::insert( T_EMAIL_SEND, $rowInsertEmail );
				}
			}
		}

		static function currentCat($id,$root_id){
			$cat = DB::fetch("SELECT id,title,parent_id from ". T_CATEGORY . " where id=$id");
			if($cat['parent_id'] == $root_id || $cat['parent_id'] == 0 || $cat['parent_id'] == $cat['id']) return $cat;
			return self::currentCat($cat['parent_id'],$root_id);
		}
		static function errorCode($code = "",$app_id=0){
			$codes = CacheLib::get('urpage_status_code'.$app_id);
			if(empty($codes)){
				$codes = DB::fetch_all("SELECT * FROM " . T_STATUS_CODE . " WHERE status = 2 and app_id=$app_id");
			}
			$lang = CookieLib::get('super_lang');
			foreach ($codes as $item) {
				if($item["code"] == $code){
					 return [
					 	'title' => $lang == 'vi' ? $item['title']: $item['title_en'],
						'code' => $item['code'],
						'id' => $item['id']
					 ];
				}
			}
			return [
				'id' => 2,
				'code' => 'U1',
				'raw' =>$code,
				'title' => $lang == 'vi' ?"Đã xảy ra lỗi hệ thống. Vui lòng liên hệ hotline UrBox ". UB_CUSTOMER_PHONE ." hoặc email tới <EMAIL> để được hỗ trợ (8h-22h, giờ Việt Nam, bao gồm Lễ Tết).":"System error occurred. Please contact UrBox at ". UB_CUSTOMER_PHONE ." or send an <NAME_EMAIL> for support (8am - 10pm, GMT +7, including Holidays).",
			];
		}
		public static function giftPrice($app_id,$giftIds){
			if(empty($app_id)) return false;
			if(!is_array($giftIds)) {
				$giftIds = (int)$giftIds;
				$query = DB::fetch( "SELECT * FROM " . T_GIFT_QUANTITY . " WHERE app_id={$app_id} and gift_detail_id={$giftIds} and status=".IS_ON." order by id DESC" );
			}else{
				$giftIds = array_unique($giftIds);
				$giftIds = implode(',',$giftIds);
				$query = DB::fetch_ALL( "SELECT * FROM " . T_GIFT_QUANTITY . " WHERE app_id={$app_id} and status=".IS_ON." and gift_detail_id in ({$giftIds})  order by id DESC" );
			}
			return $query;
		}
        static function getBrandOfficeApply($gift_id = 0){
            if($gift_id <=0) return false;
            // $gift = DB::fetch("SELECT id,approved_store FROM " . T_GIFT. " WHERE id='$gift_id'");
            //if(empty($gift) || $gift['approved_store'] == 1) return false;
            $gift_brand_office = DB::query("SELECT distinct(brand_office_id) as id FROM ". T_GIFT_BRAND_OFFICE . " WHERE gift_id='$gift_id'")->fetchAll(PDO::FETCH_COLUMN);
            return empty($gift_brand_office)? false : implode(',',$gift_brand_office);
        }
        static function writeSentryLog($e,$user = []){
            try {
                $client = (new Raven_Client([
                    'dsn' => DSN_SENTRY_LOYALTY,
                    'verify_ssl' => false
                ]))->install();
                $client->user_context($user);
                $client->captureException($e);
            }catch(Exception $e){

            }
        }
        static function writeSentryMessage($message,$data,$user = []){
            try {
                $client = (new Raven_Client([
                    'dsn' => DSN_SENTRY_LOYALTY,
                    'verify_ssl' => false
                ]))->install();
                $client->user_context($user);
                $client->captureMessage($message,$data);
            }catch(Exception $e){

            }
        }
	}