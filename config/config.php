<?php
//set time zone
date_default_timezone_set('Asia/Bangkok');
define('TIME_NOW', time());

//path & doamin config
const DOMAIN_NAME = 'page.urbox.vn';
const DOMAIN_COOKIE_STRING = '.' . DOMAIN_NAME;
define('DOMAIN', $_SERVER['SERVER_NAME']);

if (strpos($_SERVER['SERVER_NAME'], DOMAIN_NAME) !== false) $http = 'https://';
else $http = 'https://';

define('ROOT_PATH', str_replace(array('config/'), array(''), strtr(dirname(__FILE__) . "/", array('\\' => '/'))));
$webroot = str_replace('\\', '/', $http . $_SERVER['HTTP_HOST'] . (dirname($_SERVER['SCRIPT_NAME']) ? dirname($_SERVER['SCRIPT_NAME']) : ''));
$webroot .= $webroot[strlen($webroot) - 1] != '/' ? '/' : '';

define('WEB_ROOT', $webroot);
unset($http);
unset($webroot);

const COOKIE_ID = 'urp';
const TOKEN_NAME = '__token';

define('TOKEN_SECRET', md5("@" . DOMAIN_NAME));

if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    define('AJAX', 1);
} else {
    define('AJAX', 0);
}

error_reporting(0);
//debug enable
const DEBUG = 0;
if (DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

const LINK_API_URBOX = 'http://api.urbox.internal/';
const AGENT_SITE = '6';
const ACCESS_URBOX = '********************************';
const LINK_URBOX = 'https://urbox.vn/';
const LINK_URBOX_INTERNAL = 'https://home.urbox.internal/';
const DOMAIN_SHORTEN = 'https://s.urbox.vn/';
const APP_GRAPREWARD = 26;
const PAGE_NAME = "PAGE_URBOX";
const MEMCACHE_ID = 'page'; //Dung lam ID phan biet session giua cac site mini shop
const CARD_URI = 'https://card.urbox.vn/';
//include other config
require_once('config.db.php');
require_once('config.table.php');
require_once('config.s3.php');
require_once('config.const.php');
require_once('config.card.php');
require_once('config.loyal.php');